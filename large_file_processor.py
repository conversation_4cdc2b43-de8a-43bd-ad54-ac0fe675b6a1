#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超大文件SQL处理器
专门处理GB级别的SQL文件，提供实时进度监控
"""

import os
import sys
import time
import threading
from sql_database_creator import SQLDatabaseCreator, DatabaseConfig, load_config_from_file


class ProgressMonitor:
    """进度监控器"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.file_size = os.path.getsize(file_path)
        self.bytes_read = 0
        self.is_reading = False
        self.start_time = None
        
    def start_monitoring(self):
        """开始监控"""
        self.is_reading = True
        self.start_time = time.time()
        monitor_thread = threading.Thread(target=self._monitor_progress)
        monitor_thread.daemon = True
        monitor_thread.start()
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_reading = False
        
    def update_progress(self, bytes_read):
        """更新进度"""
        self.bytes_read = bytes_read
        
    def _monitor_progress(self):
        """监控进度线程"""
        while self.is_reading:
            if self.bytes_read > 0:
                progress = (self.bytes_read / self.file_size) * 100
                elapsed = time.time() - self.start_time
                speed_mb = (self.bytes_read / (1024 * 1024)) / elapsed if elapsed > 0 else 0
                
                print(f"\r文件读取进度: {progress:.1f}% "
                      f"({self.bytes_read / (1024*1024):.1f}MB/"
                      f"{self.file_size / (1024*1024):.1f}MB) "
                      f"速度: {speed_mb:.1f}MB/s", end="", flush=True)
            time.sleep(1)


class LargeFileSQLProcessor:
    """大文件SQL处理器"""
    
    def __init__(self, config):
        self.config = config
        self.creator = SQLDatabaseCreator(config, 'INFO')
        
    def read_large_file_with_progress(self, file_path):
        """带进度监控的文件读取"""
        print(f"\n开始读取大文件: {file_path}")
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"文件大小: {file_size_mb:.1f}MB")
        
        monitor = ProgressMonitor(file_path)
        monitor.start_monitoring()
        
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            content = None
            
            for encoding in encodings:
                try:
                    print(f"\n尝试使用 {encoding} 编码读取文件...")
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = ""
                        chunk_size = 1024 * 1024  # 1MB chunks
                        bytes_read = 0
                        
                        while True:
                            chunk = f.read(chunk_size)
                            if not chunk:
                                break
                            content += chunk
                            bytes_read += len(chunk.encode(encoding))
                            monitor.update_progress(bytes_read)
                    
                    print(f"\n✓ 文件读取完成，使用编码: {encoding}")
                    break
                    
                except UnicodeDecodeError:
                    print(f"✗ {encoding} 编码失败，尝试下一个...")
                    continue
                except Exception as e:
                    print(f"✗ 读取失败: {e}")
                    continue
            
            monitor.stop_monitoring()
            
            if content is None:
                print("✗ 所有编码都失败了")
                return None
                
            return content.strip()
            
        except Exception as e:
            monitor.stop_monitoring()
            print(f"\n✗ 文件读取出错: {e}")
            return None
    
    def split_sql_with_progress(self, sql_content):
        """带进度的SQL分割"""
        print("\n开始分割SQL语句...")
        content_size = len(sql_content)
        print(f"内容大小: {content_size / (1024*1024):.1f}MB")
        
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        processed_chars = 0
        last_progress_time = time.time()
        
        for i, char in enumerate(sql_content):
            processed_chars += 1
            
            # 每处理10MB显示一次进度
            if processed_chars % (10 * 1024 * 1024) == 0:
                progress = (i / content_size) * 100
                current_time = time.time()
                if current_time - last_progress_time >= 2:  # 每2秒更新一次
                    print(f"SQL分割进度: {progress:.1f}%")
                    last_progress_time = current_time
            
            if not in_string:
                if char in ("'", '"'):
                    in_string = True
                    string_char = char
                elif char == ';':
                    if current_statement.strip():
                        statements.append(current_statement.strip())
                    current_statement = ""
                    continue
            else:
                if char == string_char:
                    if i + 1 < len(sql_content) and sql_content[i + 1] == string_char:
                        current_statement += char + sql_content[i + 1]
                        i += 1
                        continue
                    else:
                        in_string = False
                        string_char = None
            
            current_statement += char
        
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        print(f"✓ SQL分割完成，共 {len(statements)} 个语句")
        return statements
    
    def process_large_file(self, file_path, batch_size=5):
        """处理大文件"""
        print(f"\n{'='*60}")
        print(f"开始处理超大文件: {file_path}")
        print(f"{'='*60}")
        
        # 生成数据库名
        filename = os.path.basename(file_path)
        database_name = self.creator.sanitize_database_name(filename)
        
        # 连接到服务器
        if not self.creator.connect_to_server():
            print("✗ 无法连接到数据库服务器")
            return False
        
        # 创建数据库
        print(f"\n创建数据库: {database_name}")
        if not self.creator.create_database(database_name, drop_if_exists=True):
            print(f"✗ 创建数据库失败: {database_name}")
            return False
        
        # 连接到数据库
        print(f"连接到数据库: {database_name}")
        if not self.creator.connect_to_database(database_name):
            print(f"✗ 连接数据库失败: {database_name}")
            return False
        
        # 读取文件
        sql_content = self.read_large_file_with_progress(file_path)
        if not sql_content:
            print("✗ 文件读取失败")
            return False
        
        # 分割SQL语句
        statements = self.split_sql_with_progress(sql_content)
        if not statements:
            print("✗ 没有找到有效的SQL语句")
            return False
        
        # 执行SQL语句
        print(f"\n开始执行 {len(statements)} 个SQL语句...")
        print(f"批次大小: {batch_size}")
        
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        for i, statement in enumerate(statements, 1):
            if not statement.strip():
                continue
            
            try:
                # 显示进度
                if i % max(1, len(statements) // 50) == 0 or i == len(statements):
                    progress = (i / len(statements)) * 100
                    elapsed = time.time() - start_time
                    speed = i / elapsed if elapsed > 0 else 0
                    eta = (len(statements) - i) / speed if speed > 0 else 0
                    
                    print(f"执行进度: {i}/{len(statements)} ({progress:.1f}%) "
                          f"速度: {speed:.1f}语句/秒 预计剩余: {eta/60:.1f}分钟")
                
                # 执行语句
                self.creator.cursor.execute(statement)
                success_count += 1
                
                # 批量提交
                if i % batch_size == 0:
                    self.creator.connection.commit()
                    print(f"  → 批量提交: {i} 个语句已执行")
                
            except Exception as e:
                error_count += 1
                print(f"✗ 语句 {i} 执行失败: {str(e)[:100]}...")
                
                # 如果错误太多，询问是否继续
                if error_count > 10:
                    choice = input(f"\n已有 {error_count} 个错误，是否继续? (y/n): ")
                    if choice.lower() != 'y':
                        break
        
        # 最终提交
        try:
            self.creator.connection.commit()
            print(f"\n✓ 最终提交完成")
        except Exception as e:
            print(f"✗ 最终提交失败: {e}")
        
        # 统计结果
        total_time = time.time() - start_time
        print(f"\n{'='*60}")
        print(f"处理完成: {file_path}")
        print(f"数据库: {database_name}")
        print(f"总语句数: {len(statements)}")
        print(f"成功执行: {success_count}")
        print(f"执行失败: {error_count}")
        print(f"总耗时: {total_time/60:.1f}分钟")
        print(f"平均速度: {success_count/total_time:.1f}语句/秒")
        print(f"{'='*60}")
        
        return error_count == 0


def main():
    """主函数"""
    print("超大文件SQL处理器")
    print("=" * 50)
    
    # 显示当前目录的大文件
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql')]
    large_files = []
    
    print("\n大文件列表 (>10MB):")
    for i, file in enumerate(sql_files, 1):
        size_mb = os.path.getsize(file) / (1024 * 1024)
        if size_mb > 10:
            large_files.append(file)
            print(f"{len(large_files):2d}. {file} ({size_mb:.1f}MB)")
    
    if not large_files:
        print("没有找到大文件")
        return
    
    print(f"\n请选择要处理的文件 (1-{len(large_files)}):")
    try:
        choice = int(input("选择: ")) - 1
        if 0 <= choice < len(large_files):
            selected_file = large_files[choice]
        else:
            print("无效选择")
            return
    except ValueError:
        print("请输入数字")
        return
    
    print(f"\n选择的文件: {selected_file}")
    size_mb = os.path.getsize(selected_file) / (1024 * 1024)
    
    if size_mb > 500:
        print(f"⚠️  警告: 这是一个超大文件 ({size_mb:.1f}MB)")
        print("处理可能需要很长时间，建议:")
        print("1. 确保有足够的磁盘空间")
        print("2. 确保网络连接稳定")
        print("3. 不要关闭程序")
        
        confirm = input("\n确认处理? (y/n): ")
        if confirm.lower() != 'y':
            return
    
    # 询问批次大小
    print(f"\n建议的批次大小:")
    if size_mb < 100:
        suggested_batch = 20
    elif size_mb < 500:
        suggested_batch = 10
    else:
        suggested_batch = 5
    
    print(f"推荐: {suggested_batch}")
    batch_input = input(f"请输入批次大小 (直接回车使用推荐值): ").strip()
    batch_size = int(batch_input) if batch_input else suggested_batch
    
    # 加载配置
    try:
        config = load_config_from_file('db_config.ini')
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    # 开始处理
    processor = LargeFileSQLProcessor(config)
    
    try:
        success = processor.process_large_file(selected_file, batch_size)
        if success:
            print("\n🎉 文件处理成功!")
        else:
            print("\n❌ 文件处理失败!")
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断了处理过程")
    except Exception as e:
        print(f"\n❌ 处理出错: {e}")
    finally:
        processor.creator.disconnect()


if __name__ == '__main__':
    main()
