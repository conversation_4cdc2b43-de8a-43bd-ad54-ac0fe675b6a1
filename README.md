# SQL批量导入工具

这是一个功能强大的Python工具，用于批量导入SQL文件到各种数据库中。

## 功能特性

- 🗄️ **多数据库支持**: MySQL、PostgreSQL、SQL Server、SQLite
- 📁 **批量处理**: 支持通配符模式批量导入多个SQL文件
- 🔄 **事务管理**: 每个文件作为一个事务，支持回滚
- 📝 **详细日志**: 完整的执行日志和错误报告
- ⚙️ **灵活配置**: 支持配置文件和命令行参数
- 🎯 **文件排序**: 支持指定文件执行顺序
- 🛡️ **错误处理**: 可选择遇到错误时继续或停止执行
- 📊 **执行报告**: 详细的执行结果统计

## 安装依赖

根据您使用的数据库类型安装相应的Python库：

```bash
# MySQL
pip install pymysql

# PostgreSQL
pip install psycopg2-binary

# SQL Server
pip install pyodbc

# SQLite (Python内置，无需安装)
```

## 快速开始

### 1. 创建配置文件

```bash
python sql_batch_importer.py --create-config
```

### 2. 编辑配置文件

编辑生成的 `db_config.ini` 文件，填入您的数据库连接信息：

```ini
[database]
type = mysql
host = localhost
port = 3306
database = your_database_name
username = your_username
password = your_password
charset = utf8mb4
```

### 3. 执行批量导入

```bash
# 导入当前目录下所有.sql文件
python sql_batch_importer.py

# 导入指定文件
python sql_batch_importer.py file1.sql file2.sql

# 使用通配符
python sql_batch_importer.py *.sql data/*.sql
```

## 使用方法

### 基本用法

```bash
# 导入所有SQL文件
python sql_batch_importer.py

# 导入指定文件
python sql_batch_importer.py boost.sql bpm-engine.sql

# 使用通配符导入
python sql_batch_importer.py bpm-*.sql
```

### 高级选项

```bash
# 指定配置文件
python sql_batch_importer.py -c my_config.ini *.sql

# 指定文件执行顺序
python sql_batch_importer.py --order "boost.sql,bpm-engine.sql,bpm-form.sql" *.sql

# 遇到错误时停止执行
python sql_batch_importer.py --stop-on-error *.sql

# 设置日志级别
python sql_batch_importer.py --log-level DEBUG *.sql

# 使用命令行参数指定数据库连接
python sql_batch_importer.py --db-type mysql --host localhost --database mydb --username user --password pass *.sql
```

### 命令行参数

- `files`: 要导入的SQL文件路径或通配符模式
- `-c, --config`: 数据库配置文件路径 (默认: db_config.ini)
- `--create-config`: 创建示例配置文件
- `--continue-on-error`: 遇到错误时继续执行 (默认)
- `--stop-on-error`: 遇到错误时停止执行
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `--order`: 指定文件执行顺序，用逗号分隔文件名
- `--db-type`: 数据库类型 (mysql, postgresql, sqlserver, sqlite)
- `--host`: 数据库主机
- `--port`: 数据库端口
- `--database`: 数据库名称
- `--username`: 用户名
- `--password`: 密码

## 配置文件格式

### MySQL配置示例

```ini
[database]
type = mysql
host = localhost
port = 3306
database = mydb
username = root
password = password
charset = utf8mb4
```

### PostgreSQL配置示例

```ini
[database]
type = postgresql
host = localhost
port = 5432
database = mydb
username = postgres
password = password
```

### SQL Server配置示例

```ini
[database]
type = sqlserver
host = localhost
port = 1433
database = mydb
username = sa
password = password

# 或者使用连接字符串
# connection_string = DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost,1433;DATABASE=mydb;UID=sa;PWD=password
```

### SQLite配置示例

```ini
[database]
type = sqlite
database = /path/to/database.db
```

## 输出示例

```
2024-01-15 10:30:15 - INFO - 成功连接到mysql数据库
2024-01-15 10:30:15 - INFO - 找到 15 个SQL文件，开始批量导入
2024-01-15 10:30:15 - INFO - 开始导入文件: boost.sql
2024-01-15 10:30:16 - INFO - 文件导入完成: boost.sql (成功: 25, 失败: 0)
...

============================================================
SQL批量导入结果汇总
============================================================
总文件数: 15
成功文件数: 14
失败文件数: 1
总SQL语句数: 1250
成功执行: 1240
执行失败: 10
成功率: 99.20%

详细结果:
------------------------------------------------------------
✓ boost.sql (25/25)
✓ bpm-engine.sql (150/150)
✗ bpm-form.sql (80/85)
    错误: 语句 82: Table 'form_data' already exists
...
```

## 注意事项

1. **备份数据**: 在执行批量导入前，请确保已备份重要数据
2. **权限检查**: 确保数据库用户有足够的权限执行SQL语句
3. **文件编码**: 工具会自动尝试多种编码格式读取SQL文件
4. **事务处理**: 每个文件作为一个独立事务，失败时会回滚该文件的所有更改
5. **大文件处理**: 对于特别大的SQL文件，建议分割后再导入

## 故障排除

### 常见错误

1. **连接失败**: 检查数据库服务是否运行，连接信息是否正确
2. **权限不足**: 确保数据库用户有CREATE、INSERT、UPDATE、DELETE权限
3. **编码问题**: 如果文件读取失败，尝试转换文件编码为UTF-8
4. **语法错误**: 检查SQL文件中的语法是否正确

### 调试技巧

```bash
# 启用详细日志
python sql_batch_importer.py --log-level DEBUG *.sql

# 测试单个文件
python sql_batch_importer.py single_file.sql

# 遇到错误立即停止
python sql_batch_importer.py --stop-on-error *.sql
```
