#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL数据库批量创建和导入工具
根据SQL文件名自动创建对应的数据库，并导入SQL内容
"""

import os
import sys
import glob
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import configparser
import re

# 数据库连接库
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False


class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self, db_type: str, **kwargs):
        self.db_type = db_type.lower()
        self.host = kwargs.get('host', 'localhost')
        self.port = kwargs.get('port')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.charset = kwargs.get('charset', 'utf8mb4')
        
        # 设置默认端口
        if not self.port:
            if self.db_type == 'mysql':
                self.port = 3306
            elif self.db_type == 'postgresql':
                self.port = 5432


class SQLDatabaseCreator:
    """SQL数据库批量创建器"""
    
    def __init__(self, config: DatabaseConfig, log_level: str = 'INFO'):
        self.config = config
        self.connection = None
        self.cursor = None
        
        # 设置日志
        self.setup_logging(log_level)
        
    def setup_logging(self, log_level: str):
        """设置日志配置"""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(f'db_creation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def connect_to_server(self) -> bool:
        """连接到数据库服务器（不指定具体数据库）"""
        try:
            if self.config.db_type == 'mysql':
                if not MYSQL_AVAILABLE:
                    raise ImportError("pymysql库未安装，请运行: pip install pymysql")
                
                self.connection = pymysql.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    charset=self.config.charset,
                    autocommit=True  # 创建数据库需要自动提交
                )
                
            elif self.config.db_type == 'postgresql':
                if not POSTGRESQL_AVAILABLE:
                    raise ImportError("psycopg2库未安装，请运行: pip install psycopg2-binary")
                
                # PostgreSQL连接到默认的postgres数据库
                self.connection = psycopg2.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database='postgres'  # 连接到默认数据库
                )
                self.connection.autocommit = True
                
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
            
            self.cursor = self.connection.cursor()
            self.logger.info(f"成功连接到{self.config.db_type}服务器")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库服务器连接失败: {str(e)}")
            return False
    
    def connect_to_database(self, database_name: str) -> bool:
        """连接到指定数据库"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if self.connection:
                    try:
                        self.connection.close()
                    except:
                        pass

                if self.config.db_type == 'mysql':
                    self.connection = pymysql.connect(
                        host=self.config.host,
                        port=self.config.port,
                        user=self.config.username,
                        password=self.config.password,
                        database=database_name,
                        charset=self.config.charset,
                        autocommit=False,
                        connect_timeout=10,
                        read_timeout=30,
                        write_timeout=30
                    )

                elif self.config.db_type == 'postgresql':
                    self.connection = psycopg2.connect(
                        host=self.config.host,
                        port=self.config.port,
                        user=self.config.username,
                        password=self.config.password,
                        database=database_name,
                        connect_timeout=10
                    )
                    self.connection.autocommit = False

                self.cursor = self.connection.cursor()
                self.logger.info(f"成功连接到数据库: {database_name}")
                return True

            except Exception as e:
                self.logger.warning(f"连接数据库 {database_name} 失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(1)  # 等待1秒后重试
                else:
                    self.logger.error(f"连接数据库 {database_name} 最终失败")
                    return False
    
    def disconnect(self):
        """断开数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            self.logger.info("数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接时出错: {str(e)}")
    
    def sanitize_database_name(self, filename: str) -> str:
        """清理文件名，生成有效的数据库名"""
        # 移除.sql扩展名
        db_name = os.path.splitext(filename)[0]
        
        # 替换特殊字符为下划线
        db_name = re.sub(r'[^a-zA-Z0-9_]', '_', db_name)
        
        # 确保以字母或下划线开头
        if db_name and not db_name[0].isalpha() and db_name[0] != '_':
            db_name = '_' + db_name
        
        # 限制长度（MySQL数据库名最大64字符）
        if len(db_name) > 60:
            db_name = db_name[:60]
        
        return db_name
    
    def database_exists(self, database_name: str) -> bool:
        """检查数据库是否存在"""
        try:
            if self.config.db_type == 'mysql':
                self.cursor.execute("SHOW DATABASES LIKE %s", (database_name,))
                return self.cursor.fetchone() is not None
            elif self.config.db_type == 'postgresql':
                self.cursor.execute(
                    "SELECT 1 FROM pg_database WHERE datname = %s", 
                    (database_name,)
                )
                return self.cursor.fetchone() is not None
        except Exception as e:
            self.logger.error(f"检查数据库 {database_name} 是否存在时出错: {str(e)}")
            return False
    
    def create_database(self, database_name: str, drop_if_exists: bool = False) -> bool:
        """创建数据库"""
        try:
            # 检查数据库是否已存在
            if self.database_exists(database_name):
                if drop_if_exists:
                    self.logger.info(f"数据库 {database_name} 已存在，正在删除...")
                    if not self.drop_database(database_name):
                        return False
                else:
                    self.logger.warning(f"数据库 {database_name} 已存在，跳过创建")
                    return True
            
            # 创建数据库
            if self.config.db_type == 'mysql':
                sql = f"CREATE DATABASE `{database_name}` CHARACTER SET {self.config.charset} COLLATE {self.config.charset}_general_ci"
                self.cursor.execute(sql)
            elif self.config.db_type == 'postgresql':
                # PostgreSQL不支持在事务中创建数据库，需要autocommit
                sql = f'CREATE DATABASE "{database_name}"'
                self.cursor.execute(sql)
            
            self.logger.info(f"成功创建数据库: {database_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据库 {database_name} 失败: {str(e)}")
            return False
    
    def drop_database(self, database_name: str) -> bool:
        """删除数据库"""
        try:
            if self.config.db_type == 'mysql':
                self.cursor.execute(f"DROP DATABASE IF EXISTS `{database_name}`")
            elif self.config.db_type == 'postgresql':
                self.cursor.execute(f'DROP DATABASE IF EXISTS "{database_name}"')
            
            self.logger.info(f"成功删除数据库: {database_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除数据库 {database_name} 失败: {str(e)}")
            return False

    def read_sql_file(self, file_path: str) -> Optional[str]:
        """读取SQL文件内容"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        if content:
                            self.logger.debug(f"使用{encoding}编码成功读取文件: {file_path}")
                            return content
                except UnicodeDecodeError:
                    continue

            self.logger.error(f"无法读取文件 {file_path}，尝试了所有编码都失败")
            return None

        except Exception as e:
            self.logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
            return None

    def split_sql_statements(self, sql_content: str) -> List[str]:
        """分割SQL语句"""
        statements = []
        current_statement = ""
        in_string = False
        string_char = None

        i = 0
        while i < len(sql_content):
            char = sql_content[i]

            if not in_string:
                if char in ("'", '"'):
                    in_string = True
                    string_char = char
                elif char == ';':
                    if current_statement.strip():
                        statements.append(current_statement.strip())
                    current_statement = ""
                    i += 1
                    continue
            else:
                if char == string_char:
                    # 检查是否是转义的引号
                    if i + 1 < len(sql_content) and sql_content[i + 1] == string_char:
                        current_statement += char + sql_content[i + 1]
                        i += 2
                        continue
                    else:
                        in_string = False
                        string_char = None

            current_statement += char
            i += 1

        # 添加最后一个语句
        if current_statement.strip():
            statements.append(current_statement.strip())

        return statements

    def import_sql_to_database(self, sql_file: str, database_name: str, batch_size: int = 100) -> Dict:
        """将SQL文件导入到指定数据库"""
        result = {
            'file': sql_file,
            'database': database_name,
            'success': False,
            'total_statements': 0,
            'executed_statements': 0,
            'failed_statements': 0,
            'errors': []
        }

        self.logger.info(f"开始导入 {sql_file} 到数据库 {database_name}")

        # 连接到目标数据库
        if not self.connect_to_database(database_name):
            result['errors'].append(f"无法连接到数据库 {database_name}")
            return result

        # 读取SQL文件
        sql_content = self.read_sql_file(sql_file)
        if not sql_content:
            result['errors'].append("无法读取SQL文件内容")
            return result

        # 分割SQL语句
        statements = self.split_sql_statements(sql_content)
        result['total_statements'] = len(statements)

        if not statements:
            result['errors'].append("文件中没有找到有效的SQL语句")
            return result

        # 执行SQL语句
        try:
            total_statements = len(statements)
            progress_interval = max(1, total_statements // 20)  # 每5%显示一次进度

            for i, statement in enumerate(statements, 1):
                if not statement.strip():
                    continue

                try:
                    # 显示进度
                    if i % progress_interval == 0 or i == total_statements:
                        progress = (i / total_statements) * 100
                        self.logger.info(f"执行进度: {i}/{total_statements} ({progress:.1f}%)")

                    # 设置语句执行超时
                    if hasattr(self.connection, 'ping'):
                        try:
                            self.connection.ping(reconnect=True)
                        except:
                            pass

                    self.cursor.execute(statement)
                    result['executed_statements'] += 1

                    # 对于大文件，每batch_size个语句提交一次，避免长时间锁定
                    if i % batch_size == 0:
                        self.connection.commit()
                        self.logger.debug(f"中间提交: {i} 个语句已执行")

                except Exception as e:
                    result['failed_statements'] += 1
                    error_msg = f"语句 {i} 执行失败: {str(e)}"
                    result['errors'].append(error_msg)
                    self.logger.error(error_msg)

                    # 如果是超时错误，尝试重新连接
                    if "timeout" in str(e).lower() or "lost connection" in str(e).lower():
                        self.logger.warning("检测到连接问题，尝试重新连接...")
                        if self.connect_to_database(database_name):
                            self.logger.info("重新连接成功，继续执行")
                        else:
                            self.logger.error("重新连接失败，停止执行")
                            break

            # 提交事务
            self.connection.commit()
            result['success'] = result['failed_statements'] == 0

            self.logger.info(f"文件 {sql_file} 导入完成到数据库 {database_name} "
                           f"(成功: {result['executed_statements']}, "
                           f"失败: {result['failed_statements']})")

        except Exception as e:
            self.connection.rollback()
            error_msg = f"事务执行失败: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg)

        return result

    def get_file_line_count(self, file_path: str) -> int:
        """获取文件行数"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return sum(1 for _ in f)
        except:
            return 0

    def process_sql_files(self, file_patterns: List[str],
                         drop_existing: bool = False,
                         continue_on_error: bool = True,
                         skip_large_files: bool = False,
                         large_file_threshold: int = 1000,
                         batch_size: int = 100) -> Dict:
        """批量处理SQL文件：创建数据库并导入"""
        all_results = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'created_databases': [],
            'failed_databases': [],
            'file_results': [],
            'summary': {}
        }

        # 收集所有SQL文件
        all_files = []
        for pattern in file_patterns:
            matched_files = glob.glob(pattern)
            all_files.extend(matched_files)

        # 去重并排序
        all_files = list(set(all_files))
        all_files.sort()

        all_results['total_files'] = len(all_files)

        if not all_files:
            self.logger.warning("没有找到匹配的SQL文件")
            return all_results

        self.logger.info(f"找到 {len(all_files)} 个SQL文件，开始批量处理")

        # 首先连接到数据库服务器
        if not self.connect_to_server():
            self.logger.error("无法连接到数据库服务器")
            return all_results

        # 处理每个SQL文件
        for sql_file in all_files:
            if not os.path.exists(sql_file):
                self.logger.warning(f"文件不存在: {sql_file}")
                continue

            # 检查文件大小
            line_count = self.get_file_line_count(sql_file)
            file_size_mb = os.path.getsize(sql_file) / (1024 * 1024)

            # 生成数据库名
            filename = os.path.basename(sql_file)
            database_name = self.sanitize_database_name(filename)

            self.logger.info(f"处理文件: {sql_file} -> 数据库: {database_name} "
                           f"({line_count} 行, {file_size_mb:.1f}MB)")

            # 检查是否跳过大文件
            if skip_large_files and line_count > large_file_threshold:
                self.logger.warning(f"跳过大文件: {sql_file} ({line_count} 行超过阈值 {large_file_threshold})")
                continue

            # 创建数据库
            if self.create_database(database_name, drop_existing):
                all_results['created_databases'].append(database_name)

                # 导入SQL文件
                result = self.import_sql_to_database(sql_file, database_name, batch_size)
                all_results['file_results'].append(result)

                if result['success']:
                    all_results['successful_files'] += 1
                    self.logger.info(f"成功处理: {sql_file} -> {database_name}")
                else:
                    all_results['failed_files'] += 1
                    self.logger.error(f"导入失败: {sql_file} -> {database_name}")

                    if not continue_on_error:
                        self.logger.error("遇到错误，停止批量处理")
                        break
            else:
                all_results['failed_databases'].append(database_name)
                all_results['failed_files'] += 1

                # 添加失败记录
                result = {
                    'file': sql_file,
                    'database': database_name,
                    'success': False,
                    'total_statements': 0,
                    'executed_statements': 0,
                    'failed_statements': 0,
                    'errors': [f"无法创建数据库 {database_name}"]
                }
                all_results['file_results'].append(result)

                if not continue_on_error:
                    self.logger.error("遇到错误，停止批量处理")
                    break

        # 生成汇总信息
        total_statements = sum(r['total_statements'] for r in all_results['file_results'])
        executed_statements = sum(r['executed_statements'] for r in all_results['file_results'])
        failed_statements = sum(r['failed_statements'] for r in all_results['file_results'])

        all_results['summary'] = {
            'total_statements': total_statements,
            'executed_statements': executed_statements,
            'failed_statements': failed_statements,
            'success_rate': f"{(executed_statements/total_statements*100):.2f}%" if total_statements > 0 else "0%",
            'created_databases_count': len(all_results['created_databases']),
            'failed_databases_count': len(all_results['failed_databases'])
        }

        self.logger.info(f"批量处理完成 - 文件: {all_results['successful_files']}/{all_results['total_files']} "
                        f"数据库: {len(all_results['created_databases'])} 个已创建")

        return all_results


def load_config_from_file(config_file: str) -> DatabaseConfig:
    """从配置文件加载数据库配置"""
    config = configparser.ConfigParser()
    config.read(config_file, encoding='utf-8')

    if 'database' not in config:
        raise ValueError("配置文件中缺少 [database] 部分")

    db_section = config['database']

    return DatabaseConfig(
        db_type=db_section.get('type', 'mysql'),
        host=db_section.get('host', 'localhost'),
        port=db_section.getint('port', None),
        username=db_section.get('username'),
        password=db_section.get('password'),
        charset=db_section.get('charset', 'utf8mb4')
    )


def print_results(results: Dict):
    """打印处理结果"""
    print("\n" + "="*70)
    print("SQL数据库批量创建和导入结果汇总")
    print("="*70)

    print(f"总文件数: {results['total_files']}")
    print(f"成功处理文件数: {results['successful_files']}")
    print(f"失败文件数: {results['failed_files']}")
    print(f"成功创建数据库数: {results['summary'].get('created_databases_count', 0)}")
    print(f"创建失败数据库数: {results['summary'].get('failed_databases_count', 0)}")

    if 'summary' in results and results['summary']:
        summary = results['summary']
        print(f"总SQL语句数: {summary.get('total_statements', 0)}")
        print(f"成功执行: {summary.get('executed_statements', 0)}")
        print(f"执行失败: {summary.get('failed_statements', 0)}")
        print(f"成功率: {summary.get('success_rate', '0%')}")

    print(f"\n成功创建的数据库 ({len(results['created_databases'])}):")
    print("-"*50)
    for db_name in results['created_databases']:
        print(f"  + {db_name}")

    if results['failed_databases']:
        print(f"\n创建失败的数据库 ({len(results['failed_databases'])}):")
        print("-"*50)
        for db_name in results['failed_databases']:
            print(f"  - {db_name}")

    print(f"\n详细处理结果:")
    print("-"*70)

    for result in results['file_results']:
        status = "[OK]" if result['success'] else "[FAIL]"
        print(f"{status} {result['file']} -> {result['database']} "
              f"({result['executed_statements']}/{result['total_statements']})")

        if result['errors']:
            for error in result['errors'][:2]:  # 只显示前2个错误
                print(f"    错误: {error}")
            if len(result['errors']) > 2:
                print(f"    ... 还有 {len(result['errors']) - 2} 个错误")


def list_sql_files_and_databases(file_patterns: List[str]):
    """列出SQL文件和对应的数据库名"""
    print("SQL文件和对应数据库名映射:")
    print("="*60)

    all_files = []
    for pattern in file_patterns:
        matched_files = glob.glob(pattern)
        all_files.extend(matched_files)

    all_files = list(set(all_files))
    all_files.sort()

    if not all_files:
        print("没有找到匹配的SQL文件")
        return

    creator = SQLDatabaseCreator(DatabaseConfig('mysql'), 'ERROR')  # 临时创建用于名称转换

    for sql_file in all_files:
        filename = os.path.basename(sql_file)
        database_name = creator.sanitize_database_name(filename)
        print(f"{sql_file:<30} -> {database_name}")

    print(f"\n总计: {len(all_files)} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SQL数据库批量创建和导入工具')
    parser.add_argument('files', nargs='*', default=['*.sql'],
                       help='要处理的SQL文件路径或通配符模式 (默认: *.sql)')
    parser.add_argument('-c', '--config', default='db_config.ini',
                       help='数据库配置文件路径 (默认: db_config.ini)')
    parser.add_argument('--drop-existing', action='store_true',
                       help='如果数据库已存在则删除后重新创建')
    parser.add_argument('--continue-on-error', action='store_true', default=True,
                       help='遇到错误时继续执行 (默认: True)')
    parser.add_argument('--stop-on-error', action='store_true',
                       help='遇到错误时停止执行')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--list-only', action='store_true',
                       help='只列出文件和数据库名映射，不执行创建和导入')
    parser.add_argument('--test-connection', action='store_true',
                       help='测试数据库连接')
    parser.add_argument('--batch-size', type=int, default=100,
                       help='批量提交的语句数量 (默认: 100)')
    parser.add_argument('--timeout', type=int, default=300,
                       help='单个语句执行超时时间(秒) (默认: 300)')
    parser.add_argument('--skip-large-files', action='store_true',
                       help='跳过大文件(超过1000行)')
    parser.add_argument('--large-file-threshold', type=int, default=1000,
                       help='大文件阈值(行数) (默认: 1000)')

    # 数据库连接参数
    parser.add_argument('--db-type', choices=['mysql', 'postgresql'],
                       help='数据库类型')
    parser.add_argument('--host', help='数据库主机')
    parser.add_argument('--port', type=int, help='数据库端口')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--password', help='密码')

    args = parser.parse_args()

    # 只列出文件映射
    if args.list_only:
        list_sql_files_and_databases(args.files)
        return

    # 测试数据库连接
    if args.test_connection:
        try:
            # 加载配置
            if any([args.db_type, args.host, args.username]):
                config = DatabaseConfig(
                    db_type=args.db_type or 'mysql',
                    host=args.host or 'localhost',
                    port=args.port,
                    username=args.username,
                    password=args.password
                )
            else:
                if not os.path.exists(args.config):
                    print(f"配置文件不存在: {args.config}")
                    return
                config = load_config_from_file(args.config)

            print("测试数据库连接...")
            print(f"数据库类型: {config.db_type}")
            print(f"服务器地址: {config.host}:{config.port}")
            print(f"用户名: {config.username}")
            print("-" * 40)

            creator = SQLDatabaseCreator(config, 'INFO')
            if creator.connect_to_server():
                print("✓ 数据库连接成功！")
                creator.disconnect()
            else:
                print("✗ 数据库连接失败！")
                sys.exit(1)
        except Exception as e:
            print(f"连接测试出错: {str(e)}")
            sys.exit(1)
        return

    try:
        # 加载数据库配置
        if any([args.db_type, args.host, args.username]):
            # 使用命令行参数
            config = DatabaseConfig(
                db_type=args.db_type or 'mysql',
                host=args.host or 'localhost',
                port=args.port,
                username=args.username,
                password=args.password
            )
        else:
            # 从配置文件加载
            if not os.path.exists(args.config):
                print(f"配置文件不存在: {args.config}")
                print("请确保配置文件存在，或使用命令行参数指定数据库连接信息")
                return
            config = load_config_from_file(args.config)

        # 验证必要的配置
        if not all([config.username, config.password]):
            print("错误: 缺少数据库用户名或密码")
            print("请在配置文件中设置或使用命令行参数指定")
            return

        # 创建处理器并执行
        creator = SQLDatabaseCreator(config, args.log_level)

        try:
            continue_on_error = args.continue_on_error and not args.stop_on_error

            print(f"开始批量处理SQL文件...")
            print(f"数据库类型: {config.db_type}")
            print(f"服务器地址: {config.host}:{config.port}")
            print(f"删除已存在数据库: {'是' if args.drop_existing else '否'}")
            print(f"遇到错误时继续: {'是' if continue_on_error else '否'}")
            print("-" * 50)

            results = creator.process_sql_files(
                args.files,
                drop_existing=args.drop_existing,
                continue_on_error=continue_on_error,
                skip_large_files=args.skip_large_files,
                large_file_threshold=args.large_file_threshold,
                batch_size=args.batch_size
            )

            print_results(results)

            # 根据结果设置退出码
            if results['failed_files'] > 0:
                sys.exit(1)

        finally:
            creator.disconnect()

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
