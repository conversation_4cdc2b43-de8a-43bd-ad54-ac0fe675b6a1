-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: bpm-report
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `instancehistory`
--

DROP TABLE IF EXISTS `instancehistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `instancehistory` (
  `ApprovalHistoryId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键，对应审批记录表中的主键',
  `InstanceNumber` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实例编号',
  `ActivityName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '节点名',
  `StartUserOrgPathCode` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '发起人(发起人岗位)组织全路径',
  `StartUserOrgPathName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '发起人(发起人岗位)组织全路径名',
  `ResolveUserId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '审批用户ID',
  `ResolveUserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '审批用户姓名',
  `ResolveUserLoginId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '审批用户AD账号',
  `ResolveType` int DEFAULT NULL COMMENT '审批操作类型',
  `ResolveTypeName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '审批操作类型名',
  `ResolveUserRegionId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ResolveUserDeptId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ResolveUserOrgId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ResolveUserOrgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ResolveUserOrgName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ResolveUserOrgPathCode` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `ResolveUserOrgPathName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `ArriveDate` datetime DEFAULT NULL COMMENT '到达时间',
  `ApprovalDate` datetime DEFAULT NULL COMMENT '审批时间',
  `ElapsedTime` int DEFAULT NULL COMMENT '审批时间长',
  `BSID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `BTID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ApprovalHistoryId`) USING BTREE,
  KEY `IX_InstanceHistory_ResolveType` (`ResolveType`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `instancehistory`
--

LOCK TABLES `instancehistory` WRITE;
/*!40000 ALTER TABLE `instancehistory` DISABLE KEYS */;
/*!40000 ALTER TABLE `instancehistory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `instances`
--

DROP TABLE IF EXISTS `instances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `instances` (
  `InstanceNumber` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '实例编号',
  `InstanceTopic` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '实例主题',
  `StartUserId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起用户ID',
  `StartUserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起人姓名',
  `StartUserLoginId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起人AD账号',
  `StartUserOrgId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起人(发起人岗位)组织ID',
  `StartUserOrgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起人(发起人岗位)组织编码',
  `StartUserOrgName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发起人(发起人岗位)组织名称',
  `StartUserOrgPathCode` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '发起人(发起人岗位)组织全路径',
  `StartUserOrgPathName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '发起人(发起人岗位)组织全路径名',
  `ProcessId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程ID',
  `ProcessName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '流程名称',
  `ProcessTopBusinessType` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程(一级)业务类型ID',
  `ProcessTopBusinessTypeName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程(一级)业务类型名称',
  `InstanceStatus` int DEFAULT NULL COMMENT '实例状态(数值)',
  `InstanceStatusName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实例状态名称',
  `InstanceStartTime` datetime DEFAULT NULL COMMENT '实例申请时间',
  `InstanceLastTime` datetime DEFAULT NULL COMMENT '实例最后时间(如果结束，就是结束时间，如果未结束就是最后到达时间)',
  `InstanceBranchId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '实例所走分支ID',
  `InstanceBranchName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '实例所走分支名',
  `BSID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `BTID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`InstanceNumber`) USING BTREE,
  UNIQUE KEY `PK_InstanceNumber` (`InstanceNumber`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `instances`
--

LOCK TABLES `instances` WRITE;
/*!40000 ALTER TABLE `instances` DISABLE KEYS */;
/*!40000 ALTER TABLE `instances` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `syncincrement`
--

DROP TABLE IF EXISTS `syncincrement`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `syncincrement` (
  `Type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '类型：SyncFlag',
  `IncrementValue` datetime DEFAULT NULL COMMENT '上一次同步时间',
  PRIMARY KEY (`Type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `syncincrement`
--

LOCK TABLES `syncincrement` WRITE;
/*!40000 ALTER TABLE `syncincrement` DISABLE KEYS */;
/*!40000 ALTER TABLE `syncincrement` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `userorgrights`
--

DROP TABLE IF EXISTS `userorgrights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `userorgrights` (
  `Id` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `UserId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `OrganizationId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `OrganizationName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IX_UserOrgRights_OrganizationId` (`OrganizationId`) USING BTREE,
  KEY `IX_UserOrgRights_UserId` (`UserId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `userorgrights`
--

LOCK TABLES `userorgrights` WRITE;
/*!40000 ALTER TABLE `userorgrights` DISABLE KEYS */;
/*!40000 ALTER TABLE `userorgrights` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-05 17:00:53
