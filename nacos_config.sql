-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: nacos_config
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `config_info`
--

DROP TABLE IF EXISTS `config_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfo_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=564 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info`
--

LOCK TABLES `config_info` WRITE;
/*!40000 ALTER TABLE `config_info` DISABLE KEYS */;
INSERT INTO `config_info` VALUES (1,'medusa.service.platform.entrance.appsetting.json','platform','{\n    \"Logging\": {\n        \"LogLevel\": {\n            \"Default\": \"Debug\",\n            \"System\": \"Information\",\n            \"Microsoft\": \"Information\"\n        }\n    },\n    \"InitDatabase\": false,\n    \"LdapSettings\": {\n        \"Enable\": false,\n        \"UseSSL\": false,\n        \"ServerName\": \"movit-tech.com\",\n        \"ServerPort\": 389\n    },\n    \"Persistence\": {\n        \"Boost\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"     \n        },\n        \"Process\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\n        },         \n        \"CronJob\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=hangfire;uid=bpm;Pwd=*****************" \n        },\n        \"Log\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n        },\n        \"LowCode\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LowCode;uid=bpm;Pwd=*****************"\n        }          \n    },\n    \"WebsocketExchange\": \"boards.refresh\",\n    \"Redis\": {\n        \"Host\": \"*************:6380,syncTimeout=5000,ConnectTimeout=5000\",\n        \"Db\": 1,\n        \"CacheDb\":7,\n        \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"GatewayService\": {\n        \"Db\": 8,\n        \"Key\": \"_routes\"\n    },\n    \"AttachmentsPath\": \"/var/www/uploads\",\n    \"EngineSettings\": {\n        \"StartApi\": \"/engine/v1/start\",\n        \"OperateApi\": \"/engine/v1/tasks\",\n        \"BatchOperateApi\": \"/engine/v1/batch-tasks\",\n        \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n        \"UserInstacesApi\": \"/engine/v1/user-instances\",\n        \"UserInstaceGroupsApi\": \"/engine/v1/user-instance-groups\",\n        \"UserErrorInstacesApi\": \"/engine/v1/user-error-instances\",\n        \"UserErrorInstaceGroupsApi\": \"/engine/v1/user-error-instance-groups\",\n        \"UserStateOfInstaceApi\": \"/engine/v1/instances/{instance-number}/user-state\",\n        \"InstanceBaseInfoApi\": \"/engine/v1/instances/{instance-number}/info\",\n        \"InstanceRelationsApi\": \"/engine/v1/instances/{instance-number}/relations\",\n        \"InstanceAttachmentsApi\": \"/engine/v1/instances/{instance-number}/attachments\",\n        \"InstanceRecordsApi\": \"/engine/v1/instances/{instance-number}/records\",\n        \"InstanceFormParamsApi\": \"/engine/v1/instances/{instance-number}/form-params\",\n        \"RelationInstancesApi\": \"/engine/v1/relation-instances\",\n        \"InstanceStepsApi\": \"/engine/v1/instances/{instance-number}/steps\",\n        \"InstanceRejectStepsApi\": \"/engine/v1/instances/{instance-number}/activities\",\n        \"NoticeApi\": \"/engine/v1/notice\",\n        \"UserMenuNumbersApi\": \"/engine/v1/menu-numbers\",\n        \"MaintainenceInstancesApi\": \"/engine/v1/maintenence-instances\",\n        \"OperateNodeApi\": \"/engine/v1/instances/{number}/nodes\",\n        \"GetInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n        \"SaveInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n        \"InstanceRecallApi\": \"/engine/v1/instance-recall\",\n        \"InstanceResumeApi\": \"/engine/v1/resume\",\n        \"InstanceLinearActivitiesApi\": \"/engine/v1/instances/{instance-number}/line-activities\",\n        \"AnalysisInstanceApi\": \"/engine/v1/analysis-instances\",\n        \"AnalysisNodeApi\": \"/engine/v1/analysis-nodes\",\n        \"AnalysisProcessEfficiencyApi\": \"/engine/v1/analysis-processes/{id}/efficiency\",\n        \"InstanceCancelApi\": \"/engine/v1/instance-cancel\",\n        \"AddCommentsApi\": \"/engine/v1/instances/{number}/comment\",\n        \"ProcessStepsApi\": \"/engine/v1/process-steps\"\n    },\n    \"TodoCentreSettings\": {\n        \"UserToDoTasksApi\": \"/todo-centre/v1/tasks\",\n        \"UserToDoTaskGroupsApi\": \"/todo-centre/v1/task-groups\",\n        \"UserDoneTasksApi\": \"/todo-centre/v1/archived-tasks\",\n        \"UserDoneTaskGroupsApi\": \"/todo-centre/v1/archived-task-groups\",\n        \"UserMenuNumbersApi\": \"/todo-centre/v1/tasks/menu-numbers\",\n        \"UserDockingTasksApi\": \"/todo-centre/v1/docking-tasks\",\n        \"UserDockingTasksUrgingApi\": \"/todo-centre/v1/docking-tasks/{id}/urging\"\n    },\n    \"ProcessSettings\": {\n        \"AgentUsersApi\": \"/process/v1/agent-users\",\n        \"ApiTablesApi\": \"/process/v1/manage/api-tables\",\n        \"UpdateAuthMemberOrgpathApi\": \"/process/v1/manage/process-auth/update-auth-path\"\n    },\n    \"LogSettings\": {\n        \"QueryDbType\": \"MySql\",\n        \"MinimumLevel\": 3,\n        \"DBName\": \"LogCenter\",\n        \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n        \"Logger\": {\n            \"DotNetCore.CAP\": \"DotNetCore.CAP\",\n            \"MT.Enterprise.Core\": \"MT.Enterprise.Core\",\n            \"Medusa.Engine\": \"Medusa.Engine\",\n            \"Medusa.Service.Platform\": \"Medusa.Service.Platform\",\n            \"Medusa.Service.Form\": \"Medusa.Service.Form\",\n            \"Medusa.Service.Process\": \"Medusa.Service.Process\",\n            \"Medusa.Service.ToDo_Center\": \"Medusa.Service.ToDo_Center\",\n            \"Medusa.Service.Biz_Logic\": \"Medusa.Service.Biz_Logic\",\n            \"Other\": \"Other\"\n        },\n        \"OperationLog\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n        }\n    },\n    \"EncryptKeys\": {\n        \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n    },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"30\"\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": 600,\n        \"IsHttps\": false\n    },    \n    \"BPM\": {\n        \"Url\": \"https://bpm.chinahho.cn:2005/todo-center\",\n        \"ImpersonatePath\": \"/auth/impersonate?movitech_token\",\n        \"IndependentPath\": \"/independent/done/{id}\",\n        \"TokenTimeout\": 10080,\n        \"MobileUrl\": \"https://bpm.chinahho.cn:2005/mobile\"\n    },\n    \"MDM\": {\n        \"RefreshCacheApi\": \"/platform/v1/manage/refresh-org-cache\",\n        \"RootOrgCode\": \"Movitech\",\n        \"RootOrgName\": \"盟拓软件(苏州)有限公司\",\n        \"AppKey\": \"de\",\n        \"UserApi\": \"http://*************/api/inner/datapub/rest/api/v1/hcm/user\",\n        \"OrgApi\": \"http://*************/api/inner/datapub/rest/api/v1/hcm/org\"\n    },\n    \"MobileSettings\": {\n        \"ListUrl\": \"http://WXAPPROVETEST.NIPPONPAINT.COM.CN/WeiXin/Index2018.aspx\"\n    },\n    \"IDocV\": {\n        \"IDocVUploadUri\": \"http://************/doc/upload\",\n        \"IDocVViewUri\": \"http://************/view\",\n        \"IDocVToken\": \"testtoken\"\n    },\n    \"CronJob\": {\n        \"Host\": \"http://*************\",\n        \"Port\": \"32004\",\n        \"Uri\": \"/job\",\n        \"BasicUserName\": \"bpm\",\n        \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n        \"DefaultTimeOut\": 180000\n    },\n    \"MessageSetting\": {\n        \"Email\": {\n            \"Host\": \"smtp.movit-tech.com\",\n            \"User\": \"<EMAIL>\",\n            \"Password\": \"\",\n            \"From\": \"<EMAIL>\",\n            \"Port\": \"25\",\n            \"TryTimes\": \"3\",\n            \"AllowedEmail\":\"<EMAIL>;<EMAIL>\"\n        },\n        \"ShortMessage\": {\n            \"Host\": \"https://api.wy17173.com:8443/sms/api/sendMessage\",\n            \"UserName\": \"910020\",\n            \"Password\": \"kTouD3mvz3I1\"\n        },\n        \"WeChat\": {\n            \n        },\n        \"DingTalk\": {\n            \"ApiUrl\": \"https://oapi.dingtalk.com\",\n            \"AppKey\": \"\",\n            \"AppSecret\": \"zVg9dJvcMEkkhN8XyK4WcW_kph_iXevekRFJOToG_P6wWd4qqukIF-RJmoeUo4R_\",\n            \"Agent_Id\": \"1641511600\",\n            \"ToUsersTbField\": \"WorkNumber\",\n            \"AllowedUserIds\": \"74f63559-7190-42a8-a71e-b6f16be08e86;0b42f88e-3fd4-4c0b-8174-36afbc25bcdc\",\n            \"TryTimes\": 3\n        },\n        \"Portal\": {\n            \"Host\": \"http://*************\",\n            \"TryTimes\": \"2\",\n            \"BizType\": \"bpm\",\n            \"BizKey\": \"bpm\",\n            \"Types\": \"pc\"\n        }\n    },\n    \"matrixAccessToken\": {\n        \"enable\": true,\n        \"tokenExpire\": \"100\",\n        \"serverExpire\": \"100\",\n        \"whiteList\" : {\n            \"^/v1/impersonate$\": [\"Post\"],\n            \"^/v1/impersonate-out$\": [\"Post\"],\n            \"^/v1/customer/menus$\": [\"Get\"],\n            \"^/v1/mobile/menus$\": [\"Get\"],\n            \"^/v1/messages/[^/]+$\": [\"Post\"],\n            \"^/v1/product-license/[^/]+$\": [\"Get\"],\n            \"^/v1/users/[^/]+/mobile-impersonate-url$\": [\"Get\"],\n            \"^/v1/manage/refresh-cache/[^/]+$\": [ \"Post\" ],\n            \"^/v1/manage/init-cache$\": [ \"Post\" ],\n            \"^/v1/manage/refresh-user-cache$\": [\"Get\"]\n        }\n    },\n    \"FeishuSettings\": {\n        \"AppId\": \"cli_a077a343d1f8500d\",\n        \"AppSecret\": \"\",\n        \"TenantTokenUrl\": \"https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal\",\n        \"BatchGetIdUrl\": \"https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id\"\n    }   \n}','0f3043c2c6d938e709527bb249d12b89','2023-09-08 08:41:36','2025-07-23 03:25:35',NULL,'*************','','shuiwu','','','','json',''),(2,'management.config.json','management','{\n  \"urlKey\": \"platform\",\n  \"port\": 20083,\n  \"uploadLimit\": \"200mb\",\n  \"apiGateway\": {\n    \"uri\": \"http://*************:32000\",\n    \"appKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"appSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n  },\n  \"localServices\": [\n    {\n      \"enable\": false,\n      \"name\": \"process\",\n      \"uri\": \"http://localhost:5052\"\n    },\n    {\n      \"enable\": false,\n      \"name\": \"platform\",\n      \"uri\": \"http://localhost:5051\"\n    }\n  ],\n  \"adminUri\": \"\",\n  \"sso\":  {\n    \"type\": \"movitech\",\n    \"movitech\": {\n      \"pc\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/platform/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      }\n    }\n  },\n  \"impersonatePath\": \"/auth/impersonate\",\n  \"noNeedAuthPaths\": {\n    \"state\": \"/platform/v1/userExts\",\n    \"captcha\": \"/platform/v1/captcha\",\n    \"process-json\": \"/process/v1/process-json\"\n  },\n  \"session\": {\n    \"maxAge\": 3600,\n    \"redis\": {\n      \"host\": \"*************\",\n      \"port\": 6380,\n      \"db\": 0,\n      \"password\": \"87htd2NxIQ0YguE9\"\n    }\n  },\n  \"rabbitMQ\": {\n    \"UserName\": \"bpm\",\n    \"Password\": \"1nsc72AZBxNFLtw4\",\n    \"host\": \"*************\",\n    \"exchange\": \"boards.refresh\"\n  },\n  \"idocv\":{\n    \"view\":\"http://************/view/url?url=\"\n  },\n  \"laboratoryFunction\": true,\n  \"documentEdit\":{\n    \"fileEditUrl\":\"https://localhost:44307/document-edit/documentedit\",\n    \"fileCentreUrl\":\"http://*************:10057\",\n    \"fileControlUrl\":\"officecontrol/exeindex.html\"\n  },\n  \"ExcelProcess\":{\n    \"CustomFlag\":\"&\"\n  },\n   \"formDesginConfig\": {\n    \"processClientViewUrl\": \"https://bpm.chinahho.cn:2005/customer/done/{procinstNo}\",\n    \"fileUploadType\": \"local\",\n    \"baiduak\": \"QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz\"\n  }\n}\n','74c5398bec63f4719955f8a82dc6e256','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(3,'customer.config.json','customer','{\n  \"urlKey\": \"customer\",\n  \"port\": 20086,\n  \"uploadLimit\": \"200mb\",\n  \"apiGateway\": {\n    \"uri\": \"http://*************:32000\",\n    \"appKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"appSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n  },\n  \"localServices\": [],\n  \"adminUri\": \"\",\n  \"sso\": {\n    \"type\": \"movitech\",\n    \"movitech\": {\n      \"pc\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/customer/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      },\n      \"h5\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/todo-center/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      },\n      \"wechat\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/todo-center/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      }\n    }\n  },\n  \"impersonatePath\": \"/customer/auth/impersonate\",\n  \"noNeedAuthPaths\": {\n    \"state\": \"/platform/v1/userExts\",\n    \"captcha\": \"/platform/v1/captcha\"\n  },\n  \"session\": {\n    \"maxAge\": 3600,\n    \"redis\": {\n      \"host\": \"*************\",\n      \"port\": 6380,\n      \"db\": 0,\n      \"password\": \"87htd2NxIQ0YguE9\"\n    }\n  },\n  \"rabbitMQ\": {\n    \"UserName\": \"bpm\",\n    \"Password\": \"1nsc72AZBxNFLtw4\",\n    \"host\": \"*************\",\n    \"exchange\": \"boards.refresh\"\n  },\n  \"idocv\":{\n    \"view\":\"http://************/view/url?url=\"\n  },\n  \"documentEdit\":{\n    \"fileEditUrl\":\"https://localhost:44307/document-edit/documentedit\",\n    \"fileCentreUrl\":\"http://*************:10057\",\n    \"fileControlUrl\":\"https://localhost:44307/document-edit/exeindex\"\n  },\n  \"IsParalle\": true,\n  \"formDesginConfig\": {\n    \"processClientViewUrl\": \"https://bpm.chinahho.cn:2005/customer/done/{procinstNo}\",\n    \"baiduak\": \"QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz\",\n    \"richTextFileSecurityKey\": \"Qazxsw4321\",\n    \"richTextFileTimeout\": \"30\",\n    \"processMapUrl\": \"https://bpm.chinahho.cn:2005/todo-center/process-map\"\n  },\n  \"userGroupEditUrl\":\"https://bpm.chinahho.cn:2005/todo-center/user-group\"\n}\n','c6fbcc8606eb21117f7e06abc26b41f1','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(4,'medusa.engine.restentrance.appsetting.json','engine','{\r\n  \"Logging\": {\r\n    \"LogLevel\": {\r\n      \"Default\": \"Error\"\r\n    }\r\n  },\r\n  \"Log4Net\": {\r\n    \"RepositoryName\": \"Medusa.Engine.RestEntrance\"\r\n  },\r\n  \"AllowedHosts\": \"*\",\r\n  \"InitDatabase\": false,\r\n  \"Persistence\": {\r\n    \"Engine\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM-Engine;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"EngineEventBus\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM_EngineEventBus;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"EngineCache\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM-EngineCache;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"Boost\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"Process\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\r\n    }\r\n  },\r\n  \"LogSettings\": {\r\n    \"QueryDbType\": \"MySql\",\r\n    \"MinimumLevel\": 3,\r\n    \"DBName\": \"LogCenter\",\r\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\r\n    \"OperationLog\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\r\n    }\r\n  },\r\n  \"EventBusSettings\": {\r\n    \"RabbitMQ\": {\r\n      \"ExchangeName\": \"bpm4.engine\",\r\n      \"HostName\": \"*************\",\r\n      \"UserName\": \"bpm\",\r\n      \"Password\": \"1nsc72AZBxNFLtw4\"\r\n    },\r\n    \"Redis\": {\r\n      \"Host\": \"*************:6380\",\r\n      \"DB\": 1,\r\n      \"CacheDb\": 7,\r\n      \"Password\": \"87htd2NxIQ0YguE9\"\r\n    },\r\n    \"EngineRedis\": {\r\n      \"Host\": \"*************:6379\",\r\n      \"DB\": 2,\r\n      \"Password\": \"7Qx0YFWyUDsbjzBv\"\r\n    },\r\n    \"Metadata\": {\r\n      \"GroupName\": \"bpm4.engine.api\",\r\n      \"KeyPrefix\": \"engine\",\r\n      \"KeyExpireHour\": 1,\r\n      \"ExpiresDays\": 7,\r\n      \"EventExpiresDays\": 30,\r\n      \"ConsumerThreadCount\": 10\r\n    },\r\n    \"DelayCount\": 1\r\n  },\r\n  \"PlatformSettings\": {\r\n    \"ReportDataPermissionApi\": \"/platform/v1/roles/F0E795D6-9650-4613-AE4D-42F0AFCD4928/users/{id}/data-permissions\",\r\n    \"OrganizationBaseInfoApi\": \"/platform/v1/manage/organizations-by-ids/{ids}\",\r\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\",\r\n    \"DataAuthorityOrganizationApi\": \"/platform/v1/manage/data-authoritys-organization\",\r\n    \"MessageAddApi\": \"/platform/v1/messages/{category}\"\r\n  },\r\n  \"ProcessSettings\": {\r\n    \"DataAuthoritySystemApi\": \"/process/v1/manage/data-authoritys-systems\",\r\n    \"DataAuthorityBusinessTypeApi\": \"/process/v1/manage/data-authoritys-business-type\",\r\n    \"DataAuthorityByUserApi\": \"/process/v1/manage/data-authoritys-by-user\",\r\n    \"DataAuthorityBusinessTypeUserStateApi\": \"/process/v1/manage/data-authoritys-business-type-user-state/{path}/{userid}\",\r\n    \"DefaultActionsConsultApi\": \"/process/v1/manage/process-buttons/default-actions-consult\"\r\n  },\r\n  \"EncryptKeys\": {\r\n    \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\r\n  },\r\n  \"ApiGateway\": {\r\n    \"Host\": \"*************\",\r\n    \"Port\": \"32000\",\r\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\r\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\r\n    \"Timeout\": \"60\"\r\n  },\r\n  \"ApiRequest\": {\r\n    \"Host\": \"*************:6380\",\r\n    \"DB\": 8,\r\n    \"Password\": \"87htd2NxIQ0YguE9\",\r\n    \"Timeout\": \"30\",\r\n    \"IsHttps\": false\r\n  },\r\n  \"TodoPushSettings\": {\r\n      \"PushType\": \"mq\",\r\n      \"PushCenterSiteUrl\": \"https://bpm.chinahho.cn:2005/customer\",\r\n      \"PushCenterMobileSiteUrl\": \"https://bpm.chinahho.cn:2005/mobile\",\r\n      \"PushSystemCode\": \"BPM\",\r\n      \"RabbitMQ\": {\r\n          \"VirtualHost\": \"/\",\r\n          \"ExchangeName\": \"\",\r\n          \"HostName\": \"*************\",\r\n          \"Port\": 5672,\r\n          \"UserName\": \"bpm\",\r\n          \"Password\": \"1nsc72AZBxNFLtw4\"\r\n      },\r\n      \"AddDraft\": {\r\n          \"Method\": \"POST\",\r\n          \"Url\": \"/todo-centre/v1/draft/batchInsert\",\r\n          \"Queue\": \"othermessage\"\r\n      },\r\n      \"DeleteDraft\": {\r\n          \"Method\": \"Delete\",\r\n          \"Url\": \"/todo-centre/v1/draft/batchDelete\",\r\n          \"Queue\": \"othermessage\"\r\n      }\r\n  },\r\n  \"Endpoints\": {\r\n    \"Dependencies\": {\r\n      \"ProcessStruct\": {\r\n        \"Type\": \"service\",\r\n        \"Method\": \"get\",\r\n        \"Url\": \"/process/v1/process-structure\"\r\n      },\r\n      \"UserFetch\": {\r\n        \"Type\": \"service\",\r\n        \"Method\": \"post\",\r\n        \"Url\": \"/platform/v1/process-resolver-users\"\r\n      }\r\n    }\r\n  },\r\n  \"CronJob\": {\r\n    \"Host\": \"http://*************\",\r\n    \"Port\": \"32004\",\r\n    \"Uri\": \"/job\",\r\n    \"BasicUserName\": \"bpm\",\r\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\r\n    \"DefaultTimeOut\": \"180000\",\r\n    \"DefaultRetryDelaysInSeconds\": \"60,3600,21600\"\r\n  },\r\n  \"BPM\": {\r\n      \"TokenTimeout\": 10080\r\n  },\r\n  \"AutoJobSettings\": {\r\n      \"RejectAutoCancel\": {\r\n          \"Method\": \"PUT\",\r\n          \"Url\": \"/engine/v1/reject-auto-instance-cancel\",\r\n          \"Comment\": \"\"\r\n      },\r\n      \"AutoApproval\": {\r\n          \"Method\": \"PUT\",\r\n          \"Url\": \"/engine/v1/timeout-auto-tasks\",\r\n          \"Comment\": \"超出设定办理时间，系统已自动办理通过\"\r\n      },\r\n      \"AutoApprovalMessage\": {\r\n          \"Method\": \"POST\",\r\n          \"Url\": \"/platform/v1/messages/Wf_TaskAutoApprovalMessage\",\r\n          \"Comment\": \"\"\r\n      }\r\n  },\r\n  \"TimeoutAutoCancelReminderMinute\": \"1440\",\r\n  \"matrixAccessToken\": {\r\n      \"enable\": true,\r\n      \"tokenExpire\": \"100\",\r\n      \"serverExpire\": \"100\",\r\n      \"whiteList\" : {}\r\n  }\r\n}','91956294ce2ad6c4b162c4a57cf597d9','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(6,'cronjob.config.json','cronjob','{\n  \"port\": 20070,\n  \"apiGateway\": {\n    \"enable\": true,\n    \"uri\": \"http://*************:32000\",\n    \"appKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"appSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n  },\n  \"redis\": {\n    \"host\": \"*************\",\n    \"port\": 6380,\n    \"db\": 2,\n    \"password\": \"87htd2NxIQ0YguE9\"\n  }\n}','41a58932f97bafdbb2b64bfa0304a6bd','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(7,'medusa.service.process.entrance.appsetting.json','process','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Error\"\n    }\n  },\n  \"AllowedHosts\": \"*\",\n  \"InitDatabase\": false,\n  \"FormDataPeriod\": 30,\n  \"ViewTokenExpireHours\": 99999,\n  \"LimitStartInstantsNumber\": 99999,\n  \"LimitApprovInstantsNumnber\": 99999,\n  \"CurrentRuntimeEnvironment\": \"PRD\",\n  \"Persistence\": {\n    \"Process\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\n    },\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\n    },\n    \"EventBus\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM_EngineEventBus;uid=bpm;Pwd=*****************"\n    },\n    \"Engine\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Engine;uid=bpm;Pwd=*****************"\n    },\n    \"EngineCache\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-EngineCache;uid=bpm;Pwd=*****************"\n    },\n    \"Todo\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-ToDoCenter;uid=bpm;Pwd=*****************"\n    },\n    \"Report\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Report;uid=bpm;Pwd=*****************"\n    },\n    \"LogCenter\": {\n        \"DbType\": \"Mysql\",\n        \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    },\n     \"StructuredStorage\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=bpm-structuredstorage;uid=bpm;Pwd=*****************"\n    }  \n  },\n  \"LogSettings\": {\n    \"QueryDbType\": \"MySql\",\n    \"MinimumLevel\": 3,\n    \"DBName\": \"LogCenter\",\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n    \"OperationLog\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"PlatformSettings\": {\n    \"UserRoles\": \"/platform/v1/users/{id}/roles\",\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\",\n    \"UserDataAuthorities\": \"/platform/v1/manage/data-authoritys-by-user\",\n    \"UserDataAuthoritiesGroupByRole\": \"/platform/v1/manage/data-authoritys-by-user-groupby-role\",\n    \"CurrentUserAuthorityOrganization\": \"/platform/v1/manage/data-authoritys-organization\",\n    \"UsersImpersonateUrl\": \"/platform/v1/users/{account}/impersonate-url\"\n  },\n  \"FormSettings\": {\n    \"Apis\": {\n      \"ProcessFormRelation\": \"/form/v1/manage/processes/{id}/forms\",\n      \"FormBatchPublish\": \"/form/v1/manage/forms/batch-publish\",\n      \"FormState\": \"/form/v1/manage/form-states\",\n      \"Form\": \"/form/v1/manage/form\",\n      \"FormDesigns\": \"/form/v1/manage/forms\",\n      \"FormSchema\": \"/form/v1/templates/{id}/form-schema\",\n      \"FormEnginePublish\": \"/form/v1/engine-endpoints/instance-start\",\n      \"FormVersions\": \"/form/v1/manage/form-versions\"\n    }\n  },\n  \"ProcessSettings\": {\n    \"BpmnPageUrl\": \"http://*************:32002/process/bpmn-view\",\n    \"ExcelProcess\": {\n      \"NodeEndFlag\": \"\",\n      \"ABRoleSuffix\": \"B\",\n      \"CustomFlag\":\"&\"\n    },\n    \"AuthorizedBusinessTypes\": \"/process/v1/tree-business-types-authority?params=%7B%7D\",\n    \"ProcessWebUrl\": \"https://bpm.chinahho.cn:2005/customer/start\",\n    \"ProcessMobileUrl\": \"https://bpm.chinahho.cn:2005/mobile/start\"\n  },\n  \"EngineSettings\": {\n    \"ProcessMainParamsApi\": \"/engine/v1/process-instance-main-params\",\n    \"InstanceJson\": \"/engine/v1/instances/{id}/json\",\n    \"InstanceWholeJson\": \"/engine/v1/instances/{id}/whole-json\",\n    \"StartApi\": \"/engine/v1/start\",\n    \"OperateApi\": \"/engine/v1/tasks\",\n    \"BatchOperateApi\": \"/engine/v1/batch-tasks\",\n    \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n    \"TasksStateApi\": \"/engine/v1/tasks/status\",\n    \"UserInstacesApi\": \"/engine/v1/user-instances\",\n    \"UserInstaceGroupsApi\": \"/engine/v1/user-instance-groups\",\n    \"UserErrorInstacesApi\": \"/engine/v1/user-error-instances\",\n    \"UserErrorInstaceGroupsApi\": \"/engine/v1/user-error-instance-groups\",\n    \"UserStateOfInstaceApi\": \"/engine/v1/instances/{instance-number}/user-state\",\n    \"InstanceBaseInfoApi\": \"/engine/v1/instances/{instance-number}/info\",\n    \"InstanceRelationsApi\": \"/engine/v1/instances/{instance-number}/relations\",\n    \"InstanceAttachmentsApi\": \"/engine/v1/instances/{instance-number}/attachments\",\n    \"InstanceRecordsApi\": \"/engine/v1/instances/{instance-number}/records\",\n    \"InstanceFormParamsApi\": \"/engine/v1/instances/{instance-number}/form-params\",\n    \"RelationInstancesApi\": \"/engine/v1/relation-instances\",\n    \"InstanceStepsApi\": \"/engine/v1/instances/{instance-number}/steps\",\n    \"InstanceRejectStepsApi\": \"/engine/v1/instances/{instance-number}/reject-steps/{task-id}/task\",\n    \"NoticeApi\": \"/engine/v1/notice\",\n    \"UserMenuNumbersApi\": \"/engine/v1/menu-numbers\",\n    \"MaintainenceInstancesApi\": \"/engine/v1/data-authority-maintenence-instances\",\n    \"OperateNodeApi\": \"/engine/v1/instances/{number}/nodes\",\n    \"GetInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"SaveInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"InstanceRecallApi\": \"/engine/v1/instance-recall\",\n    \"InstanceResumeApi\": \"/engine/v1/resume\",\n    \"InstanceLinearActivitiesApi\": \"/engine/v1/instances/{instance-number}/line-activities\",\n    \"AnalysisInstanceApi\": \"/engine/v1/analysis-instances\",\n    \"AnalysisNodeApi\": \"/engine/v1/analysis-nodes\",\n    \"AnalysisProcessEfficiencyApi\": \"/engine/v1/analysis-processes/{id}/efficiency\",\n    \"InstanceCancelApi\": \"/engine/v1/instance-cancel\",\n    \"InstanceBatchCancelApi\": \"/engine/v1/batch-instance-cancel\",\n    \"InstanceBatchNoticeApi\": \"/engine/v1/batch-notice\",\n    \"AddCommentsApi\": \"/engine/v1/instances/{number}/comment\",\n    \"ProcessStepsApi\": \"/engine/v1/process-steps\",\n    \"TaskStepInfoApi\": \"/engine/v1/tasks/{id}/step-info\",\n    \"TaskFixedByUserApi\": \"/engine/v1/tasks/fixed-by-user/{user-id}\",\n    \"TaskFixedBatchReplaceApi\": \"/engine/v1/tasks/fixed-batch-replace\",\n    \"RoleTaskFixedBatchReplaceApi\": \"/engine/v1/role-tasks/fixed-batch-replace\",\n    \"InstanceStatus\": \"/engine/v1/instances/instance-status\",\n    \"TaskByUsuerApi\": \"/engine/v1/tasks/tasks-by-user/{user-id}\",\n    \"BatchApproveApi\": \"/engine/v1/activity-resolvers/batch-approve\",\n    \"InstanceAuthIdApi\": \"/engine/v1/instances/{id}/auth-id\",\n    \"InstanceCustomTopic\": \"/engine/v1/instances/{id}/custom-topic\",\n    \"InstanceStatusApi\": \"/engine/v1/instances/{id}/instance-status\",\n    \"RecallTaskApi\": \"/engine/v1/tasks/{id}/recall-task\",\n    \"RecallExtraTaskApi\": \"/engine/v1/tasks/{id}/recall-extra-task\",\n    \"InstancePars\": \"/engine/v1/instances/{id}/instance-pars\",\n    \"ParallelStatusApi\": \"/engine/v1/tasks/{id}/parallel-status\",\n    \"CheckIsCanStartApi\": \"/engine/v1/instances/check-is-can-start?bsid={bsid}&btid={btid}&boid={boid}\",\n    \"ParallelInfoApi\": \"/engine/v1/tasks/{id}/parallel-info\",\n    \"CheckIsOwnerUserApi\": \"/engine/v1/instances/check-is-owner-user?number={number}&userId={userId}\",\n    \"TaskRecallState\": \"/engine/v1/tasks/{id}/recall-state\",\n    \"TaskDelayState\": \"/engine/v1/tasks/{id}/delay-state\",\n    \"DelayTaskApi\": \"/engine/v1/tasks/{id}/delay-task\",\n    \"UpdateInstanceCallBackStatusApi\":\"/engine/v1/instances/{instanceId}/instance-Call-Back-Status\",\n    \"InstanceBatchDelApi\": \"/engine/v1/batch-del\",\n    \"MachineNodeTaskApi\": \"/engine/v1/machine-node-task/{id}\"\n  },\n  \"ApiGateway\": {\n    \"Host\": \"*************\",\n    \"Port\": \"32000\",\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n    \"Timeout\": \"30\"\n  },\n  \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"30\",\n    \"IsHttps\": false\n  },\n  \"EncryptKeys\": {\n    \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n  },\n  \"TodoCentreSettings\": {\n    \"UserToDoTasksApi\": \"/todo-centre/v1/tasks\",\n    \"UserToDoTaskGroupsApi\": \"/todo-centre/v1/task-groups\",\n    \"UserDoneTasksApi\": \"/todo-centre/v1/archived-tasks\",\n    \"UserDoneTaskGroupsApi\": \"/todo-centre/v1/archived-task-groups\",\n    \"UserMenuNumbersApi\": \"/todo-centre/v1/tasks/menu-numbers\",\n    \"UserDockingTasksApi\": \"/todo-centre/v1/docking-tasks\",\n    \"UserDockingTasksUrgingApi\": \"/todo-centre/v1/docking-tasks/{id}/urging\",\n    \"RelationInstancesApi\": \"/todo-centre/v1/tasks/relation-instances\",\n    \"UserInstacesApi\": \"/todo-centre/v1/tasks/user-instances\",\n    \"UserInstaceGroupsApi\": \"/todo-centre/v1/tasks/user-instance-groups\",\n    \"UpdateTaskProcesingApi\": \"/todo-centre/v1/taskProcesing/{id}\",\n    \"BatchUpdateTaskProcesingApi\": \"/todo-centre/v1/batchTaskProcesing\",\n    \"BatchUpdateTaskProcesingByInstanceNumberApi\": \"/todo-centre/v1/taskProcesingByInstanceNumber/{id}\",\n    \"GlobalRelationApi\": \"/todo-centre/v1/global/relation\",\n    \"BatchDelApi\": \"/todo-centre/v1/global/batchDelete\",\n    \"TaskUpdateStatus\": \"/todo-centre/v1/task/updateStatus\"\n  },\n  \"TodoPushSettings\": {\n      \"PushType\": \"\",\n      \"PushCenterSiteUrl\": \"https://bpm.chinahho.cn:2005/customer\",\n      \"PushCenterMobileSiteUrl\": \"https://bpm.chinahho.cn:2005/mobile\",\n      \"PushSystemCode\": \"BPM\",\n      \"RabbitMQ\": {\n          \"VirtualHost\": \"/\",\n          \"ExchangeName\": \"\",\n          \"HostName\": \"*************\",\n          \"Port\": 5672,\n          \"UserName\": \"bpm\",\n          \"Password\": \"1nsc72AZBxNFLtw4\"\n      },\n      \"MyStart\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/myStart/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"AddDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"DeleteDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchDelete\",\n          \"Queue\": \"othermessage\"\n      },\n      \"UpdateTodo\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/task/batchUpdate\",\n          \"Queue\": \"todomessage\"\n      }\n  },\n  \"FileCentreSettings\": {\n    \"ServerType\": \"local\",\n        \"Upload\": \"http://*************:32057/v1/document-services/upload/{serverType}\",\n        \"Download\": \"/customer/api/file-centre/v1/document-services/download/{serverType}\",\n        \"DownloadGW\": \"http://*************:32057/v1/document-services/download/{serverType}\",\n        \"PreviewServerType\": \"wps\",\n        \"Preview\": \"/api/file-centre/v1/document-services/view/{serverType}\",\n        \"DownloadList\": \"http://*************:32000/api/file-centre/v1/document-services/downloads/{serverType}\"\n  },\n  \"Transport\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\"\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000,\n    \"DefaultRetryDelaysInSeconds\": \"60,3600,21600\",\n    \"QueueName\": \"bpm-job-queue\"\n  },\n  \"BPM\": {\n    \"Url\": \"https://bpm.chinahho.cn:2005\",\n    \"ImpersonatePath\": \"/platform/auth/impersonate?token\",\n    \"IndependentPath\": \"/customer/done/{id}\",\n    \"StartPath\": \"/customer/start?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"ReStartPath\": \"/customer/start/{instance-number}?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"TokenTimeout\": 10080\n  },\n  \"AttachmentsPath\": \"/var/www/uploads\",\n  \"EventBusSettings\": {\n    \"Redis\": {\n      \"Host\": \"*************:6380,abortConnect=false,connectTimeout=60000\",\n      \"DB\": 1,\n      \"CacheDb\":7,\n      \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"RabbitMQ\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\", \n      \"InterfaceExchange\": \"bpm4.engine.fanout\",\n      \"IsPublishDebugMessage\":true\n    }\n  },\n  \"IsEnableToDoCenter\": true,\n    \"WebOffice\": {\n    \"ExternalDomain\": \"https://wpszt.chinahho.cn:12006\",\n    \"InternalDomain\": \"http://*************\",\n    \"GetLinkApi\": \"/open/api/preview/v1/files/{file_id}/link\",\n    \"GetEditLinkApi\": \"/open/api/edit/v1/files/{file_id}/link\",\n    \"GetWrapheaderApi\": \"/open/api/cps/sync/v1/wrapheader\",\n    \"GetContentOperateApi\": \"/open/api/cps/sync/v1/content/operate\",\n    \"GetDownloadApi\": \"/open/api/cps/v1/download/{download_id}\",\n    \"AccessKey\": \"JFVYGLQPWEUSUKPP\",\n    \"SecretKey\": \"SKbgbxempheppfmq\",\n      \"FileTypeW\": \"doc;docx\",\n    \"FileTypeS\": \"xls;xlsx\",\n    \"FileTypeP\": \"ppt;pptx\",\n    \"FileTypeF\": \"pdf\",\n    \"FileTypeX\": \"png;jpg;jpeg;gif;bmp;svg;zip;rar;tar;7z;gz\",\n    \"EditRoleCode\":  \"GWGLY\"\n  },\n  \"ContractLockSettings\": {\n    \"ContractLockApi\": \"http://*************:9182\",\n    \"Accesstoken\": \"UYAIS4vIIB\",\n    \"Secret\": \"SmbMDoM1BTifTtxTNoJ8tEEUyomzIn\",\n    \"ReceiverName\": \"测试账号\",\n    \"Contact\": \"13328033090\",\n    \"CategoryId\": \"2916235861576192065\",\n    \"TenantType\": \"COMPANY\",\n    \"TenantName\": \"中国水务投资集团有限公司\",\n    \"SealId\": 2906084454476009538,\n    \"SealReplaceUrl\": {\n      \"Before\": \"http://127.0.0.1:9181\",\n      \"After\": \"http://*************:12003\"\n    },\n    \"OpenTestUser\": \"0\"\n  },\n  \"FddTaskSettings\": {\n    \"FddAppID\": \"80001107\",\n    \"FddAppSecret\": \"FFJ2FVUUQOXWFGK4GKPPRRFF74CS4KTF\",\n    \"FddOpenCorpId\": \"3b66c3fa92d84d4e9ba84b58766ba003\",\n    \"FddServerUrl\": \"https://uat-api.fadada.com/api/v5/\",\n    \"FddActorName\": \"盟拓软件（苏州）有限公司\",\n    \"FddCorpMembers\": \"1780890618366214144\",\n    \"FddIsOnline\": \"0\"\n  },\n  \"IsEnableCancelNumber\": false,\n  \"matrixAccessToken\": {\n    \"enable\": true,\n    \"tokenExpire\": \"100\",\n    \"serverExpire\": \"100\",\n    \"whiteList\" : {\n      \"^/endpoints/business-data$\": [\"Post\"],\n      \"^/endpoints/[^/]+/cancel$\": [\"Put\"],\n      \"^/endpoints/[^/]+/recall$\": [\"Put\"],\n      \"^/endpoints/[^/]+/urging$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/done$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/reject$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/notice$\": [\"Post\"],\n      \"^/endpoints/tasks/[^/]+/extra-task$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/handover$\": [\"Put\"],\n      \"^/endpoints/process-steps$\": [\"Post\"],\n      \"^/v1/instances/[^/]+/steps$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/info$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/user/info$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/rename$\": [\"Put\"],\n      \"^/v1/webOffice/v1/3rd/file/history$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/version/[^/]+$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/online$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/save$\": [\"Post\"],\n      \"^/v1/contractlock/contractcallback$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack2$\": [\"Post\"],\n      \"^/v1/documents/upload$\": [\"Post\"],\n      \"^/v1/documents/upload/gw$\": [\"Post\"],\n      \"^/v1/documents/upload/gwmb$\": [\"Post\"],\n      \"^/v1/hr/import-annual-leave$\": [\"Post\"],\n      \"^/v1/process-map/setProcessMap_Cache$\":[\"Post\"]\n    }\n  }\n}','d2d5101091cfbfabfa49b7081dd171e8','2023-09-08 08:41:36','2025-07-23 03:27:21',NULL,'*************','','shuiwu','','','','json',''),(8,'medusa.service.form.entrance.appsetting.json','form','{\n    \"Logging\": {\n        \"LogLevel\": {\n            \"Default\": \"Information\"\n        }\n    },\n    \"AllowedHosts\": \"*\",\n    \"InitDatabase\": false,\n    \"ApiVersion\": [\"V1\", \"V2\"],\n    \"Persistence\": {\n        \"Form\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=bpm-form;uid=bpm;Pwd=*****************"\n        }\n    },\n    \"LogSettings\": {\n        \"QueryDbType\": \"MySql\",\n        \"MinimumLevel\": 3,\n        \"DBName\": \"LogCenter\",\n        \"LogAddress\": \"server=*************;Port=3306;Database=logcenter;uid=bpm;Pwd=*****************"\n    },\n    \"FormSettings\": {\n        \"OnlineEditorUrl\": \"\",\n        \"DesignUrl\": \"\",\n        \"TemplatePath\": \"/var/www/templates\",\n        \"UploadApi\": \"/api/platform/v1/documents/upload\",\n        \"FormUploadApi\": \"/api/platform/v1/documents/form-files-upload\",\n        \"DictionaryApi\": \"/api/process/v1/manage/bizDictionry-modules/batch/biz-dictionaries\",\n        \"HttpPrefixApi\": \"/api/process/v1/manage/bizDictionry-modules/biz-dictionaries\",\n        \"HttpVerificationApi\": \"/api/modeling/v1/process-related/outside-verification\",\n        \"AgentUsersApi\": \"/api/platform/v1/agent-users\",\n        \"UserComponent\": {\n            \"Name\": \"select-user-input\",\n            \"Input\": \"value\",\n            \"Output\": \"change\",\n            \"Multiple\": false,\n            \"LabelField\": \"userName\"\n        },\n        \"OrganizationComponent\": {\n            \"Name\": \"select-department-input\",\n            \"Input\": \"value\",\n            \"Output\": \"change\",\n            \"Multiple\": false,\n            \"LabelField\": \"organizationName\"\n        },\n         \"DetailTableDataExportApi\": \"/api/form/v1/templates/detail-table-data/export\"\n    },\n    \"PlatformSettings\": {\n        \"ProductLicense\": \"/platform/v1/product-license/{id}\"\n    },    \n    \"ProcessSettings\": {\n        \"ProcessResolverUsersApi\": \"/api/process/v1/process-resolver-users\",\n        \"CheckFormBind\": \"/api/process/v1/design-processes/check-form-bind\",\n        \"SystemPars\": \"/api/process/v1/manage/process-paramter-modules/getSystemPars\"\n    },\n    \"EngineSettings\": {\n        \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n        \"InstanceStatus\": \"/engine/v1/instances/{id}/instance-status\"\n    },\n    \"FileCentreSettings\": {\n        \"ServerType\": \"local\",\n        \"Upload\": \"http://*************:32057/v1/document-services/upload/{serverType}\",\n        \"Download\": \"http://*************:32057/v1/document-services/download/{serverType}\",\n        \"FormUpload\": \"/api/file-centre/v1/document-services/upload/{serverType}\",\n        \"FormDownload\": \"/api/file-centre/v1/document-services/download/{serverType}\",\n        \"PreviewServerType\": \"wps\",\n        \"FormPreview\": \"/api/file-centre/v1/document-services/view/{serverType}\"\n    },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"60\"\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": \"30\",\n        \"IsHttps\": false\n    }\n}\n','931c276670dfa3f6c4174fe39ab70fba','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(9,'medusa.service.todocentre.entrance.appsetting.json','todocentre','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Information\"\n    }\n  },\n  \"AllowedHosts\": \"*\",\n  \"InitDatabase\": false,\n  \"Persistence\": {\n    \"Todo_Master\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Bpm-TodoCenter;uid=bpm;Pwd=****************;\"\n    },\n    \"Todo_Slave1\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Bpm-TodoCenter;uid=bpm;Pwd=****************;\"\n    },\n    \"Process\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=****************;\"\n    }\n  },\n  \"LogSettings\": {\n    \"QueryDbType\": \"MySql\",\n    \"MinimumLevel\": 3,\n    \"DBName\": \"LogCenter\",\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n    \"OperationLog\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"ApiGateway\": {\n    \"Host\": \"*************\",\n    \"Port\": \"32000\",\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n    \"Timeout\": \"30\"\n  },\n  \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"30\",\n    \"IsHttps\": false\n  },\n  \"OcelotAuthOption\": {\n    \"Host\": \"http://*************:32014/connect/Token\",\n    \"UserName\": \"Admin\",\n    \"Password\": \"12345678\",\n    \"GrantType\": \"client_credentials\",\n    \"UrlPrefix\": \"integration-ocelot\"\n  },\n  \"PlatformSettings\": {\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\",\n    \"Message_PostByCategory\": \"http://*************:32008/v1/messages/{category}\"\n  },\n  \"ProcessSettings\": {\n    \"DataAuthoritysByUserGroupByRole\": \"/process/v1/manage/data-authoritys-by-user-groupby-role\",\n    \"Form-params\": \"/process/v1/instances/{id}/form-params\",\n    \"TripartiteSystem\":\"/process/v1/tripartite-systems/{id}\",\n    \"UpdateInstanceCallBackStatusApi\":\"/process/v1/instances/{instanceId}/instance-call-back-status\"\n  },\n  \"FileCentreSettings\": {\n      \"ServerType\": \"local\",\n      \"Download\": \"http://*************:32057/v1/document-services/download/\"\n  },\n  \"TodoCentreSettings\": {\n    \"RouteKey\": \"todo-centre\",\n    \"TrafficLights\": {\n      \"Red\": 720,\n      \"Yellow\": 1440,\n      \"Green\": 2160\n    },\n    \"GetRelation_TakeTable\": 1,\n    \"Urge\": {\n      \"SystemCodes\": \"BPM\",\n      \"IntervalHours\": 1\n    },\n    \"SendMessage\": {\n      \"BPM\": \"\"\n    },\n    \"TaskGroupModeMaxNum\": 1000,\n    \"IsMyStartSummary\": true,\n    \"DefaultGroup\": {\n      \"GroupId\": \"08afd060-b97d-4731-82ca-57ee60bc0ef5\",\n      \"GroupCode\": \"Other\",\n      \"GroupName\": \"其他\",\n      \"GroupIdPath\": \"dc061481-c443-4ef2-9b5c-841238b996e0||08AFD060-B97D-4731-82CA-57EE60BC0EF5\",\n      \"GroupNamePath\": \"地产||其他\"\n    },\n    \"CleanData\": {\n      \"HowManyDaysAgo\": 400,\n      \"HowManyAtATime\": 500\n    },\n    \"StandardAccrualTokenUrl\": \"https://dm.gtcloud.cn:8091/eci/oauth2/edm/token/code-auth\",\n    \"StandardAccrualSearchUrl\": \"https://dm.gtcloud.cn:8091/eci/s/api/v2/api/CDA/BPMComponent/dataList\",\n    \"StandardAccrualDefaultUser\": \"97001299\",\n    \"TimeoutAutoApprovalReminderMessage\": {\n      \"ReminderMinute\": 180\n    },\n    \"CustomerUrl\": \"https://bpm.chinahho.cn:2005/customer\",\n    \"MobileUrl\": \"https://bpm.chinahho.cn:2005/mobile\"\n  },\n  \"RabbitMQOption\": {\n    \"TodoMessage\": {\n      \"HostName\": \"*************\",\n      \"Port\": 5672,\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\",\n      \"VirtualHost\": \"/\",\n      \"Exchange\": null,\n      \"Queues\": \"todomessage\"\n    },\n    \"OtherMessage\": {\n      \"HostName\": \"*************\",\n      \"Port\": 5672,\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\",\n      \"VirtualHost\": \"/\",\n      \"Exchange\": null,\n      \"Queues\": \"othermessage\"\n    },\n    \"PortalMessage\": {\n      \"HostName\": \"*************\",\n      \"Port\": 5672,\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\",\n      \"VirtualHost\": \"/\",\n      \"Exchange\": null,\n      \"Queues\": \"portalmessage\"\n    }\n  },\n  \"EventBusSettings\": {\n    \"Redis\": {\n      \"Host\": \"*************:6380,abortConnect=false,connectTimeout=60000\",\n      \"CacheDb\": 7,\n      \"Password\": \"87htd2NxIQ0YguE9\"\n    }\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000\n  },\n  \"Settings\": {\n    \"Company\": \"ShuiWu\",\n    \"SaiWu\": {\n      \"ModuleCode\": \"bpm\",\n      \"Host\": \"http://*************\",\n      \"ClientId\": \"bpm\",\n      \"ClientSecret\": \"bpm\",\n      \"BpmHost\": \"\",\n      \"needPushProcessing\": true\n    },\n    \"ShuiWu\": {\n      \"ModuleCode\": \"bpm\",\n      \"Host\": \"https://portal.chinahho.cn:2006\",\n      \"ClientId\": \"bpm\",\n      \"ClientSecret\": \"bpm\",\n      \"BpmHost\": \"/todo-centre\",\n      \"needPushProcessing\": true,\n      \"FaWenProcess\": [\n        \"2eb0003f-f34e-47dc-b651-7ddc60e57b32\",\n        \"8a487b60-04bd-48e7-b37b-6fb56309d99a\",\n        \"931c6faa-3bf0-433b-bbdf-b3e98850ce93\",\n        \"e90a332d-def4-4d1d-abfc-f2567b32b798\",\n        \"438ea907-f449-4e21-b06d-23740755ac3b\"\n      ],\n      \"ShouWenProcess\": [\n        \"2eb0003f-f34e-47dc-b651-7ddc60e57b32\",\n        \"8a487b60-04bd-48e7-b37b-6fb56309d99a\",\n        \"931c6faa-3bf0-433b-bbdf-b3e98850ce93\",\n        \"e90a332d-def4-4d1d-abfc-f2567b32b798\",\n        \"438ea907-f449-4e21-b06d-23740755ac3b\"\n      ],\n      \"XinWenProcess\": [\n        \"06a6d648-a46f-484f-a34c-a4dcf0c438d4\",\n        \"5f73a569-d927-44be-bfb8-332a026b3f20\"\n      ],\n      \"XinWenCategory\": \"bpm1\",\n      \"SFWCategory\": \"默认分类\",\n      \"QinJiaProcess\": [\"0731ab1f-d6a9-4864-ab96-7fa3ebacf6c0\"],\n      \"ChuChaiProcess\": [\"a216903c-08e2-41ae-96b4-7ff93353877f\",\"925ef256-c05f-4c7d-8e80-933d74323e5d\"],\n      \"XinWenPictureURL\": \"https://bpm.chinahho.cn:2005/customer/api/file-centre/v1/document-services/download/local?FilePath=\",\n      \"sendShortMsgProcessIds\": [ \"36cc7a7f-9d80-401f-97e5-2bf1f4ad9bc3\" ]\n    }\n  },\n  \"matrixAccessToken\": {\n      \"enable\": false,\n      \"tokenExpire\": \"100\",\n      \"serverExpire\": \"100\",\n      \"whiteList\" : {\n        \"/v1/SFW/MySW\": [\"Get\"],\n        \"/v1/SFW/MyFW\": [\"Get\"],\n        \"/v1/SFW/PushNews\": [\"Post\"]\n      }\n  },\n  \"MinioSettings\": {\n      \"Endpoint\": \"*************:9000\",\n      \"AccessKey\": \"root\",\n      \"SecretKey\": \"root1234\",\n      \"Bucket\": \"userfiles\",\n      \"RootFolder\": \"bpm\",\n      \"ViewHost\": \"https://portal.chinahho.cn:2006/dms\"\n  }\n}','0f65c3dcc1bb7089a431ac745d48da24','2023-09-08 08:41:36','2024-11-08 12:15:43',NULL,'*************','','shuiwu','','','','json',''),(10,'gateway.config.json','gateway','{\n  \"port\": 20050,\n  \"limit\": \"200mb\",\n  \"proxyTimeout\": 60000,\n  \"routeType\": \"redis\",\n  \"routes\": {\n  },\n  \"redis\": {\n    \"host\": \"*************\",\n    \"port\": 6380,\n    \"db\": 8,\n    \"password\": \"87htd2NxIQ0YguE9\"\n  },\n  \"token\": {\n    \"expires\": 43200,\n    \"secret\": \"movitech-jwt\",\n    \"whiteList\": [\n      \"799e6e124ad95e09b055ae8c8cc53d7f\"\n    ],\n    \"forJobs\": [\n      \"nCBYEUvkcgFFGFCRcHm3Lizu\"\n    ]\n  },\n  \"OcelotAuthOption\": {\n    \"Host\": \"http://*************:32065/connect/Token\",\n    \"UserName\": \"Admin\",\n    \"Password\": \"12345678\",\n    \"GrantType\": \"client_credentials\",\n    \"UrlPrefix\": \"integration-ocelot\"\n  }  \n}','2f4e87c1581166f3eb304e332e39c2a6','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(11,'medusa.service.report.entrance.appsetting.json','report','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Debug\"\n    }\n  },\n  \"AllowedHosts\": \"*\",\n  \"Persistence\": {\n    \"ReportBoost\": {\n            \"DbType\": \"SqlServer\",\n            \"ConnectionString\": \"server=*************;Database=ReportCenter_Dev;Integrated Security=False;User ID=sa;Password=*********;\"\n    }\n  },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"30\"\n    },\n  \"PlatformSettings\": {\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\"\n  }\n}\n','be2567e81e9d1f4f4a3aacb5631960b8','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(12,'medusa.service.dynamicapi.appsettings.json','programcenter','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Information\",\n      \"Microsoft\": \"Warning\",\n      \"Microsoft.Hosting.Lifetime\": \"Information\"\n    }\n  },\n  \"AllowedHosts\": \"*\",  \n  \"Persistence\": {\n    \"DynamicApi\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Port=3306;Database=DynamicApi;uid=bpm;Pwd=*****************"    \n    }\n  }\n}\n','a70bd200387dee939a923b781503591f','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(13,'zh.json','i18n','{\n  \"registrationFailed\": \"产品注册失败\",\n  \"notRegistered\": \"产品未注册\",\n  \"notFound\": \"{0}数据不存在！\",\n  \"notFoundFunction\": \"找不到实现的方法！\",\n  \"entityNotFound\": \"表实体不存在！\",\n  \"modelCheck\": \"{0}不能为空！\",\n  \"deleteCheck\": \"请先删除{0}子数据！\",\n  \"existNameOrCore\": \"名称或编码不能重复！\",\n  \"existCore\": \"编码不能重复！\",\n  \"main-title\": {\n    \"top-title\": \"BPM管理后台\"\n  },\n  \"instance\": {\n    \"notice\": {\n      \"version-inconsistency\": \"当前业务数据已发生变更,请到业务系统中重新核实提交!\",\n      \"data-exists\": \"流程数据已存在，不能发起,请检查BOID是否重复!\",\n      \"data-repeat-submit\":\"请勿重复提交!\",\n      \"data-repeat-recall\":\"请勿重复撤回!\",\n      \"data-repeat-delay\":\"请勿重复延时!\",\n      \"data-invalid-resubmit\": \"流程{0}已作废，请勿重复提交!\",\n      \"data-approve-resubmit\": \"流程{0}已审批，请勿重复提交!\",\n      \"no-allowed-recall\":\"不允许撤回!\"\n    }\n  },\n  \"framework\": {\n    \"enabled\": \"启用\",\n    \"disabled\": \"禁用\",\n    \"loading\": \"加载中\",\n    \"favorite\": \"常用流程\",\n    \"helper\": \"流程助手\",\n    \"stop-impersonate\": \"停止模拟\",\n    \"authorization\": \"流程授权\",\n    \"handover\": \"待办移交\",\n    \"proxy\": \"发起代理\",\n    \"logout\": \"退出\",\n    \"state-timeout\": \"登录超时\",\n    \"state-timeout-relogin\": \"登录超时，请重新登录。\",\n    \"state-timeout-relogin-help\": \"点击“确定”按钮，前往登录页面重新登录。\",\n    \"request-failed\": \"请求失败\",\n    \"upload-failed\": \"上传失败\",\n    \"notification\": \"提示信息\",\n    \"nonpermission\": \"你没有权限访问！\",\n    \"add\": \"添加\",\n    \"export\": \"导出\",\n    \"delete\": \"删除\",\n    \"delete-info\": \"确定要删除？\",\n    \"delete-info-param\": \"确定要删除{{value}}？\",\n    \"recall-info\": \"确定要撤回？\",\n    \"save\": \"保存\",\n    \"load-more\": \"加载更多\",\n    \"create\": \"新建\",\n    \"ok\": \"确定\",\n    \"operate\": \"操作\",\n    \"cancel\": \"取消\",\n    \"empty\": \"无\",\n    \"unauthorized\": \"你没有此页面的访问权限\",\n    \"prepositions\": {\n      \"s\": \"的\",\n      \"space\": \"\",\n      \"colon\": \"：\",\n      \"hour\": \"小时\",\n      \"total\": \"共\",\n      \"items\": \"条数据\"\n    },\n    \"headerUserNotfound\": \"头信息中缺少用户信息，请将用户信息base64编码后放入Header[\\\"current_user\\\"]中。\"\n  },\n  \"boost\": {\n    \"layout\": {\n      \"home\": \"首页\",\n      \"logout\": \"登出\",\n      \"refreshCache\": \"刷新缓存\",\n      \"login\": {\n        \"form\": {\n          \"title\": \"盟拓BPM流程管理平台\",\n          \"user-input\": \"用户名\",\n          \"psd-input\": \"密码\",\n          \"user-title\": \"用户登录\",\n          \"login-btn\": \"登录\",\n          \"code\": \"验证码\",\n          \"code-change\": \"换一张\"\n        },\n        \"timeout\": {\n          \"title\": \"登录超时\",\n          \"message\": \"即将跳转至登录页面...\"\n        }\n      }\n    },\n    \"utils\": {\n      \"selectPromptImage\": \"/assets/images/no-result-tips.png\"\n    }\n  },\n  \"frontButtons\": {\n    \"post\": \"提交\",\n    \"save\": \"保存\",\n    \"cancel\": \"作废\",\n    \"reject\": \"退回\"\n  },\n  \"buttons\": {\n    \"read\": \"查看\",\n    \"add\": \"添加\",\n    \"agent\": \"代理\",\n    \"back\": \"返回\",\n    \"cancel\": \"取消\",\n    \"delete\": \"删除\",\n    \"edit\": \"编辑\",\n    \"export\": \"导出\",\n    \"import\": \"导入\",\n    \"handover\": \"转交\",\n    \"iam\": \"权限\",\n    \"ok\": \"确定\",\n    \"reset\": \"重置\",\n    \"save\": \"保存\",\n    \"search\": \"搜索\",\n    \"searching\": \"搜索中\",\n    \"user\": \"人员\",\n    \"notUser\": \"非人员\",\n    \"parameterType\": \"参数类别\",\n    \"startUserLogic\": \"发起人逻辑取人\",\n    \"unStartUserLogic\": \"非发起人逻辑取人\",\n    \"cancel-modify\": \"取消修改\",\n    \"chooseUser\": \"选人\",\n    \"impersonate\": \"模拟登录\",\n    \"form\": \"表单编辑\",\n    \"node\": \"节点编辑\",\n    \"addNode\": \"新增节点\",\n    \"up\": \"上移\",\n    \"down\": \"下移\",\n    \"move\": \"跳转至此\",\n    \"repush\": \"重推\",\n    \"publish\": \"发布\",\n    \"save-publish\": \"保存并发布\",\n    \"refresh\": \"刷新\",\n    \"create\": \"新建\",\n    \"choose\": \"选择\",\n    \"download\": \"下载\",\n    \"upload\": \"上传\",\n    \"all\": \"全部\",\n    \"published\": \"已发布\",\n    \"unpublished\": \"未发布\",\n    \"changing\": \"修改中\",\n    \"user-iam\": \"角色成员\",\n    \"menu-iam\": \"菜单权限\",\n    \"data-iam\": \"数据权限\",\n    \"createMatrix\": \"生成矩阵\",\n    \"binding\": \"绑定\",\n    \"unbundling\": \"解绑\",\n    \"enable\": \"启用\",\n    \"disable\": \"禁用\",\n    \"batchDisable\": \"批量禁用\",\n    \"newVersion\": \"设计新版本\",\n    \"startFieldRight\": \"发起节点字段权限\",\n    \"endFieldRight\": \"结束节点字段权限\",\n    \"fieldRight\": \"字段权限\",\n    \"query\": \"查询\",\n    \"design\": \"设计\",\n    \"close\": \"关闭\",\n    \"finish\": \"完成\",\n    \"batchAdd\": \"批量添加\",\n    \"syncAuth\": \"同步授权\",\n    \"retry\": \"重试\",\n    \"batchRetry\": \"批量重试\",\n    \"promote\": \"立即触发\",\n    \"batchDelete\": \"批量删除\",\n    \"archiveAll\": \"归档所有分类\",\n    \"archive\": \"归档当前分类\",\n    \"addEntityObject\": \"添加业务对象\",\n    \"batch-void\": \"批量作废\",\n    \"batch-cc\": \"批量抄送\",\n    \"process-intervention\": \"流程干预\",\n    \"interv-step-user\": \"步骤人员干预\",\n    \"interv-form-data\": \"表单数据干预\",\n    \"interv-approval-comments\": \"审批意见干预\",\n    \"intervention-record\": \"干预记录查看\",\n    \"process-info\": \"流程信息查看\",\n    \"AuthApi\": \"授权Api\",\n    \"authApiGroup\": \"授权Api组合\",\n    \"offline\": \"下线\",\n    \"nextStep\": \"下一步\",\n    \"previousStep\": \"上一步\",\n    \"node-event\": \"节点事件\",\n    \"submit-after\": \"提交后事件\",\n    \"batch-import\":\"批量导入\",\n    \"batch-export\":\"批量导出\",\n    \"exportApi\": \"开放API\",\n    \"refreshApi\": \"刷新API\",\n    \"bindApi\": \"绑定API\",\n    \"batch-auth\": \"批量组织授权\",\n    \"imort-authorization\": \"导入权责表\",\n    \"export-authorization\": \"导出权责表\",\n    \"imort-other\": \"导入链接/继承\",\n    \"batch-invalid\": \"批量设置无效\",\n    \"batch-publish\": \"批量发布\",\n    \"copyLink\": \"复制预览地址\",\n    \"copyDownload\": \"复制下载地址\"\n  },\n  \"paginations\": {\n    \"total\": \"共 {{value}} 条\"\n  },\n  \"controls\": {\n    \"input\": \"请输入\",\n    \"select\": \"请选择\",\n    \"upload\": \"请上传\",\n    \"selectItems\": \"请勾选数据\",\n    \"serachText\": \"请输入查询信息\"\n  },\n  \"units\": {\n    \"hour\": \"小时\"\n  },\n  \"messages\": {\n    \"delete\": \"确定要删除当前数据吗?\",\n    \"cancel-modify\": \"确定要取消修改数据吗?\",\n    \"interven\": \"确定要跳转至此吗?\",\n    \"success\": \"操作成功\",\n    \"fail\": \"操作失败\",\n    \"exists\": \"已经存在该{{value}}\",\n    \"reset\": \"确定要重置当前数据吗?\",\n    \"publish\": \"确定要发布吗？\",\n    \"offline\": \"确定要下线吗？\",\n    \"intervention-save\": \"干预保存后，约5秒内生效。\",\n    \"canNotEmpty\": \"{{value}}不能为空\",\n    \"copySuccess\":\"复制成功\",\n    \"copyFailed\":\"复制失败\"\n  },\n  \"platform\": {\n    \"role-list\": \"角色列表\",\n    \"columns\": {\n      \"organization\": \"组织\",\n      \"role\": \"角色\",\n      \"des\": \"描述\",\n      \"user\": \"人员\",\n      \"operate\": \"操作\"\n    },\n    \"routes\": {\n      \"user\": \"用户管理\",\n      \"log\": \"日志管理\",\n      \"operationLog\": \"操作日志\",\n      \"organization\": \"组织构架\",\n      \"company\": \"公司信息维护\",\n      \"department\": \"部门信息维护\",\n      \"positionLevel\": \"岗位管理\",\n      \"system\": \"系统管理\",\n      \"dictionary\": \"数据字典\",\n      \"menu\": \"菜单\",\n      \"platform\": \"平台管理\",\n      \"home\": \"首页\",\n      \"login\": \"登录\",\n      \"loginout\": \"登出\",\n      \"role\": \"角色权限\",\n      \"roleMember\": \"角色人员\",\n      \"roleMenu\": \"角色菜单\",\n      \"userHome\": \"用户中心\",\n      \"userEdit\": \"用户编辑\",\n      \"userDetail\": \"用户详情\",\n      \"message\": \"消息中心\",\n      \"messageRule\": \"消息规则\",\n      \"messageTemplate\": \"消息模板\"\n    },\n    \"fields\": {\n      \"id\": \"ID\",\n      \"company\": \"公司\",\n      \"position\": \"岗位\",\n      \"department\": \"部门\",\n      \"organization\": \"组织\",\n      \"project\": \"项目\",\n      \"positionLevel\": \"岗位\",\n      \"role\": \"角色\",\n      \"leader\": \"负责人\",\n      \"phone\": \"电话\",\n      \"city\": \"城市\",\n      \"remark\": \"备注\",\n      \"code\": \"编码\",\n      \"name\": \"名称\",\n      \"user\": \"人员\",\n      \"menu\": \"菜单\",\n      \"englishName\": \"英文名称\",\n      \"chineseName\": \"中文名称\",\n      \"type\": \"类型\",\n      \"illustration\": \"说明\",\n      \"description\": \"描述\",\n      \"keyword\": \"关键字\",\n      \"operation\": \"操作\",\n      \"parameter\": \"参数\",\n      \"order\": \"排序\",\n      \"yes\": \"是\",\n      \"no\": \"否\",\n      \"detail\": \"详情\",\n      \"level\": \"层级\",\n      \"account\": \"账号\",\n      \"password\": \"密码\",\n      \"status\": \"状态\",\n      \"inUse\": \"是否有效\",\n      \"message\": \"消息\",\n      \"language\": \"语言\",\n      \"inStart\": \"是否开启\",\n      \"frequency\": \"频次\",\n      \"orgLevel\": \"组织类型\",\n      \"valid\": \"有效\",\n      \"inValid\": \"无效\",\n      \"primaryPosition\": \"主岗\",\n      \"roleMenu\": \"角色菜单\",\n      \"bizid\": \"业务Id\",\n      \"companyId\": \"公司Id\",\n      \"departmentId\": \"部门Id\",\n      \"positionUser\": \"岗位人员\",\n      \"cityId\": \"城市Id\",\n      \"date\": \"日期\",\n      \"organizationLevel\": \"组织级别\",\n      \"organizationBusinessType\": \"业态\",\n      \"captcha\": \"验证码\",\n      \"label\": \"维度\",\n      \"domainName\": \"业态名称\",\n      \"domainCode\": \"业态编码\",\n      \"domain\": \"业态\",\n      \"domainLeve\": \"层级\",\n      \"domainLevelCode\": \"层级编码\",\n      \"domainLevelName\": \"层级名称\",\n      \"domainLevelCList\": \"等级列表\",\n      \"domainLevelCName\": \"层级等级\",\n      \"standDomainLevelCode\": \"标准层级编码\",\n      \"standDomainLevelName\": \"标准层级名称\",\n      \"standDomainLevelWeight\": \"标准层级权重\",\n      \"isPcShow\": \"PC显示\",\n      \"isMobileShow\": \"APP显示\",\n      \"levelMaintenance\": \"等级维护\",\n      \"items\": \"字典项\",\n      \"itemsMaintenance\": \"字典项维护\",\n      \"isSysDefine\": \"系统内置\",\n      \"isShrink\": \"是否在更多区域\",\n      \"style\": \"样式\",\n      \"enabled\": \"是否启用\",\n      \"category\": \"类别\",\n      \"function\": \"实现方法\",\n      \"hasUsed\": \"数据已被使用\",\n      \"businessSystem\": \"业务系统\",\n      \"isCanView\": \"是否能查看\",\n      \"createDate\": \"创建时间\",\n      \"createUser\": \"创建人\",\n      \"modifyDate\": \"修改时间\",\n      \"modifyUser\": \"修改人\",\n      \"isStandardRole\": \"是否标准角色\",\n      \"commonFile\": \"通用附件\",\n      \"application\": \"所属应用\"\n    },\n    \"application\": {\n      \"pageName\": \"应用维护\",\n      \"statusDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": \"0\"\n        }\n      ],\n      \"existedCode\": \"应用已经存在！\"\n    },\n    \"externalSystems\": {\n      \"domain\": \"域名\",\n      \"secretKey\": \"密钥\",\n      \"appKey\": \"公钥\",\n      \"dataValidationUrl\": \"数据校验Url\",\n      \"canStartInvalid\": \"允许发起作废\",\n      \"settingOfCreate\": \"发起成功回调\",\n      \"settingOfTaskCreate\": \"待办创建回调\",\n      \"settingOfAudit\": \"审批回调\",\n      \"settingOfApprove\": \"流程结束回调\",\n      \"apiTodoTaskBatchApprovalCallbackUrl\": \"待办中心待办批量审批回调\",\n      \"apiTodoTaskBatchTransferCallbackUrl\": \"待办中心待办转交审批回调\",\n      \"apiTodoMyStartUrgeCallbackUrl\": \"待办中心我发起催办回调\",\n      \"apiTodoMyStartWithdrawCallbackUrl\": \"待办中心我发起撤回回调\",\n      \"apiTodoArchivedTaskWithdrawCallbackUrl\": \"待办中心已办撤回回调\",\n      \"apiTodoDraftDeleteCallbackUrl\": \"待办中心草稿删除回调\",\n      \"systemTypeCodes\": \"系统类型\",\n      \"apiTodo\":\"API 待办中心\",\n      \"interface\": \"接口\",\n      \"params\": \"回调参数\",\n      \"code\": \"系统编码\",\n      \"name\": \"系统名称\",\n      \"baseInfo\": \"基本信息\",\n      \"generate\": \"生成密钥\",\n      \"preview\": \"预览\",\n      \"detail\": \"明细\",\n      \"registerInfo\": \"注册信息\",\n      \"apiRegister\": \"API 注册\",\n      \"mqRegister\": \"MQ 注册\",\n      \"serviceRegister\": \"WebService 注册\",\n      \"server\": \"服务器\",\n      \"port\": \"端口\",\n      \"publishMQ\": \"发布队列\",\n      \"receiveMQ\": \"订阅队列\",\n      \"apiType\": [\n        {\n          \"label\": \"WebService\",\n          \"value\": \"webservice\"\n        },\n        {\n          \"label\": \"WebApi\",\n          \"value\": \"webapi\"\n        }\n      ],\n      \"requestMethod\": [\n        {\n          \"label\": \"GET\",\n          \"value\": \"get\"\n        },\n        {\n          \"label\": \"POST\",\n          \"value\": \"post\"\n        }\n      ],\n      \"statusDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": \"0\"\n        }\n      ]\n    },\n    \"log\": {\n      \"projectName\": \"项目名\",\n      \"module\": \"模块\",\n      \"largeModule\": \"模块大类\",\n      \"smallModule\": \"模块小类\",\n      \"logType\": \"日志类型\",\n      \"firstFilterField\": \"过滤字段1\",\n      \"secondFilterField\": \"过滤字段2\",\n      \"lately\": \"最近\",\n      \"interval\": \"时段\",\n      \"time\": \"时间\",\n      \"message\": \"消息内容\",\n      \"detail\": \"消息详情\",\n      \"operator\": \"操作人\",\n      \"serverName\": \"服务名称\",\n      \"interfaceName\": \"接口名称\",\n      \"responseData\": \"响应数据\",\n      \"interfaceTime\": \"接口耗时\",\n      \"info\": \"消息详情\",\n      \"interfaceAddress\": \"接口地址\",\n      \"requestParam\": \"请求参数\",\n      \"requestBody\": \"请求数据\",\n      \"errorMessage\": \"异常信息\",\n      \"filter\": \"筛选\"\n    },\n    \"monitor\": {\n      \"service\": \"服务\",\n      \"middleWare\": \"中间件\",\n      \"html\": \"前端\",\n      \"address\": \"探针地址\",\n      \"mailAddress\": \"邮件地址\",\n      \"templeteCode\": \"邮件模板编码\",\n      \"cron\": \"表达式\",\n      \"failCount\": \"失败次数\",\n      \"type\": \"探针类型\",\n      \"typeDateset\": [\n        {\n          \"label\": \"服务\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"中间件\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"前端\",\n          \"value\": 2\n        }\n      ],\n      \"cronDateset\": [\n        {\n          \"label\": \"每隔一分钟\",\n          \"value\": \"*/1 * * * *\"\n        },\n        {\n          \"label\": \"每隔两分钟\",\n          \"value\": \"*/2 * * * *\"\n        },\n        {\n          \"label\": \"每隔三分钟\",\n          \"value\": \"*/3 * * * *\"\n        }\n      ]\n    },\n    \"company\": {\n      \"list\": \"公司列表\",\n      \"info\": \"公司信息\",\n      \"subsidiary\": \"子公司\",\n      \"topCompanies\": \"顶级公司\",\n      \"superiorCompany\": \"上级公司\",\n      \"address\": \"公司地址\",\n      \"contact\": \"联系人\",\n      \"fax\": \"传真\",\n      \"domain\": \"域名\",\n      \"typeDataset\": [\n        {\n          \"label\": \"集团\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"集团本部\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"事业部\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"事业部本部\",\n          \"value\": 4\n        },\n        {\n          \"label\": \"项目公司\",\n          \"value\": 5\n        }\n      ]\n    },\n    \"organization\": {\n      \"org\": \"组织\",\n      \"list\": \"组织列表\",\n      \"info\": \"组织信息\",\n      \"subdivision\": \"子组织\",\n      \"topOrganization\": \"顶级组织\",\n      \"isPrimary\": \"是否主岗\",\n      \"inUse\": \"是否可用\",\n      \"user\": \"人员\",\n      \"addCheck\": \"请选择组织\",\n      \"selectedOrganization\": \"已选择的组织\",\n      \"organizationUser\": \"组织人员\",\n      \"organizationCode\": \"组织编号\",\n      \"organizationName\": \"组织名称\",\n      \"organizationFullPathText\": \"组织名称全路径\",\n      \"organizationFullPathCode\": \"组织编码全路径\",\n      \"positionCode\": \"岗位编号\",\n      \"positionName\": \"岗位名称\",\n      \"positionInfo\": \"岗位信息\",\n      \"superiorOrganization\": \"上级组织\",\n      \"businessType\": \"业态\",\n      \"businessTypeLevel\": \"业态层级\",\n      \"positionType\": \"岗位类型\",\n      \"positionTypeDataset\": [\n        {\n          \"label\": \"普通岗位\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"分管领导\",\n          \"value\": 1\n        }\n      ]\n    },\n    \"position\": {\n      \"addCheck\": \"请选择公司或部门！\"\n    },\n    \"positionLevel\": {\n      \"shortName\": \"岗位缩写\",\n      \"setting\": \"岗位设定\",\n      \"list\": \"岗位列表\",\n      \"companyName\": \"公司名称\",\n      \"departmentName\": \"部门名称\",\n      \"user\": \"人员\"\n    },\n    \"dictionary\": {\n      \"category\": \"类型列表\",\n      \"list\": \"字典列表\",\n      \"edit\": \"类型编辑\",\n      \"topType\": \"顶级类型\",\n      \"subtype\": \"子类型\",\n      \"superiorParameter\": \"上级参数\",\n      \"value\": \"参数值\",\n      \"selectCheck\": \"请选择类型！\",\n      \"deleteCheck\": \"请先删除子数据！\"\n    },\n    \"menu\": {\n      \"link\": \"菜单链接\",\n      \"nameCheck\": \"请先保存菜单数据！\",\n      \"action\": \"功能权限\",\n      \"permission\": \"数据权限\",\n      \"list\": \"菜单列表\",\n      \"info\": \"菜单信息\",\n      \"icon\": \"图标\",\n      \"route\": \"路由\",\n      \"isMobile\": \"是否移动端\",\n      \"hidden\": \"菜单隐藏\",\n      \"path\": \"路径\",\n      \"codePath\": \"编码路径\",\n      \"namePath\": \"名称路径\",\n      \"application\": \"应用\",\n      \"subModule\": \"子模块\",\n      \"topModule\": \"顶级模块\",\n      \"typeDataset\": [\n        {\n          \"label\": \"模块\",\n          \"value\": \"M\"\n        },\n        {\n          \"label\": \"页面\",\n          \"value\": \"P\"\n        }\n      ]\n    },\n    \"role\": {\n      \"keyword\": \"名称(编码)\",\n      \"permission\": \"菜单权限\",\n      \"show\": \"显示\",\n      \"action\": \"功能权限\",\n      \"inUse\": \"状态\",\n      \"user\": \"人员\",\n      \"tagType\": \"标签分类\",\n      \"orgLevel\": \"组织等级\",\n      \"businessType\": \"业态\",\n      \"tag\": \"标签\",\n      \"editTag\": \"编辑标签/业态\",\n      \"includeChildren\": \"是否包含子级\",\n      \"dataRights\": \"数据权限\",\n      \"organizationLevelLabel\": \"组织层级\",\n      \"organizationGroupLabel\": \"行安全性\",\n      \"personal\": \"本人\",\n      \"custom\": \"自定义\",\n      \"organization\": \"选择组织\",\n      \"exists\": \"已存在组织\",\n      \"dataRightsType\": [\n        {\n          \"label\": \"组织层级\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"行安全性\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"本人\",\n          \"value\": 2\n        }\n      ],\n      \"inUseDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": 0\n        }\n      ],\n      \"memberDataset\": [\n        {\n          \"label\": \"人员\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"公司\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"部门\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"岗位\",\n          \"value\": 4\n        }\n      ],\n      \"dataAuthorityItems\": [\n        {\n          \"label\": \"全部\",\n          \"value\": \"qb\"\n        },\n        {\n          \"label\": \"本人\",\n          \"value\": \"br\"\n        },\n        {\n          \"label\": \"本部门及下级部门\",\n          \"value\": \"bbmjxjbm\"\n        },\n        {\n          \"label\": \"指定部门\",\n          \"value\": \"zdbm\"\n        }\n      ]\n    },\n    \"user\": {\n      \"company\": \"所属公司\",\n      \"department\": \"所属部门\",\n      \"position\": \"所属岗位\",\n      \"role\": \"所属角色\",\n      \"addTime\": \"创建日期\",\n      \"name\": \"姓名\",\n      \"agentEndTime\": \"代理结束时间\",\n      \"agenter\": \"代理人\",\n      \"dynamicSetting\": \"动态设置\",\n      \"currentUser\": \"当前用户\",\n      \"phone\": \"手机号\",\n      \"baseInfo\": \"基本信息\",\n      \"extendInfo\": \"扩展信息\",\n      \"reportRelation\": \"汇报关系\",\n      \"orgRelation\": \"组织关系\",\n      \"roleRelation\": \"权限关系\",\n      \"isPrimaryPosition\": \"是否主岗\",\n      \"lock\": \"锁定状态\",\n      \"gender\": \"性别\",\n      \"man\": \"男\",\n      \"girl\": \"女\",\n      \"birthday\": \"生日\",\n      \"email\": \"邮箱\",\n      \"lessOneOragization\": \"至少选择一个组织！\",\n      \"lessOneRole\": \"至少选择一个角色！\",\n      \"workNumber\": \"工号\",\n      \"upperUser\": \"直属上级\",\n      \"jobGrade\": \"职级\",\n      \"education\": \"学历\",\n      \"joinDate\": \"入职时间\",\n      \"leaderName\": \"上级名称\",\n      \"businessLineName\": \"业务条线\",\n      \"lineLeader\": \"条线领导\",\n      \"genderDataset\": [\n        {\n          \"label\": \"男\",\n          \"value\": \"M\"\n        },\n        {\n          \"label\": \"女\",\n          \"value\": \"F\"\n        }\n      ],\n      \"lockDataset\": [\n        {\n          \"label\": \"无锁定\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"临时锁定\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"长期锁定\",\n          \"value\": 2\n        }\n      ],\n      \"statusDataset\": [\n        {\n          \"label\": \"无效\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"有效\",\n          \"value\": 1\n        }\n      ],\n      \"workingStatusDataset\": [\n        {\n          \"label\": \"在职\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"离职\",\n          \"value\": 0\n        }\n      ],\n      \"workingState\": \"在职状态\",\n      \"dataSecurity\": \"数据安全性\",\n      \"loginCheck\": \"用户账号或密码错误！\",\n      \"captchaCheck\": \"验证码错误或已过期！\",\n      \"impersonateCheck\": \"模拟登录失败！\",\n      \"lessOnePrimaryPosition\": \"每个用户至少需要一个主岗！\",\n      \"notFound\": \"用户不存在！\",\n      \"existAccount\": \"账号或者工号已经被使用！\",\n      \"existReportRelation\": \"存在同样的汇报关系！\",\n      \"leave\": \"离职\"\n    },\n    \"message\": {\n      \"message\": \"消息\",\n      \"keyValue\": \"业务关键字\",\n      \"msgType\": \"消息类型\",\n      \"category\": \"消息分类\",\n      \"fromSys\": \"发起系统\",\n      \"msgTitle\": \"消息标题\",\n      \"msgBody\": \"消息内容\",\n      \"sendTo\": \"收件人\",\n      \"priority\": \"优先级\",\n      \"sendTime\": \"发送时间\",\n      \"sendingStatus\": \"发送状态\",\n      \"tryTimes\": \"尝试次数\",\n      \"messageTemplate\": \"消息模板\",\n      \"templateCode\": \"编码\",\n      \"templateName\": \"名称\",\n      \"templateContent\": \"内容\",\n      \"templateType\": \"类型\",\n      \"templateSubject\": \"标题\",\n      \"sendStatus\": [\n        {\n          \"label\": \"待发送\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"发送成功\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"发送失败\",\n          \"value\": -1\n        }\n      ],\n      \"messageTypeItems\": [\n        {\n          \"label\": \"短信\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"邮件\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"微信\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"钉钉\",\n          \"value\": 4\n        }\n      ],\n      \"dataStatus\": [\n        {\n          \"label\": \"禁用\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"启用\",\n          \"value\": 1\n        }\n      ],\n      \"templateTypeItems\": [\n        {\n          \"label\": \"短信\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"邮件\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"微信\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"钉钉\",\n          \"value\": 4\n        }\n      ]\n    },\n    \"init\": {\n      \"getglobalbtn\": \"获取全局配置\",\n      \"global\": \"全局\",\n      \"customer\": \"前台\",\n      \"mobile\": \"移动端\",\n      \"admin\": \"后台\",\n      \"name\": \"系统名称\",\n      \"logo\": \"Logo\",\n      \"logoThumbnail\": \"Logo收缩图\",\n      \"icon\": \"Icon\",\n      \"enableWatermark\": \"启用水印\",\n      \"watermarkType\": \"水印类型\",\n      \"watermarkTypeDataset\": [\n        {\n          \"label\": \"用户名称 + 账号 + 日期\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"自定义\",\n          \"value\": \"99\"\n        }\n      ],\n      \"watermark\": \"水印\",\n      \"theme\": \"主题\",\n      \"copyright\": \"版权\",\n      \"help\": \"帮助\",\n      \"trafficLightUnit\": \"红绿灯单位\",\n      \"trafficLightUnitDataset\": [\n        {\n          \"label\": \"小时\",\n          \"value\": \"hour\"\n        },\n        {\n          \"label\": \"天\",\n          \"value\": \"day\"\n        }\n      ],\n      \"initProcessEnableOrgFilter\": \"发起流程启用组织筛选\",\n      \"helpText\": {\n        \"name\": \"页面的title\",\n        \"logo\": {\n          \"customer\": \"页面的Logo,推荐上传宽高在200*48以下的png图片\",\n          \"mobile\": \"页面的Logo\",\n          \"admin\": \"要求：上传 128px * 30px 的jpeg,png或svg图片\"\n        },\n        \"logoThumbnail\": {\n          \"admin\": \"要求：上传 30px * 30px 的jpeg,png或svg图片\"\n        },\n        \"icon\": \"要求：上传 32px * 32px 或 64px * 64px 的icon文件\",\n        \"theme\": \"页面的主题颜色\",\n        \"copyright\": \"页面是否显示版权信息\",\n        \"help\": \"页面的帮助文本\",\n        \"trafficLightUnit\": \"系统的红绿灯单位\",\n        \"initProcessEnableOrgFilter\": \"发起流程是否启用组织筛选\",\n        \"canGetGloablSetting\": \"您可以从全局配置中获取以下信息：\"\n      },\n      \"message\": {\n        \"onlyJpg\": \"只能上传jpeg、png或svg图片\",\n        \"imageSize\": \"图片大小必须小于150KB\",\n        \"onlyIcon\": \"只能上传icon文件\"\n      }\n    },\n    \"gateway\": {\n      \"name\": \"名称\",\n      \"address\": \"地址\",\n      \"operation\": \"操作\"\n    },\n    \"dataPermission\": {\n      \"permission\": \"数据权限\",\n      \"descripton\": \"说明\",\n      \"dataRequied\": \"条件配置不完整\",\n      \"filterCondition\": \"过滤条件\",\n      \"conditionLogic\": \"过滤条件逻辑\",\n      \"addCondition\": \"添加条件\",\n      \"statusDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": 0\n        }\n      ],\n      \"operators\": [\n        {\n          \"label\": \"等于\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"包含于\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"开始于\",\n          \"value\": 3\n        }\n      ],\n      \"conditionType\": [\n        {\n          \"label\": \"固定值\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"系统变量\",\n          \"value\": 2\n        }\n      ],\n      \"systemVariables\": [\n        {\n          \"label\": \"当前用户\",\n          \"value\": \"currentUser\"\n        },\n        {\n          \"label\": \"当前部门\",\n          \"value\": \"currentDepart\"\n        }\n      ],\n      \"existedCode\": \"编号已存在！\"\n    },\n    \"apis\": {\n      \"moduleCode\": \"模块\",\n      \"path\": \"地址\",\n      \"summary\": \"概要描述\",\n      \"operationId\": \"方法\",\n      \"permission\": \"数据权限\",\n      \"impostApi\": \"导入接口\",\n      \"apiJson\": \"接口数据\",\n      \"dataError\": \"数据格式错误\",\n      \"importFaild\": \"导入失败\",\n      \"tableRelation\": \"关联表\",\n      \"moduleCodeDataset\": [\n        {\n          \"label\": \"系统后台\",\n          \"value\": \"platform\"\n        },\n        {\n          \"label\": \"流程管理\",\n          \"value\": \"process\"\n        }\n      ],\n      \"notNull\": \"导入数据不能为空！\",\n      \"nullModuleCode\": \"模块编码不能为空\"\n    },\n    \"dataPermissionTable\": {\n      \"table\": \"表\",\n      \"tableName\": \"表名\",\n      \"descCn\": \"中文描述\",\n      \"descEn\": \"英文描述\",\n      \"column\": \"列信息\",\n      \"columnName\": \"列名\",\n      \"columnType\": \"列类型\",\n      \"addColumn\": \"添加列\",\n      \"columnTypes\": [\n        {\n          \"label\": \"字符\",\n          \"value\": \"string\"\n        },\n        {\n          \"label\": \"数字\",\n          \"value\": \"number\"\n        },\n        {\n          \"label\": \"GUID\",\n          \"value\": \"uniqueidentifier\"\n        }\n      ],\n      \"existedName\": \"表名已存在！\"\n    },\n    \"tableRelations\": {\n      \"tableRelation\": \"表映射关系\",\n      \"tableName\": \"表名\",\n      \"columnName\": \"列名\",\n      \"targetTableName\": \"目标表名\",\n      \"targetColumnName\": \"目标列名\",\n      \"statusDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": 0\n        }\n      ],\n      \"existedRelation\": \"表映射关系已存在！\"\n    },\n    \"product\": {\n      \"registration\": {\n        \"registe\": \"注册\",\n        \"productKey\": \"产品密钥\",\n        \"licenseFile\": \"授权文件\",\n        \"productInfo\": \"产品信息\",\n        \"productId\": \"产品编号\",\n        \"productName\": \"产品名称\",\n        \"productVersion\": \"产品版本\",\n        \"customerName\": \"客户名称\",\n        \"licenseType\": \"授权类型\",\n        \"expirationDate\": \"过期日期\",\n        \"licenseStatus\": \"授权状态\",\n        \"manufacturer\": \"制造厂商\",\n        \"active\": \"可用\",\n        \"expiration\": \"已过期\",\n        \"trial\": \"试用版\",\n        \"commercial\": \"商用版\"\n      }\n    },\n    \"sso\": {\n      \"unauthorized\": \"未授权!\"\n    },\n    \"commonRole\": {\n      \"keyword\": \"名称(编码)\",\n      \"permission\": \"数据权限\",\n      \"show\": \"显示\",\n      \"action\": \"功能权限\",\n      \"inUse\": \"状态\",\n      \"user\": \"人员\",\n      \"tagType\": \"标签分类\",\n      \"orgLevel\": \"组织等级\",\n      \"businessType\": \"业态\",\n      \"tag\": \"标签\",\n      \"valid\": \"有效\",\n      \"invalid\": \"无效\",\n      \"descripiton\": \"角色描述\",\n      \"hasUsed\": \"已被使用\",\n      \"process\": \"所在流程\",\n      \"postNameCode\": \"岗位名称或代码\",\n      \"btId\": \"BTID\",\n      \"info\": \"组织信息\",\n      \"codeOrName\": \"角色编码或角色名称\",\n      \"Notfound\": \"待导入的角色人员数据不存在\",\n      \"Import\": \"角色人员数据导入\",\n      \"OrgDomainError\": \"组织业态与sheet名不匹配\",\n      \"OrgError\": \"此组织不存在\",\n      \"CommonRoleNotInThisLevlel\": \"角色不属于当前业态或层级，或角色编码不存在\",\n      \"CommonRoleNameNotNull\": \"角色编码存在时，角色名称不能为空\",\n      \"CommonRoleCodeNotNull\": \"角色名称存在时，角色编码不能为空\",\n      \"CommonRoleNameCodeNotMatching\": \"角色编码与角色名称不匹配\",\n      \"RoleUserNameNotNull\": \"人员账号存在时，人员名称不能为空\",\n      \"RoleUserCodeNotNull\": \"人员名称存在时，人员账号不能为空\",\n      \"RoleUserCodeNameNotMatching\": \"人员账号和人员名称不匹配\",\n      \"RoleUserNameNotInvalid\": \"人员为无效人员或离职状态人员\",\n      \"NoRoleImport\": \"您没有导入该组织数据的权限\",\n      \"UserNotFound\": \"用户不存在\",\n      \"ComonRoleDelayEffect\":\"注意：角色人员调整将延迟3分钟生效！\",\n      \"inUseDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": \"true\"\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": \"false\"\n        }\n      ],\n      \"memberDataset\": [\n        {\n          \"label\": \"人员\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"公司\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"部门\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"岗位\",\n          \"value\": 4\n        }\n      ],\n      \"batchExport\": \"批量导出\",\n      \"batchImport\": \"批量导入\"\n    },\n    \"matrix\": {\n      \"source\": \"数据来源\",\n      \"matrix\": \"矩阵\",\n      \"dimension\": \"维度\",\n      \"dictionary\": \"请输入字典编码\",\n      \"config\": \"配置信息\",\n      \"preview\": \"生成矩阵\",\n      \"sync\": \"同步视图维度\",\n      \"sync-dictionary\": \"同步字典维度\",\n      \"inUse\": \"状态\",\n      \"inUseDataset\": [\n        {\n          \"label\": \"有效\",\n          \"value\": \"true\"\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": \"false\"\n        }\n      ],\n      \"role\": \"角色\",\n      \"status\": \"状态\",\n      \"statusDataset\": [\n        {\n          \"label\": \"草稿\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"无效\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"已发布\",\n          \"value\": \"1\"\n        }\n      ]\n    },\n    \"tag\": {\n      \"keyword\": \"名称或编码\",\n      \"category\": \"标签分类\"\n    },\n    \"process\": {\n      \"no\": \"序号\",\n      \"processNo\": \"流程编号\",\n      \"title\": \"流程主题\",\n      \"applicant\": \"申请人\",\n      \"creatDate\": \"申请时间\",\n      \"status\": \"状态\",\n      \"residenceTime\": \"停留时长\",\n      \"formEdit\": \"表单编辑\",\n      \"nodeEdit\": \"节点编辑\",\n      \"step\": \"步骤名称\",\n      \"approver\": \"审批人\",\n      \"typeName\": \"待办类型\",\n      \"dockingStatus\": \"推送状态\",\n      \"dockingMessage\": \"推送消息\",\n      \"dockingDate\": \"推送时间\",\n      \"lastDockingDate\": \"最近推送时间\",\n      \"dockingCount\": \"推送次数\",\n      \"canNotNullify\": \"流程：{{value}}不能作废\",\n      \"selectProcess\": \"请选择流程\",\n      \"nullify\": \"作废\",\n      \"batchNullify\": \"批量作废\",\n      \"nullifyComment\": \"作废原因\",\n      \"del\": \"删除\",\n      \"batchDel\": \"批量删除\",\n      \"batchDelComment\": \"删除原因\",\n      \"notice\": \"抄送\",\n      \"batchNotice\": \"批量抄送\",\n      \"authorizationTable\": \"权责表\",\n      \"processIntervention\": \"流程干预\",\n      \"processInfo\": \"流程信息\",\n      \"formDataIntervention\": \"表单数据\",\n      \"approveHistoryIntervention\": \"审批意见\",\n      \"formData\": \"表单数据\",\n      \"user\": \"人员\",\n      \"stepUser\": \"步骤人员\",\n      \"interventionHistory\": \"干预记录\",\n      \"dataNotBeNull\": \"数据不能为空\",\n      \"jsonFormatError\": \"Json 格式异常\",\n      \"keyWordNotBeModfiy\": \"关键字信息不能修改\",\n      \"keyWords\": \"关键参数\",\n      \"keywordsWarning\": \"(请勿调整表单关键参数，如需调整请退回流程！)\",\n      \"attachmentMaintenance\":\"附件维护\"\n    },\n    \"jobManage\": {\n      \"jobCatetory\": \"调度分类\",\n      \"jobName\": \"任务名称\",\n      \"keyWords\": \"关键字\",\n      \"createTime\": \"创建时间\",\n      \"finishTime\": \"完成时间\",\n      \"jobData\": \"任务数据\",\n      \"jobResult\": \"执行结果\",\n      \"jobDetail\": \"任务明细\",\n      \"jobStatus\": [\n        {\n          \"label\": \"队列中\",\n          \"value\": \"Enqueued\"\n        },\n        {\n          \"label\": \"成功\",\n          \"value\": \"Succeeded\"\n        },\n        {\n          \"label\": \"失败\",\n          \"value\": \"Failed\"\n        }\n      ]\n    },\n    \"cacheManage\": {\n      \"cacheCode\": \"缓存Key\",\n      \"desc\": \"描述\",\n      \"createTime\": \"最后刷新时间\",\n      \"initSystemCache\": \"初始化系统缓存\"\n    },\n    \"internalApi\": {\n      \"apiInfo\": \"API信息\",\n      \"apiCategory\": \"API分类\",\n      \"system\": \"所属系统\",\n      \"category\": \"所属分类\",\n      \"systemCode\": \"系统代码\",\n      \"systemName\": \"系统名称\",\n      \"requestMethod\": \"请求方式\",\n      \"requestPath\": \"请求路径\",\n      \"summary\": \"描述\",\n      \"export\": \"是否开放\",\n      \"requestParams\": \"请求参数\",\n      \"requestBodySchema\": \"请求体结构\",\n      \"responseSchema\": \"返回体结构\",\n      \"paramName\": \"参数名称\",\n      \"paramType\": \"参数类型\",\n      \"dataType\": \"数据类型\",\n      \"paramDesc\": \"参数描述\",\n      \"required\": \"必填\",\n      \"requestBodyType\": \"请求体类型\",\n      \"requestBody\": \"请求体\",\n      \"responseBodyType\": \"返回体类型\",\n      \"responseBody\": \"返回体\",\n      \"categoryName\": \"分类名称\",\n      \"exportDataSource\": [  \n        {\n          \"label\": \"是\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"否\",\n          \"value\": 0\n        }\n      ],\n      \"messages\":{\n        \"lessOneApi\": \"至少选择一个API\",\n        \"choseSystem\": \"请选择需要刷新的系统\"\n      }        \n    },\n    \"holiday\":{\n      \"maintain\": \"节假日维护\",\n      \"date\": \"日期\",\n      \"type\": \"类型\",\n      \"remark\": \"备注\",\n      \"name\":\"节假日\",\n      \"weekday\": \"工作日\",\n      \"statusDataset\": [\n        {\n          \"label\": \"节假日\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"工作日\",\n          \"value\": 0\n        }\n      ]\n    }\n  },\n  \"bpm\": {\n    \"user\": {\n      \"phone-number\": \"电 话\",\n      \"department-name\": \"部 门\",\n      \"email\": \"邮 箱\",\n      \"position-level\": \"职 位\",\n      \"organization\": \"组 织\"\n    },\n    \"bpmn\": {\n      \"start\": \"开始\",\n      \"end\": \"结束\",\n      \"defaultStep\": \"默认节点\",\n      \"save\": \"保存\",\n      \"normal\": \"新步骤\",\n      \"sub_process\": \"子流程\",\n      \"automator\": \"自动化\",\n      \"line\": \"连线\",\n      \"autoAlignment\": \"自动调整\",\n      \"fullScreen\": \"全屏\",\n      \"quitFullScreen\": \"退出全屏\",\n      \"delete\": \"删除\",\n      \"confirm\": \"确定\",\n      \"cancel\": \"取消\",\n      \"duplicateStart\": \"已经存在开始节点！\",\n      \"duplicateEnd\": \"已经存在结束节点！\",\n      \"noDeleteTarget\": \"请选择删除对象！\",\n      \"deleteConfirm\": \"确定要删除吗？\",\n      \"cannotTargetStart\": \"连线不能连向开始节点！\",\n      \"cannotSourceEnd\": \"连线不能从结束节点开始！\",\n      \"resetZoom\": \"适应画布\",\n      \"realignment\": \"重新排列\",\n      \"locate\": \"定位\",\n      \"zoom\": \"缩放\",\n      \"zoomIn\": \"放大\",\n      \"zoomOut\": \"缩小\",\n      \"align\": \"对齐\",\n      \"alignCenter\": \"垂直中线对齐\",\n      \"alignLeft\": \"左端对齐\",\n      \"alignRight\": \"右端对齐\",\n      \"alignMiddle\": \"水平中线对齐\",\n      \"alignTop\": \"顶端对齐\",\n      \"alignBottom\": \"底端对齐\",\n      \"distributeHorizontal\": \"水平分散对齐\",\n      \"distributeVertical\": \"垂直分散对齐\",\n      \"undo\": \"撤销\",\n      \"redo\": \"重做\",\n      \"userTask\": \"用户任务\",\n      \"copy\": \"复制到剪贴板\",\n      \"paste\": \"从剪贴板黏贴\",\n      \"processDefaultValue\": \"流程默认值\",\n      \"helper\": {\n        \"title\": \"操作帮助\",\n        \"selectAll\": \"全选\",\n        \"editSelection\": \"编辑选中\",\n        \"dragSelect\": \"拖拽选择\",\n        \"lasso\": \"套索工具\",\n        \"space\": \"空间工具\"\n      },\n      \"messages\": {\n        \"copySuccess\": \"流程图已复制到剪贴板\",\n        \"pasteSuccess\": \"流程图复制成功\",\n        \"noStart\": \"缺少开始节点\",\n        \"pasteSecureTips\": \"受安全性限制，请使用 <Ctrl + V> 黏贴内容\"\n      }\n    },\n    \"fields\": {\n      \"domain\": \"业态\",\n      \"branch\": \"分支\",\n      \"role\": \"角色\",\n      \"version\": \"版本\",\n      \"businessObject\": \"业务对象\",\n      \"formCode\": \"表单编码\",\n      \"formName\": \"表单名称\",\n      \"moduleInfo\": \"模块信息\",\n      \"code\": \"编码\",\n      \"name\": \"名称\",\n      \"authorizedUserId\": \"被授权人\",\n      \"startDate\": \"开始时间\",\n      \"endDate\": \"结束时间\",\n      \"inPar\": \"限定\",\n      \"outPar\": \"返回参数\",\n      \"dataId\": \"输入参数\",\n      \"dataType\": \"参数类型\",\n      \"currentUser\": \"当前登陆人\"\n    },\n    \"routes\": {\n      \"processCategory\": \"流程分类\",\n      \"business-type\": \"业务类型\",\n      \"processDefaultSetting\": \"流程默认值\",\n      \"process\": \"流程\",\n      \"businessObject\": \"业务对象\",\n      \"designProcess\": \"设计流程\",\n      \"dataAuthority\": \"数据权限\"\n    },\n    \"category\": {\n      \"list\": \"流程分类列表\",\n      \"categoryName\": {\n        \"label\": \"分类名称\",\n        \"validation\": \"请输入分类名称\"\n      },\n      \"inUse\": \"是否启用\",\n      \"organizationName\": \"组织名称\",\n      \"parentIdName\": \"一级分类名称\",\n      \"editCategory\": \"编辑分类\",\n      \"addPrimaryCategory\": \"新增一级分类\",\n      \"addSecondaryCategory\": \"新增二级分类\"\n    },\n    \"imitateStart\": {\n      \"BSID\": \"BSID\",\n      \"BTID\": \"BTID\",\n      \"BOID\": \"BOID\",\n      \"startUrl\": \"发起链接\",\n      \"start\": \"发起\",\n      \"processNotfount\": \"找不到流程数据\",\n      \"orgNotfountProcess\": \"组织没有挂流程\",\n      \"notFountAuth\": \"流程未授权\"\n    },\n    \"processEvent\": {\n      \"serviceUrl\": \"服务地址\",\n      \"interfaceName\": \"接口名称\",\n      \"type\": \"请求方式\",\n      \"requestExample\": \"请求示例\",\n      \"returnExample\": \"返回示例\",\n      \"authList\": \"授权列表\"\n    },\n    \"processAuth\": {\n      \"type\": \"授权类型\",\n      \"memberName\": \"授权目标\",\n      \"types\": [\n        {\n          \"label\": \"组织授权\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"角色授权\",\n          \"value\": 3\n        }\n      ]\n    },\n    \"ruleSet\": {\n      \"controlPoint\": \"管控点\",\n      \"ruleCode\": \"管控点编码\",\n      \"ruleName\": \"管控点名称\",\n      \"ruleCount\": \"规则数量\",\n      \"displayCondition\": \"显示条件\",\n      \"standard\": \"判定标准\",\n      \"actualState\": \"实际情况\",\n      \"ruleDetailName\": \"规则名称\",\n      \"condition\": \"规则条件\",\n      \"earlyWarning\": \"预警方式\",\n      \"tipsInfo\": \"提示信息\",\n      \"importance\": \"重要性\",\n      \"chooseBO\": \"请先选择业务对象\",\n      \"editExpression\": \"编辑公式\",\n      \"systemBuilt-in\": \"系统内置\",\n      \"sysParam\": \"系统内置参数\",\n      \"lightsType\": [\n        {\n          \"label\": \"红灯\",\n          \"value\": \"red\"\n        },\n        {\n          \"label\": \"黄灯\",\n          \"value\": \"yellow\"\n        },\n        {\n          \"label\": \"绿灯\",\n          \"value\": \"green\"\n        }\n      ],\n      \"priorityType\": [\n        {\n          \"label\": \"紧急\",\n          \"value\": \"urgent\"\n        },\n        {\n          \"label\": \"重要\",\n          \"value\": \"important\"\n        },\n        {\n          \"label\": \"一般\",\n          \"value\": \"general\"\n        }\n      ]\n    },\n    \"process\": {\n      \"setting\": {\n        \"addNode\": \"添加审批节点\",\n        \"currentNode\": \"当前节点\",\n        \"newNode\": \"新审批节点\",\n        \"approver\": \"审批人\",\n        \"showButton\": \"显示按钮\",\n        \"newStep\": \"新步骤\",\n        \"copyNode\": \"复制节点\",\n        \"nodeName\": \"节点名称\",\n        \"interveneReason\": \"干预原因\",\n        \"saveSuccess\": \"保存成功\",\n        \"pleaseintervene\": \"请先干预再保存\",\n        \"intervenelimit\": \"每次只能干预一次\",\n        \"emptyapprover\": \"审批人为空\",\n        \"editapprover\": \"维护人员\",\n        \"up\": \"上移\",\n        \"down\": \"下移\",\n        \"jumpInfo\": \"请点击保存按钮后，稍等片刻！\"\n      },\n      \"list\": \"流程列表\",\n      \"import\": \"流程导入\",\n      \"versionList\": \"版本列表\",\n      \"newProcess\": \"新建流程\",\n      \"selectProcessMsg\": \"至少得选择一个流程！\",\n      \"batchPublishMaxNumber\": \"一次最多批量发布40条！\",\n      \"published\": \"流程已发布，不能重复发布！\",\n      \"publishing\": \"正在发布，请稍后刷新页面或取消勾选！\",\n      \"batchAuthorizeMaxNumber\": \"一次最多批量授权40条！\",\n      \"authorizing\": \"正在授权，请稍后刷新页面或取消勾选！\",\n      \"selectOrganizationMsg\": \"至少得选择一个组织！\",\n      \"flowChartExport\": \"流程图、外部链接、内部继承不支持导出，请取消勾选！\",\n      \"start-system\": \"发起系统\",\n      \"start-user\": \"发起人\",\n      \"ProcessStatus\": [\n        {\n          \"label\": \"草稿\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"已发布\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"修改中\",\n          \"value\": \"1\"\n        }\n      ],\n      \"listTable\": {\n        \"tagName\": \"类别标签\",\n        \"businessTypeName\": \"业务类型\",\n        \"name\": \"流程名称\",\n        \"version\": \"版本号\",\n        \"statusName\": \"发布状态\",\n        \"validStatus\": \"有效状态\",\n        \"action\": \"操作\",\n        \"publish\": \"发布\",\n        \"prompts\": {\n          \"importSuccess\": \"导入成功\"\n        }\n      },\n      \"listVersionTable\": {\n        \"number\": \"版本号\",\n        \"addTime\": \"创建时间\",\n        \"userName\": \"创建人\",\n        \"introduction\": \"备注\",\n        \"action\": \"操作\",\n        \"restore\": \"复制新版本\",\n        \"restoreTip\": \"使用此版本数据创建新版本\",\n        \"currentNumber\": \"当前版本\"\n      },\n      \"fieldRight\": {\n        \"title\": \"字段权限\",\n        \"fieldName\": \"字段名称\",\n        \"fieldCode\": \"字段编码\",\n        \"useFormRight\": \"引用表单权限\",\n        \"see\": \"可见\",\n        \"unSee\": \"不可见\",\n        \"write\": \"可写\",\n        \"edit\": \"可编辑\",\n        \"unEdit\": \"不可编辑\",\n        \"required\": \"必填\",\n        \"unRequired\": \"非必填\",\n        \"print\": \"打印\",\n        \"hideTable\": \"整表隐藏\"\n      },\n      \"nodeEvent\": {\n        \"apiName\": \"接口名称\",\n        \"apiPath\": \"接口路径\",\n        \"apiMethod\": \"请求类型\",\n        \"afterEvent\": \"处理后事件\",\n        \"beforeEvent\": \"处理前事件\"\n      },\n      \"oneStop\": {\n        \"tabs\": {\n          \"title\": \"流程管理\",\n          \"baseInfo\": {\n            \"title\": \"基本信息\",\n            \"name\": \"流程名称\",\n            \"nameValidation\": \"请输入流程名称\",\n            \"namePlaceholder\": \"请输入流程名称\",\n            \"shortName\": \"流程简称\",\n            \"shortNamePlaceholder\": \"请输入流程简称\",\n            \"source\": \"发起系统\",\n            \"sourceValidation\": \"请选择发起系统\",\n            \"sourcePlaceholder\": \"请选择发起系统\",\n            \"businessTypeId\": \"业务类型\",\n            \"businessTypeIdValidation\": \"请选择业务类型\",\n            \"businessTypeIdPlaceholder\": \"请选择业务类型\",\n            \"tagId\": \"类别标签\",\n            \"tagIdValidation\": \"请选择类别标签\",\n            \"tagIdPlaceholder\": \"请选择类别标签\",\n            \"btIdValidation\": \"请输入 BTID\",\n            \"btIdPlaceholder\": \"请输入 BTID\",\n            \"inUse\": \"是否有效\",\n            \"code\": \"流程编号\",\n            \"codePlaceholder\": \"请输入流程编号\",\n            \"typePlaceholder\": \"请选择类型\"\n          },\n          \"businessObject\": {\n            \"title\": \"虚拟对象\",\n            \"binding\": \"绑定\",\n            \"add\": \"新建\",\n            \"save\": \"保存\",\n            \"cancel\": \"取消\",\n            \"businessType\": \"业务类型\",\n            \"businessTypeValidation\": \"请选择业务类型\",\n            \"chooseBusinessObject\": \"选择虚拟对象\",\n            \"chooseBusinessObjectValidation\": \"请选择虚拟对象\",\n            \"confirmTitle\": \"提示\",\n            \"confirmContent\": \"虚拟对象变更，将取消流程已绑定的表单数据！\",\n            \"view\": {\n              \"baseInfo\": \"基本信息\",\n              \"property\": \"属性\",\n              \"name\": \"名称\",\n              \"nameValidation\": \"请输入虚拟对象名称\",\n              \"namePlaceholder\": \"请输入虚拟对象名称\",\n              \"code\": \"编码\",\n              \"codeValidation\": \"请输入虚拟对象编码\",\n              \"codePlaceholder\": \"请输入虚拟对象编码（发布后无法修改，建议使用英文词组）\",\n              \"businessTypeId\": \"业务类型\",\n              \"businessTypeIdValidation\": \"请选择业务类型\",\n              \"version\": \"版本\"\n            },\n            \"property\": {\n              \"name\": \"名称\",\n              \"code\": \"编码\",\n              \"type\": \"类型\",\n              \"default\": \"默认值\",\n              \"action\": \"操作\",\n              \"addTips\": \"请添加数据\"\n            }\n          },\n          \"formInfo\": {\n            \"title\": \"表单信息\",\n            \"businessObjectForms\": \"业务对象表单\",\n            \"processConfiguredForms\": \"流程配置表单\",\n            \"nameAndCode\": \"名称/编码\",\n            \"version\": \"版本\",\n            \"state\": \"状态\",\n            \"action\": \"操作\",\n            \"node\": \"节点\",\n            \"chooseForm\": \"选择PC表单\",\n            \"chooseMobileForm\": \"选择Mobile表单\",\n            \"formCode\": \"表单编码\",\n            \"formCodePlaceholder1\": \"请输入表单编码\",\n            \"formCodePlaceholder2\": \"自动导入\",\n            \"formName\": \"表单名称\",\n            \"formNamePlaceholder\": \"请输入表单名称\",\n            \"formModules\": \"模块信息\",\n            \"formModuleItems\": {\n              \"baseInfo\": \"基本信息\",\n              \"relations\": \"相关流程\",\n              \"attachments\": \"相关附件\",\n              \"records\": \"审批记录\",\n              \"uoloadFileError\": \"上传文件格式错误！\",\n              \"importForm\": \"导入表单\",\n              \"importFormValidation\": \"请上传表单！\",\n              \"newDesignForm\": \"新建设计表单\",\n              \"newDesignFormValidation\": \"请完善表单编码和名称！\"\n            },\n            \"showHistoryVersion\": \"显示历史版本\",\n            \"sameStepDifferentStepTypes\":\"存在同步骤顺序不同步骤类型\",\n            \"sameStepTypeDifferentSteps\":\"存在同步骤类型不同步骤顺序\"\n          },\n          \"processBpmn\": {\n            \"title\": \"流程图\",\n            \"fixed\": \"固定流程\",\n            \"code\": \"业务编码\",\n            \"diagram\": {\n              \"save\": \"保存流程图\",\n              \"upload\": \"导入权责表\",\n              \"missData\": \"请添加设计数据！\",\n              \"missNode\": \"请添加节点！\",\n              \"missLine\": \"请添加节点线！\",\n              \"missStart\": \"请添加开始节点！\",\n              \"missEnd\": \"请添加结束节点！\",\n              \"missNodeName\": \"请填写节点名称！\",\n              \"perfectNodePolicy\": \"请完善节点处理策略值！\"\n            },\n            \"baseInfo\": {\n              \"title\": \"基本\",\n              \"name\": \"步骤名称\",\n              \"nameValidation\": \"请输入步骤名称\",\n              \"namePlaceholder\": \"请输入步骤名称\",\n              \"formType\": \"表单类型\",\n              \"processForm\": \"流程表单\",\n              \"independForm\": \"独立表单\",\n              \"formCode\": \"步骤表单\",\n              \"formCodeValidation\": \"请选择步骤表单\",\n              \"formCodePlaceholder\": \"请选择步骤表单\",\n              \"actionIds\": \"审批按钮\",\n              \"actionIdsConsult\": \"征询审批按钮\",\n              \"actionIdsConsultSingle\": \"征询审批按钮(单人)\",\n              \"stepActionsAssignSingle\": \"交办审批按钮(单人)\",\n              \"formEditable\": \"是否允许编辑表单\",\n              \"fixedNode\": \"固定步骤\",\n              \"fixedResolver\": \"固定审批人\"\n            },\n            \"policy\": {\n              \"title\": \"策略\",\n              \"resolvePolicy\": \"处理策略\",\n              \"ccPolicy\": \"抄送策略\",\n              \"resolverType\": \"处理类型\",\n              \"resolverTypeValidation\": \"请选择处理类型\",\n              \"resolverTypePlaceholder\": \"请选择处理类型\",\n              \"resolverUsers\": \"处理人\",\n              \"resolverUsersValidation\": \"请选择处理人\",\n              \"resolverUsersPlaceholder\": \"请选择处理人\",\n              \"resolverPosition\": \"处理岗位\",\n              \"resolverPositionValidation\": \"请选择处理岗位\",\n              \"resolverPositionPlaceholder\": \"请选择处理岗位\",\n              \"resolverCommonRole\": \"标准角色\",\n              \"resolverCommonRoleValidation\": \"请选择标准角色\",\n              \"resolverCommonRolePlaceholder\": \"请选择标准角色\",\n              \"resolverSpecialRole\": \"特殊角色\",\n              \"resolverSpecialRoleValidation\": \"请选择特殊角色\",\n              \"resolverSpecialRolePlaceholder\": \"请选择特殊角色\",\n              \"resolverMatrixRole\": \"矩阵角色\",\n              \"resolverMatrixRoleValidation\": \"矩阵角色\",\n              \"resolverMatrixRolePlaceholder\": \"矩阵角色\",\n              \"resolverRoleType\": \"限定类型\",\n              \"resolverOrganizationLevel\": \"组织层级\",\n              \"resolverOrganizationLevelValidation\": \"请选择组织层级\",\n              \"resolverOrganizationLevelPlaceholder\": \"请选择组织层级\",\n              \"resolverOrganization\": \"组织机构\",\n              \"resolverOrganizationValidation\": \"请选择组织机构\",\n              \"resolverOrganizationPlaceholder\": \"请选择组织机构\",\n              \"resolverBusinessVariableValue\": \"变量值\",\n              \"resolverBusinessVariableValueValidation\": \"请选择变量值\",\n              \"resolverBusinessVariableValuePlaceholder\": \"请选择变量值\",\n              \"resolverSystemVariableValue\": \"变量值\",\n              \"resolverSystemVariableValueValidation\": \"请选择变量值\",\n              \"resolverSystemVariableValuePlaceholder\": \"请选择变量值\",\n              \"participatingType\": \"会签策略\",\n              \"participatingTypePlaceholder\": \"请选择会签策略\",\n              \"participatingPercent\": \"会签比例\",\n              \"participatingPercentValidation\": \"请填写会签比例\",\n              \"participatingPercentPlaceholder\": \"请填写会签比例\",\n              \"participatingUsers\": \"会签决定人\",\n              \"participatingUsersValidation\": \"请选择会签决定人\",\n              \"participatingUsersPlaceholder\": \"请选择会签决定人\",\n              \"concurrentType\": \"并签策略\",\n              \"concurrentTypePlaceholder\": \"请选择并签策略\",\n              \"concurrentPercent\": \"并签比例\",\n              \"concurrentPercentValidation\": \"请填写并签比例\",\n              \"concurrentPercentPlaceholder\": \"请填写并签比例\",\n              \"sameApproverType\": \"是否允许相同审批人跳过\",\n              \"sameApproverTypePlaceholder\": \"请选择是否允许相同审批人跳过\",\n              \"emptyApproverType\": \"步骤审批人为空时\",\n              \"emptyApproverTypePlaceholder\": \"请选择步骤审批人为空时\",\n              \"emptyApproverUsers\": \"空审批人跳转人\",\n              \"emptyApproverUsersValidation\": \"请选择空审批人跳转\",\n              \"emptyApproverUsersPlaceholder\": \"请选择空审批人跳转\",\n              \"ccResolverUsers\": \"抄送人\",\n              \"ccResolverUsersPlaceholder\": \"请选择抄送人\",\n              \"no\": \"无\"\n            },\n            \"line\": {\n              \"name\": \"名称\",\n              \"namePlaceholder\": \"请输入名称\",\n              \"expression\": \"表达式\",\n              \"exoressionPlaceholder\": \"请输入表达式\",\n              \"exoressionTitle\": \"单击编辑表达式\",\n              \"example\": \"示例\"\n            }\n          },\n          \"organizationAuth\": {\n            \"title\": \"组织授权\",\n            \"name\": \"组织名称\",\n            \"code\": \"组织编码\",\n            \"include\": \"是否包含子集\",\n            \"selectValidation\": \"请选择组织\"\n          },\n          \"roleAuth\": {\n            \"title\": \"角色授权\",\n            \"name\": \"角色名称\",\n            \"selectValidation\": \"请选择角色\",\n            \"authList\": \"授权列表\"\n          },\n          \"controlPoint\": {\n            \"title\": \"管控点\"\n          },\n          \"extendInfo\": {\n            \"title\": \"扩展信息\",\n            \"entryCondition\": \"入口条件\",\n            \"entryConditionValidation\": \"请输入入口条件\",\n            \"entryConditionPlaceholder\": \"请输入入口条件\",\n            \"customProcessTopic\": \"自定义标题\",\n            \"customProcessTopicValidation\": \"请输入自定义标题\",\n            \"customProcessTopicPlaceholder\": \"请输入自定义标题\",\n            \"customApprovalTopic\": \"自定义审批标题\",\n            \"customApprovalTopicValidation\": \"请输入自定义审批标题\",\n            \"customApprovalTopicPlaceholder\": \"请输入自定义审批标题\",\n            \"introduction\": \"流程介绍\",\n            \"introductionValidation\": \"请输入流程介绍\",\n            \"introductionPlaceholder\": \"请输入流程介绍\",\n            \"autoApproveType\": \"自动审批\",\n            \"autoApproveTypeValidation\": \"请选择自动审批类型\",\n            \"autoApproveTypePlaceholder\": \"请选择自动审批类型\",\n            \"sameApproverAfterAuto\": \"相同审批人后面步骤自动审批\",\n            \"sameApproverPreAuto\": \"相同审批人前面步骤自动审批\",\n            \"businessUrl\": \"业务系统PC链接\",\n            \"businessMobileUrl\": \"业务系统Mobile链接\",\n            \"isAllowMobileStart\": \"是否允许移动端发起\",\n            \"isAllowStructuredStorage\": \"是否允许结构化存储\",\n            \"isShouFaWen\": \"是否收发文流程\",\n            \"approverEmptyEdit\": \"发起人可编辑空审批人\",\n            \"virtualObjectDataWriteback\": \"虚拟对象数据回写\",\n            \"consultInheritActions\": \"被征询人是否继承按钮权限\",\n            \"param1\": \"流程类别\",\n            \"parentProcess\": \"父流程\",\n            \"parentProcessPlaceholder\": \"请选择父流程\",\n            \"isWatermark\": \"批量下载是否需要加水印\",\n            \"isIntervenAfter\": \"是否允许干预后续审批人\",\n            \"lC_IsContract\": \"是否显示合同\",\n            \"lC_ContractNo\": \"合同编号\",\n            \"lC_ContractNoPlaceholder\": \"请填写合同编号\",\n            \"lC_IsContractTrue\": \"合同报审必填\",\n            \"lC_IsPaync\": \"是否NC付款\",\n            \"lC_IsPlaceOnFile\": \"是否需要归档\",\n            \"lC_RiskIsDisplay\": \"是否显示风控\",\n            \"lC_RiskType\": \"风险类别\",\n            \"lC_RiskDescribe\": \"风险描述\",\n            \"lC_DescribeContent\": \"描述内容\"\n          },\n          \"fillNotes\": {\n            \"title\": \"填写说明\"\n          },\n          \"institutionalNotes\": {\n            \"title\": \"制度说明\"\n          },\n          \"fileNotes\": {\n            \"title\": \"附件标准\"\n          },\n          \"onlineProcessTable\": {\n            \"title\": \"权责表\"\n          },\n          \"externalLink\": {\n            \"title\": \"外部链接\"\n          },\n          \"insideInherit\": {\n            \"title\": \"内部继承\"\n          },\n          \"keyParam\": {\n            \"title\": \"关键参数\",\n            \"keyParameterType\": [\n              {\n                \"label\": \"分支\",\n                \"value\": 1\n              },\n              {\n                \"label\": \"节点\",\n                \"value\": 2\n              },\n              {\n                \"label\": \"入口条件\",\n                \"value\": 3\n              },\n              {\n                \"label\": \"自定义标题\",\n                \"value\": 4\n              },\n              {\n                \"label\": \"管控点\",\n                \"value\": 5\n              }\n            ],\n            \"source\": [\n              {\n                \"label\": \"业务对象\",\n                \"value\": 1\n              },\n              {\n                \"label\": \"系统参数\",\n                \"value\": 2\n              }\n            ]\n          }\n        },\n        \"prompts\": {\n          \"publishSuccess\": \"发布成功\",\n          \"saveSuccess\": \"保存成功\"\n        },\n        \"processPublish\": {\n          \"empty\": \"空数据\",\n          \"notes\": \"说明：点击确定，将统一发布流程相关数据。\"\n        }\n      },\n      \"messages\": {\n        \"chooseOrganization\": \"请先选择组织\"\n      },\n      \"form\": {\n        \"number\": \"数字\",\n        \"text\": \"文本\",\n        \"table\": \"从表\"\n      },\n      \"business-type\": {\n        \"add\": \"添加业务类型\",\n        \"parent\": \"父级业务类型\",\n        \"ExistedCode\": \"业务类型编码已经存在！\",\n        \"CheckUsed\": \"业务类型已被使用，无法删除！\"\n      },\n      \"domain-level-module\": {\n        \"existedCode\": \"业态层级编码已经存在！\"\n      },\n      \"excel-process\": {\n        \"list\": {\n          \"no\": \"序号\",\n          \"item\": \"管理事项\",\n          \"dept\": \"主责部门\",\n          \"creator\": \"发起人\",\n          \"type\": \"类别\",\n          \"name\": \"流程名\",\n          \"introduction\": \"流程简介\",\n          \"branch\": \"分支名\",\n          \"branchDescription\": \"分支说明\",\n          \"inuse\": \"是否有效\",\n          \"code\": \"流程编码\",\n          \"entryCondition\": \"入口条件\",\n          \"method\": \"发起方式\",\n          \"processConfigCondition\": \"流程配置条件\"\n        },\n        \"operation\": {\n          \"save\": \"保存\",\n          \"preview\": \"预览\",\n          \"previewDescription\": \"数据预览、编辑\",\n          \"done\": \"完成\",\n          \"success\": \"操作成功\"\n        },\n        \"import\": {\n          \"upload\": \"上传\",\n          \"description\": \"上传权责表数据\",\n          \"notFound\": \"请上传权责表数据\",\n          \"message\": \"导入信息\",\n          \"success\": \"导入成功\",\n          \"messageParm\": \"导入流程：{{value}}\"\n        },\n        \"steps\": {\n          \"previous\": \"上一步\",\n          \"next\": \"下一步\"\n        }\n      },\n      \"business-object\": {\n        \"business-type\": \"业务类型\",\n        \"version\": \"版本\",\n        \"statusDataset\": [\n          {\n            \"label\": \"全部\",\n            \"value\": \"\"\n          },\n          {\n            \"label\": \"已发布\",\n            \"value\": \"published\"\n          },\n          {\n            \"label\": \"未发布\",\n            \"value\": \"unpublished\"\n          },\n          {\n            \"label\": \"修改中\",\n            \"value\": \"changing\"\n          }\n        ],\n        \"baseInfo\": \"基本信息\",\n        \"attribute\": \"属性\",\n        \"defaultValue\": \"默认值\",\n        \"type\": \"类型\",\n        \"addData\": \"请添加数据\",\n        \"detailTable\": \"明细表\",\n        \"attributeType\": [\n          {\n            \"label\": \"文本\",\n            \"value\": \"text\"\n          },\n          {\n            \"label\": \"数字\",\n            \"value\": \"number\"\n          },\n          {\n            \"label\": \"从表\",\n            \"value\": \"table\"\n          },\n          {\n            \"label\": \"日期\",\n            \"value\": \"date\"\n          }\n        ],\n        \"pleaseSaveFieldFirst\": \"请先保存字段\",\n        \"ExistedCode\": \"业务对象编码已经存在！\",\n        \"HasUsed\": \"已被使用\"\n      },\n      \"notfound\": \"流程数据不存在\",\n      \"rollBackCheck\": \"当前流程处于修改中，不能恢复！\",\n      \"startUserNotfount\": \"发起人为空！\",\n      \"bpmnUrlNotfount\": \"流程图地址未配置！\",\n      \"btidBotfound\": \"btid不能为空！\",\n      \"json\": {\n        \"FormatError\": \"流程Json结构异常！\",\n        \"NotNull\": \"流程设计数据为空！\"\n      },\n      \"branch\": {\n        \"NotNull\": \"流程分支数据不能为空！\"\n      },\n      \"step\": {\n        \"NotNull\": \"流程步骤数据不能为空！\",\n        \"ToDoState\": \"未到达\"\n      },\n      \"processVersion\": {\n        \"Notfound\": \"流程版本数据不存在！\",\n        \"RollBackInfo\": \"从版本{0}复制\"\n      },\n      \"authorization\": {\n        \"NullProcessId\": \"流程数据为空！\",\n        \"Notfound\": \"授权数据不存在！\"\n      },\n      \"draft\": {\n        \"Notfound\": \"草稿数据不存在！\"\n      },\n      \"userAgent\": {\n        \"EmptyUserAgent\": \"代理人为空！\",\n        \"EmptyUserId\": \"授权UserId为空！\"\n      },\n      \"dataAuthority\": {\n        \"ExistedName\": \"数据权限名称已经存在！\",\n        \"ExistedUser\": \"用户已经存在！\",\n        \"ExistedProcess\": \"流程已经存在！\"\n      },\n      \"ugreTips\": \"当天已催办，请不要重复催办！\"\n    },\n    \"messages\": {\n      \"chooseOrganization\": \"请先选择组织\"\n    },\n    \"processDefaultSetting\": {\n      \"canCC\": \"是否可以抄送\",\n      \"autoComplete\": \"自动跳过\",\n      \"postion\": \"节点坐标\"\n    },\n    \"form\": {\n      \"number\": \"数字\",\n      \"text\": \"文本\",\n      \"table\": \"从表\",\n      \"list\": \"表单列表\",\n      \"searchPlaceholder\": \"搜索模型名称/编码\",\n      \"editTitle\": \"新建设计表单\",\n      \"user\": \"人员\",\n      \"module\": [\n        {\n          \"label\": \"基本信息\",\n          \"value\": \"baseInfo\"\n        },\n        {\n          \"label\": \"相关流程\",\n          \"value\": \"relation\"\n        },\n        {\n          \"label\": \"相关附件\",\n          \"value\": \"attachment\"\n        },\n        {\n          \"label\": \"审批记录\",\n          \"value\": \"record\"\n        },\n        {\n          \"label\": \"管控点\",\n          \"value\": \"controlPoint\"\n        },\n         {\n          \"label\": \"公文正文\",\n          \"value\": \"document\"\n        }\n      ]\n    },\n    \"business-type\": {\n      \"add\": \"添加业务类型\",\n      \"parent\": \"父级业务类型\",\n      \"scope\":\"适用范围\"\n    },\n    \"excel-process\": {\n      \"title\": {\n        \"addBranch\": \"新增流程分支\",\n        \"addRole\": \"添加流程角色\",\n        \"baseInfo\": \"基础信息\",\n        \"editStep\": \"编辑步骤\",\n        \"nodeConfig\": \"节点配置\",\n        \"property\": \"流程属性\",\n        \"roleConfig\": \"角色配置\",\n        \"relevantCountersign\": \"相关会签\"\n      },\n      \"field\": {\n        \"branchName\": \"分支名称\",\n        \"condition\": \"条件\",\n        \"roleType\": \"处理类型\",\n        \"domain\": \"业态\",\n        \"roleLimitType\": \"限定类型\",\n        \"notLimit\": \"不限定\",\n        \"organizationLevelLabel\": \"组织层级\",\n        \"organizationGroupLabel\": \"组织机构\",\n        \"standardRole\": \"标准角色\",\n        \"stepIndex\": \"步骤顺序\",\n        \"stepType\": \"节点类型\",\n        \"stepName\": \"步骤名称\",\n        \"resolver\": \"处理人\",\n        \"directUser\": \"直接处理人\",\n        \"matrixRole\": \"矩阵角色\",\n        \"position\": \"处理岗位\",\n        \"specificRole\": \"特殊角色\",\n        \"systemVariable\": \"系统变量\",\n        \"businessVariable\": \"业务变量\",\n        \"enableEditForm\": \"是否允许编辑表单\",\n        \"fixedStep\": \"固定步骤\",\n        \"fixedApprover\": \"固定审批人\",\n        \"fixedOriginalApprover\": \"固定原始审批人\",\n        \"signStrategy\": \"会签策略\",\n        \"signPercent\": \"会签比例\",\n        \"parallelStrategy\": \"并签策略\",\n        \"parallelPercent\": \"并签比例\",\n        \"sameStartApproverStrategy\": \"是否允许与发起人相同跳过\",\n        \"sameApproverStrategy\": \"是否允许相同审批人跳过\",\n        \"noneApproverStrategy\": \"步骤审批人为空时\",\n        \"approvalButtons\": \"审批按钮\",\n        \"satisfactionCondition\": \"满足条件\",\n        \"limitApprover\": \"限定审批人\",\n        \"batchApproval\": \"是否允许批量审批\",\n        \"organizationVariable\": \"组织变量\",\n        \"fixedProcess\": \"固化流程\",\n        \"freeProcess\": \"自由流\",\n        \"fixedProcessUsers\": \"固化人员\",\n        \"startUser\": \"发起人\",\n        \"startUserSuperior\": \"发起人直接上级\",\n        \"startUserDeptLeader\": \"发起人部门负责人\",\n        \"startUserDeptVP\": \"发起人部门分管领导\",\n        \"startUserCompanyLeader\": \"发起人公司负责人\",\n        \"startUserCompanyVP\": \"发起人公司分管领导\",\n        \"startUserProjectLeader\": \"发起人项目负责人\",\n        \"startUserProjectVP\": \"发起人项目分管领导\",\n        \"rejectAutoCancel\":\"退回自动作废\",\n        \"autoCancelTime\": \"超时时间\",\n        \"hour\": \"小时\",\n        \"timeoutAutoApproval\":\"超时自动审批\",\n        \"autoApprovalTime\":\"超时时间\",\n        \"processMaxApprovalCount\":\"流程最大审批人数\",\n        \"nodeMaxApprovalCount\":\"节点最大审批人数\",\n        \"nodeMaxConsultCount\":\"节点最大意见征询次数\",\n        \"nodeMaxHandoverCount\":\"节点最大委托次数\",\n        \"delayTime\":\"延时时间（天）\",\n        \"warningLight\": \"红黄绿灯预警设置\",\n        \"timeoutReminders\": \"超时提醒\",\n        \"timeoutRemindersRemark\": \"（多次提醒“;”分隔，单位：小时）\",\n        \"timeoutRemindersMsg\": \"只能输入正整数,分号分隔\"\n      },\n      \"list\": {\n        \"no\": \"序号\",\n        \"item\": \"管理事项\",\n        \"dept\": \"主责部门\",\n        \"creator\": \"发起人\",\n        \"type\": \"类别\",\n        \"name\": \"流程名\",\n        \"introduction\": \"流程简介\",\n        \"branch\": \"分支名\",\n        \"branchDescription\": \"分支说明\",\n        \"inuse\": \"是否有效\",\n        \"code\": \"流程编码\",\n        \"entryCondition\": \"入口条件\",\n        \"method\": \"发起方式\",\n        \"processConfigCondition\": \"流程配置条件\"\n      },\n      \"operation\": {\n        \"addBranch\": \"新增分支\",\n        \"editBranch\": \"编辑分支\",\n        \"deleteBranch\": \"删除分支\",\n        \"addRole\": \"添加角色\",\n        \"editRole\": \"编辑角色\",\n        \"deleteRole\": \"删除角色\",\n        \"node\": \"审批节点: \",\n        \"editStep\": \"编辑步骤\",\n        \"deleteStep\": \"删除步骤\",\n        \"save\": \"保存\",\n        \"preview\": \"预览\",\n        \"previewDescription\": \"数据预览、编辑\",\n        \"done\": \"完成\",\n        \"doneDescription\": \"数据保存成功\",\n        \"success\": \"操作成功\",\n        \"editStartStep\": \"编辑开始节点\",\n        \"editEndStep\": \"编辑结束节点\",\n        \"copyName\": \"复制名称\"\n      },\n      \"import\": {\n        \"upload\": \"上传\",\n        \"description\": \"上传权责表数据\",\n        \"notFound\": \"请上传权责表数据\",\n        \"templateDownload\": \"模板下载\",\n        \"chooseDataTable\": \"请选择文件.\",\n        \"processData\": \"导入流程.\",\n        \"processFinish\": \"流程导入完成.\"\n      },\n      \"steps\": {\n        \"previous\": \"上一步\",\n        \"next\": \"下一步\"\n      },\n      \"message\": {\n        \"branchNameDuplicate\": \"存在相同名称的分支\",\n        \"roleNameDuplicate\": \"存在相同名称的角色\",\n        \"inputErro\": \"输入格式不正确\",\n        \"noBranches\": \"没有分支\",\n        \"noRoles\": \"没有节点\",\n        \"noValidData\": \"没有有效数据\"\n      },\n      \"api\": {\n        \"Name\": \"权责表\",\n        \"Order\": \"序号\",\n        \"BusinessType\": \"*业务类型\",\n        \"TagName\": \"类别标签\",\n        \"ProcessCode\": \"*流程编号\",\n        \"ProcessName\": \"*流程名称\",\n        \"ShortName\": \"流程简称\",\n        \"BtId\": \"BTID\",\n        \"Authorizer\": \"发起人\",\n        \"InUse\": \"是否有效\",\n        \"Condition\": \"入口条件\",\n        \"AuthorizeType\": \"*发起系统编码\",\n        \"BranchIntroduction\": \"分支说明\",\n        \"BranchName\": \"*分支名\",\n        \"Category\": \"类别\",\n        \"DepartmentName\": \"主责部门\",\n        \"Expression\": \"判断条件\",\n        \"IsGroupDecision\": \"是否决策流程\",\n        \"ProcessType\": \"流程类型\",\n        \"PCLink\": \"PC端链接\",\n        \"MobileLink\": \"移动端链接\",\n        \"ParentProcessCode\": \"父流程编码\",\n        \"InnerInherit\": \"内部继承\",\n        \"OuterLink\": \"外部链接\",\n        \"FormCode\": \"*表单编码\",\n        \"ManagerItem\": \"管理事项\",\n        \"ErrorString\": \"导入的数据中包含系统无法识别的特殊字符!\",\n        \"Notfound\": \"权责表数据不存在！\",\n        \"EmptyBranch\": \"存在空的分支！\",\n        \"Start\": \"开始\",\n        \"End\": \"结束\",\n        \"TagNotfound\": \"Sheet名:{0}在系统中不存在！\",\n        \"ColumnNotfound\": \"列:{0}在sheet:{1}中不存在\",\n        \"ColumnEmpty\": \"sheet:{0}中列:{1}值为空\",\n        \"ColumnValueNotfound\": \"sheet:{0}中列:{1}值:{2}在系统中不存在！\",\n        \"ColumnValueNotPublish\": \"sheet:{0}中列:{1}值:{2}在系统中未发布！\",\n        \"FlagNoInMultiStep\": \"sheet[{0}]中第{1}行标记{2}不允许同时出现在多个步骤上！\",\n        \"StepNoInMultiFlag\": \"sheet[{0}]中第{1}行会签步骤{2}不允许同时出现多个标记！\",\n        \"GetTasError\": \"标签类型:{0}获取失败!\",\n        \"DuplicateProcessName\": \"sheet:{1}中流程名称:{0}在系统已存在！\",\n        \"ExportCheck\": \"非权责导入数据不能导出\",\n        \"FillIntroduction\": {\n          \"Domain\": \"此处为该流程所属的业态\",\n          \"Role1\": \"此处填写角色名称1\",\n          \"Role2\": \"此处填写角色名称2\"\n        },\n        \"MatrixRole\": \"矩阵角色\",\n        \"DirectUser\": \"直接处理人\",\n        \"Position\": \"通用岗位\",\n        \"SpecificRole\": \"特色角色\",\n        \"SystemVariable\": \"系统变量\",\n        \"BusinessVariable\": \"业务变量\",\n        \"CommonRole\": \"通用角色\",\n        \"MatrixRoleNotfound\": \"sheet:{0}中的矩阵角色:{1}不存在\",\n        \"DirectUserNotfound\": \"sheet:{0}中的直接处理人:{1}不存在\",\n        \"PositionNotfound\": \"sheet:{0}中的处理岗位:{1}不存在\",\n        \"SpecificRoleNotfound\": \"sheet:{0}中的特色角色:{1}不存在\",\n        \"SystemVariableNotfound\": \"sheet:{0}中的系统变量:{1}不存在\",\n        \"BusinessVariableNotfound\": \"sheet:{0}中的业务变量:{1}不存在\",\n        \"CommonRoleNotfound\": \"sheet:{0}中的通用角色:{1}不存在\",\n        \"OrgLevelNotfound\": \"sheet:{0}中的组织层级:{1}不存在\",\n        \"StartUser\": \"发起人\",\n        \"StartUserSuperior\": \"发起人直接上级\",\n        \"StartUserDeptLeader\": \"发起人部门负责人\",\n        \"StartUserDeptVP\": \"发起人部门分管领导\",\n        \"StartUserCompanyLeader\": \"发起人公司负责人\",\n        \"StartUserCompanyVP\": \"发起人公司分管领导\",\n        \"StartUserProjectLeader\": \"发起人项目负责人\",\n        \"StartUserProjectVP\": \"发起人项目分管领导\",\n        \"DomainLevelFormatError\": \"sheet:{0}中的业态层级:{1}格式不正确\",\n        \"DomainNotfound\": \"sheet:{0}中的组织层级:{1}不存在\",\n        \"noBindForms\": \"没有绑定表单\"\n      }\n    },\n    \"business-object\": {\n      \"business-type\": \"业务类型\",\n      \"version\": \"版本\",\n      \"statusDataset\": [\n        {\n          \"label\": \"全部\",\n          \"value\": \"\"\n        },\n        {\n          \"label\": \"已发布\",\n          \"value\": \"published\"\n        },\n        {\n          \"label\": \"未发布\",\n          \"value\": \"unpublished\"\n        },\n        {\n          \"label\": \"修改中\",\n          \"value\": \"changing\"\n        }\n      ],\n      \"baseInfo\": \"基本信息\",\n      \"attribute\": \"属性\",\n      \"defaultValue\": \"默认值\",\n      \"length\": \"长度\",\n      \"decimal\": \"小数点位数\",\n      \"type\": \"类型\",\n      \"addData\": \"请添加数据\",\n      \"detailTable\": \"明细表\",\n      \"attributeType\": [\n        {\n          \"label\": \"文本\",\n          \"value\": \"text\"\n        },\n        {\n          \"label\": \"数字\",\n          \"value\": \"number\"\n        },\n        {\n          \"label\": \"从表\",\n          \"value\": \"table\"\n        },\n        {\n            \"label\": \"日期\",\n            \"value\": \"date\"\n          }\n      ],\n      \"pleaseSaveFieldFirst\": \"请先保存字段\",\n      \"entity-object-base\": \"业务对象库\",\n      \"entity-object\": \"业务对象\"\n    },\n    \"agent\": {\n      \"authorized\": \"授权人\",\n      \"agent\": \"代理人\",\n      \"enabled\": \"是否启用\"\n    },\n    \"data-authority\": {\n      \"name\": \"权限名称\",\n      \"config\": \"权限配置\",\n      \"role\": \"角色授权\",\n      \"organization\": \"组织权限\",\n      \"business-type\": \"业务类型权限\",\n      \"systems\": \"系统权限\",\n      \"user\": \"用户(流程查询)\",\n      \"account\":\"账号\",\n      \"username\":\"姓名\",\n      \"orgpath\":\"组织路径\",\n      \"process\":\"流程模板(流程查询)\",\n      \"processName\":\"流程名称\",\n      \"domain\": \"业态\",\n      \"businessTypePath\": \"业务类型路径\"\n    },\n    \"work-handover\": {\n      \"role-replace\": \"人员角色替换\",\n      \"todo-handover\": \"人员待办交接\",\n      \"fixed-handover\": \"指定人员交接\",\n      \"change-person\": \"异动人\",\n      \"replacement\": \"替换人\",\n      \"approver\": \"审批人\",\n      \"batch-change\": \"批量异动\",\n      \"batch-modify\": \"批量修改\",\n      \"process-no\": \"流程编号\",\n      \"process-topic\": \"流程主题\",\n      \"start-user\": \"发起人\",\n      \"notice-user\": \"传阅人\",\n      \"start-time\": \"发起时间\",\n      \"current-step\": \"当前步骤\",\n      \"role\": \"角色\",\n      \"todo\": \"待办\",\n      \"number\": \"单据\",\n      \"todo-type\": \"待办类型\",\n      \"arrive-time\": \"到达时间\",\n      \"selectAddUser\": \"请选择加签人！\",\n      \"selectHandUser\": \"请选择交办人！\"\n    }\n  },\n  \"components\": {\n    \"comp-expression-designer\": {\n      \"title\": \"表达式编辑器\",\n      \"defaultPromptText\": \"点击\\\"+\\\"以添加表达式\",\n      \"explain\": \"说明:<br /> 1.xxx<br /> 2.xxx\",\n      \"text\": \"文本\",\n      \"user\": \"人员\",\n      \"number\": \"数字\",\n      \"businessObjects\": \"业务对象\",\n      \"globalObjects\": \"全局对象\",\n      \"custom\": \"自定义\",\n      \"simpleEdit\": \"简单\",\n      \"seniorEdit\": \"高级\",\n      \"customEditPlaceholder\": \"请输入自定义表达式\",\n      \"badExpression\": \"表达式格式不正确\",\n      \"logicalOperatorList\": [\n        {\n          \"label\": \"并且\",\n          \"value\": \"&&\",\n          \"expression\": \"&&\"\n        },\n        {\n          \"label\": \"或者\",\n          \"value\": \"||\",\n          \"expression\": \"||\"\n        }\n      ],\n      \"operatorList\": [\n        {\n          \"label\": \"等于\",\n          \"value\": \"==\",\n          \"expression\": \"==\"\n        },\n        {\n          \"label\": \"不等于\",\n          \"value\": \"!=\",\n          \"expression\": \"!=\"\n        },\n        {\n          \"label\": \"大于\",\n          \"value\": \">\",\n          \"expression\": \">\"\n        },\n        {\n          \"label\": \"大于等于\",\n          \"value\": \">=\",\n          \"expression\": \">=\"\n        },\n        {\n          \"label\": \"小于\",\n          \"value\": \"<\",\n          \"expression\": \"<\"\n        },\n        {\n          \"label\": \"小于等于\",\n          \"value\": \"<=\",\n          \"expression\": \"<=\"\n        },\n        {\n          \"label\": \"包含\",\n          \"value\": \"Contains\",\n          \"expression\": \".Contains\"\n        },\n        {\n          \"label\": \"不包含\",\n          \"value\": \"Not Contains\",\n          \"expression\": \".Contains\"\n        },\n        {\n          \"label\": \"开始以\",\n          \"value\": \"StartsWith\",\n          \"expression\": \".StartsWith\"\n        },\n        {\n          \"label\": \"开始不以\",\n          \"value\": \"Not StartsWith\",\n          \"expression\": \".StartsWith\"\n        },\n        {\n          \"label\": \"结束以\",\n          \"value\": \"EndsWith\",\n          \"expression\": \".EndsWith\"\n        },\n        {\n          \"label\": \"结束不以\",\n          \"value\": \"Not EndsWith\",\n          \"expression\": \".EndsWith\"\n        }\n      ]\n    }\n  },\n  \"todoCenter\": {\n    \"archive\": {\n      \"BadParams\": \"参数 \\\"business-id\\\" 与 \\\"business-number + business-id-extra\\\" 必须至少传一组。\"\n    },\n    \"fields\":{\n      \"id\":\"主键\",\n      \"systemCode\":\"系统编码\",\n      \"subSystemCode\":\"子系统编码\",\n      \"subSystemName\":\"子系统名称\",\n      \"groupId\":\"分类Id\",\n      \"groupCode\":\"分类编码\",\n      \"groupName\":\"分类名称\",\n      \"groupIdPath\":\"分类Id路径\",\n      \"groupNamePath\":\"分类名称路径\",\n      \"businessID\":\"业务Id\",\n      \"businessNumber\":\"单号\",\n      \"businessTitle\":\"标题\",\n      \"businessStatusCode\":\"业务状态编码\",\n      \"businessStatusName\":\"业务状态名称\",\n      \"startTime\":\"业务发起时间\",\n      \"processId\":\"流程Id\",\n      \"processCode\":\"流程编码\",\n      \"processName\":\"流程名称\",\n      \"processVersion\":\"流程版本\",\n      \"summary\":\"摘要\",\n      \"remarks\":\"备注\",\n      \"startUserLoginId\":\"发起人\",\n      \"startUserName\":\"发起人姓名\",\n      \"startUserOrgCode\":\"发起人组织编码\",\n      \"startUserOrgName\":\"发起人组织名称\",\n      \"startUserOrgCodePath\":\"发起人组织编码路径\",\n      \"startUserOrgNamePath\":\"发起人组织名称路径\",\n      \"ownerUserLoginId\":\"申请人账号\",\n      \"ownerUserName\":\"申请人姓名\",\n      \"ownerUserOrgCode\":\"申请人组织编码\",\n      \"ownerUserOrgName\":\"申请人组织名称\",\n      \"tripartiteViewUrl\":\"PC链接\",\n      \"tripartiteViewMobileUrl\":\"移动端链接\",\n      \"msgType\":\"消息类型\",\n      \"param1\":\"属性1\",\n      \"param2\":\"属性2\",\n      \"param3\":\"属性3\",\n      \"param4\":\"属性4\",\n      \"param5\":\"属性5\",\n      \"param6\":\"属性6\",\n      \"param7\":\"属性7\",\n      \"param8\":\"属性8\",\n      \"splitTb1\":\"分表字段1\",\n      \"status\":\"数据状态\",\n      \"statusName\":\"数据状态\",\n      \"updateProcesingTime\":\"流程最后更新时间\",\n      \"createUserId\":\"创建人Id\",\n      \"createUserName\":\"创建人姓名\",\n      \"createTime\":\"创建时间\",\n      \"lastModifyUserId\":\"修改人Id\",\n      \"lastModifyUserName\":\"修改人姓名\",\n      \"lastModifyTime\":\"修改时间\",\n      \"currentStepName\":\"当前步骤名称\",\n      \"taskId\":\"待办Id\",\n      \"userLoginId\":\"账号\",\n      \"userName\":\"姓名\",\n      \"userOrgCodePath\":\"用户组织编码路径\",\n      \"userOrgNamePath\":\"用户组织名称路径\",\n      \"urgency\":\"催办\",\n      \"isBatchApprove\":\"是否允许批量审批\",\n      \"isApprove\":\"可审批\",\n      \"isBatchHandOver\":\"可批量转交\",\n      \"overDateTime\":\"过期时间\",\n      \"isRead\":\"已读\",\n      \"specialLabel\":\"特殊标签\",\n      \"useForGroupName\":\"分组名称\",\n      \"arriveTime\":\"到达时间\"\n    }\n  },\n  \"forms\": {\n    \"modules\": {\n      \"Form\": \"表单\",\n      \"Model\": \"模型\",\n      \"Template\": \"模板\"\n    },\n    \"fields\": {\n      \"Code\": \"编码\",\n      \"Name\": \"名称\",\n      \"Data\": \"数据\",\n      \"BusinessObjectCode\": \"业务对象\",\n      \"BusinessTypeId\": \"业务类型\"\n    },\n    \"form\": {\n      \"placeholder\": {\n        \"Input\": \"请输入{0}\",\n        \"Select\": \"请选择{0}\"\n      },\n      \"publishCheck\": {\n        \"ExistPublishedData\": \"存在已发布数据！\",\n        \"ExistForbidData\": \"存在禁用数据！\"\n      },\n      \"existCode\": \"表单编码已经存在！\",\n      \"checkOldVersionSave\": \"旧版本表单禁止操作！\",\n      \"processFormExisted\": \"流程已经绑定表单！\",\n      \"checkFormBind\": \"表单已与流程绑定无法删除！\"\n    },\n    \"designForm\": {\n      \"fields\": {\n        \"Number\": \"序号\",\n        \"Operation\": \"操作\",\n        \"Total\": \"合计\"\n      },\n      \"buttons\": {\n        \"Add\": \"添加\",\n        \"Delete\": \"删除\",\n        \"CopyAndAdd\": \"复制并添加\",\n        \"Export\": \"导出\"\n      }\n    },\n    \"model\": {\n      \"existCode\": \"模型编码已经存在！\"\n    },\n    \"template\": {\n      \"fileNotfound\": \"模板文件不存在！\",\n      \"filePathNotfound\": \"模板存储路径未配置！\",\n      \"onlineEditorUrlhNotfound\": \"在线编辑器链接不存在！\"\n    },\n    \"task\": {\n      \"apiUrlNotfound\": \"任务状态接口地址为配置！\"\n    }\n  },\n  \"engine\": {\n    \"Instance\": {\n      \"StartActivityName\": \"发起\",\n      \"Forbidden\": \"拒绝访问\"\n    }\n  },\n  \"modeling\": {\n    \"framework\": {\n        \"headerUserNotfound\": \"头信息中缺少用户信息，请将用户信息base64编码后放入Header[\\\"current_user\\\"]中。\"\n    },\n    \"notFound\": \"{0}数据不存在！\",\n    \"modelCheck\": \"{0}不能为空！\",\n    \"dataBase\": {\n        \"notfound\": \"数据库信息不存在！\",\n        \"checkDB\": \"无法连接到数据库，请认真检查数据库信息是否正确！\"\n    },\n    \"businessObject\": {\n        \"notfound\": \"业务对象不存在！\",\n        \"exist\": \"业务对象已存在或已有物理表！\"\n    },\n    \"compositeObject\": {\n        \"notfound\": \"组合对象不存在！\"\n    },\n    \"pageModeling\": {\n        \"notfound\": \"页面不存在！\",\n        \"nameExist\": \"页面名称已存在！\",\n        \"undercarriage\": \"页面已下架！\"\n    },\n    \"dataTableRelated\": {\n        \"notfound\": \"业务对象关系不存在！\",\n        \"exist\": \"业务对象关系已存在！\"\n    },\n    \"module\": {\n        \"notfound\": \"模块信息不存在！\"\n    },\n    \"businessSystem\": {\n        \"notfound\": \"业务系统不存在！\",\n        \"exist\": \"业务系统已存在！\"\n    },\n    \"application\": {\n        \"notfound\": \"应用不存在！\",\n        \"exist\": \"应用名称已存在！\"\n    },\n    \"dictionary\": {\n        \"notfound\": \"数据字典不存在！\",\n        \"exist\": \"数据字典编码已存在！\"\n    },\n    \"businessObjectColumn\": {\n      \"notfound\": \"业务对象列不存在\"\n    }\n  },\n  \"data-center\": {\n    \"buttons\": {\n      \"search\": \"查询\",\n      \"reset\": \"重置\",\n      \"add\": \"添加\",\n      \"edit\": \"编辑\",\n      \"save\": \"保存\",\n      \"delete\": \"删除\",\n      \"publish\": \"发布\",\n      \"loading-entity-table\": \"加载业务对象\",\n      \"test-link\": \"测试连接\",\n      \"business-object-authority\": \"业务对象权限\",\n      \"enable\": \"启用\",\n      \"disable\": \"禁用\",\n      \"cancel\":\"取消\"\n    },\n    \"data-base\": {\n      \"default\": \"数据库\",\n      \"name\": \"数据库名称\",\n      \"localhost\": \"数据库地址\",\n      \"type\": \"数据库类型\",\n      \"description\": \"数据库描述\",\n      \"account\": \"数据库账号\",\n      \"password\": \"数据库密码\",\n      \"is-outside-business-base\": \"是否外部业务库\",\n      \"options\": [\n        {\n          \"label\": \"SqlServer\",\n          \"value\": \"SqlServer\"\n        },\n        {\n          \"label\": \"MySql\",\n          \"value\": \"MySql\"\n        }\n      ],\n      \"tips\": {\n        \"success\": \"连接成功\"\n      }\n    },\n    \"business-object\": {\n      \"default\":\"业务对象\",\n      \"name\": \"业务对象名称\",\n      \"description\": \"业务对象描述\",\n      \"col-name\": \"字段名称\",\n      \"col-description\": \"字段描述\",\n      \"col-length\": \"长度\",\n      \"col-decimal\": \"小数点位数\",\n      \"col-defaultValue\": \"默认值\",\n      \"col-type\": \"类型\",\n      \"col-is-primary-key\": \"是否主键\",\n      \"col-is-nullable\": \"是否可为空\",\n      \"col-is-enable\": \"是否启用\",\n      \"state\": \"状态\",\n      \"state-options\": [\n        {\n          \"label\": \"未发布\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"已发布\",\n          \"value\": 1\n        }\n      ],\n      \"options\": [\n        {\n          \"label\": \"文本\",\n          \"value\": \"text\"\n        },\n        {\n           \"label\": \"长文本\",\n          \"value\": \"ntext\"\n        },\n        {\n          \"label\": \"数字\",\n          \"value\": \"number\"\n        },\n        {\n          \"label\": \"布尔值\",\n          \"value\": \"bool\"\n        },\n          {\n            \"label\": \"对象\",\n            \"value\": \"object\"\n          },\n          {\n            \"label\": \"文件\",\n            \"value\": \"file\"\n          }\n      ],\n      \"tips\": {\n        \"check-name\": \"字段名称重复\"\n      }\n    },\n    \"composite-object\": {\n      \"name\":\"组合对象名称\",\n      \"main-business-object\":\"主对象\",\n      \"parent-business-object-filed\": \"父级对象字段\",\n      \"parent-business-object\": \"父级对象\",\n      \"business-object\": \"子对象\",\n      \"business-object-filed\": \"子对象字段\",\n      \"join-type\":\"关联类型\",\n      \"join-type-options\":[\n        {\n          \"label\":\"等值连接\",\n          \"value\":1\n        },\n         {\n          \"label\":\"左联接\",\n          \"value\":2\n        }\n      ],\n       \"join-relation\":\"关联关系\",\n      \"join-relation-options\":[\n        {\n          \"label\":\"一对一\",\n          \"value\":1\n        },\n         {\n          \"label\":\"一对多\",\n          \"value\":2\n        }\n      ]\n    },\n    \"authority\": {\n      \"business-sys-code\": \"业务系统编码\",\n      \"business-sys-name\": \"业务系统名称\",\n      \"business-sys-describe\": \"业务系统描述\",\n      \"state\": \"状态\",\n      \"business-object-type\": \"业务对象类型\"\n    },\n    \"common\": {\n      \"please-select\": \"请选择\",\n      \"please-input\": \"请输入\",\n      \"serial-number\": \"序号\",\n      \"operation\": \"操作\"\n    }\n  },\n  \"fucntion-center\": {\n    \"page-list\": {\n      \"serial-number\": \"序号\",\n      \"name\": \"页面名称\",\n      \"type\": \"页面类型\",\n      \"type-options\": [\n        {\n          \"label\": \"查询列表\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"表单\",\n          \"value\": 2\n        },\n         {\n          \"label\": \"查询树列表\",\n          \"value\": 3\n        }\n      ],\n      \"create-date\": \"创建时间\",\n      \"operation\": \"操作\",\n      \"state\": \"状态\",\n      \"state-options\": [\n        {\n          \"label\": \"未发布\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"已发布\",\n          \"value\": 1\n        },\n          {\n          \"label\": \"已下架\",\n          \"value\": 2\n        }\n      ],\n      \"application\":\"所属应用\"\n    },\n    \"page-start\": {\n      \"name\": \"页面名称\",\n      \"data-source-type\": \"数据源类型\",\n      \"data-source-type-options\": [\n        {\n          \"label\": \"业务对象\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"视图\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Api\",\n          \"value\": 3\n        }\n      ],\n      \"combination-page\": \"组合模板\",\n      \"one-page\": \"单页模板\",\n      \"list\": \"查询列表\",\n      \"form\": \"表单\",\n      \"start-desgin\": \"开始设计\",\n      \"data-base\": \"数据库\",\n      \"business-object\": \"业务对象\",\n      \"application\":  \"所属应用\"\n    },\n    \"common\": {\n      \"please-select\": \"请选择\",\n      \"please-input\": \"请输入\",\n      \"serial-number\": \"序号\",\n      \"operation\": \"操作\"\n    },\n    \"buttons\": {\n      \"search\": \"查询\",\n      \"reset\": \"重置\",\n      \"add\": \"添加\",\n      \"edit\": \"编辑\",\n      \"save\": \"保存\",\n      \"delete\": \"删除\",\n      \"publish\": \"发布\",\n      \"enable\": \"启用\",\n      \"disable\": \"禁用\",\n      \"priview\": \"预览\",\n      \"desgin\": \"设计\",\n      \"copy\": \"复制\",\n      \"undercarriage\": \"下架\",\n      \"mountMenu\": \"挂载菜单\"\n    }\n  },\n  \"apiManage\": {\n    \"addApi\": \"添加API\",\n    \"apiCode\": \"代码\",\n    \"apiName\": \"API\",\n    \"showName\":\"名称\",\n    \"apiDesc\":\"描述\",\n    \"apiVersion\":\"版本\",\n    \"apiCategory\":\"类型\",\n    \"buttons\": {\n      \"uploadPlugin\": \"插件上传\",\n      \"pluginHelp\": \"插件开发帮助\"\n    },\n    \"statusDataset\": [\n        {\n          \"label\": \"作废\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"停用\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"启用\",\n          \"value\": \"1\"\n        }        \n      ],\n    \"apiCategoryDataset\": [\n        {\n          \"label\": \"代码\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"插件\",\n          \"value\": \"2\"\n        }        \n      ],\n      \"message\": {\n        \"onlyZipFile\": \"只能上传ZIP类型的文件\"\n      }\n  },\n  \"integration\": {\n    \"titles\": {\n      \"addApp\": \"添加应用\",\n      \"editApp\": \"编辑应用\",\n      \"authApi\": \"授权Api\",\n      \"cancelAuth\": \"取消授权\",\n      \"authApiGroup\": \"授权Api组合\",\n      \"apiList\": \"Api列表\",\n      \"apiGroupList\": \"Api组合列表\",\n      \"selectedApi\": \"已选Api\",\n      \"selectedApiGroup\": \"已选Api组合\",      \n      \"apiGroupRequestDesign\": \"组合请求入口设计\",\n      \"apiGroupServerDesign\": \"组合后端服务设计\",\n      \"apiGroupResultDesign\": \"组合返回结果设计\",\n      \"resultMapping\": \"结果映射\",\n      \"requestBody\": \"请求体\"\n    },\n		\"fields\": {\n			\"service\": \"服务\",\n			\"serviceType\": \"服务类型\",\n			\"serviceCode\": \"服务编码\",\n			\"serviceName\": \"服务名称\",\n			\"appCode\": \"应用编码\",\n			\"appName\": \"应用名称\",\n      \"appPassword\": \"应用密码\",\n			\"description\": \"描述\",\n			\"modelCode\": \"模型编码\",\n			\"modelName\": \"模型名称\",\n			\"remark\": \"备注\",\n			\"hostUrl\": \"Host URL\",\n			\"wsdlUrl\": \"WSDL URL\",\n			\"dbHost\": \"数据库服务器\",\n			\"userName\": \"用户名\",\n			\"password\": \"密码\",\n			\"dbName\": \"数据库\",\n			\"authenticationMode\": \"认证模式\",\n			\"certifiedAPI\": \"认证API\",\n			\"staticPassword\": \"静态密码\",\n			\"healthInterval\": \"健康检测时间间隔\",\n			\"healthTimeout\": \"健康检测超时时间\",\n			\"healthIntervalUnit\": \"健康检测时间间隔单位\",\n			\"healthTimeoutUnit\": \"健康检测超时时间单位\",\n			\"basicInfo\": \"基础信息\",\n			\"connectionConf\": \"连接配置\",\n			\"completed\": \"完成\",\n      \"affiliatedService\": \"所属服务\",\n      \"apiCode\": \"API编码\",\n      \"apiName\": \"API名称\",\n      \"apiUrl\": \"API请求路径\",\n      \"requestUrl\": \"请求路径\",      \n      \"requestModel\": \"请求方式\",\n      \"apiGroupName\": \"Api组合名称\",\n      \"publishStatus\": \"发布状态\",\n      \"dataType\": \"数据类型\",\n      \"requestSample\": \"请求示例\",\n      \"fieldAttribute\": \"字段属性\",\n      \"isRequired\": \"必填\",\n      \"requestParamModule\": \"入参请求模式\",\n      \"mappingType\": \"映射类型\",\n      \"fixedValue\": \"固定值\",\n      \"paramMapping\": \"参数映射\",\n      \"resultMapping\": \"结果映射\",\n      \"paramName\": \"参数名称\",\n      \"mappingValue\": \"映射值\",\n      \"rootNode\": \"根节点\"\n		},\n		\"validationExtend\": {\n			\"invalid\": \"无效\",\n      \"existed\": {\n        \"serviceCode\": \"服务编码已存在\",\n        \"apiCode\": \"API编码已存在\"\n      }\n		},\n		\"actions\": {\n			\"previous\": \"上一步\",\n			\"next\": \"下一步\",\n			\"viewDetail\": \"查看详情\",\n			\"cancelTable\": \"返回列表\",\n			\"addApi\": \"配置API\"\n		},\n		\"dicts\": {\n			\"serviceTypeDataset\": [{\n					\"label\": \"Http(s)\",\n					\"value\": \"Https\"\n				},\n				{\n					\"label\": \"Web Service\",\n					\"value\": \"WebService\"\n				},\n				{\n					\"label\": \"SQL Server\",\n					\"value\": \"SqlServer\"\n				},\n				{\n					\"label\": \"My SQL\",\n					\"value\": \"MySql\"\n				}\n			],\n			\"exchangeModelStatus\": [{\n				\"label\": \"草稿\",\n				\"value\": 0\n			}, {\n				\"label\": \"启用\",\n				\"value\": 1\n			}, {\n				\"label\": \"禁用\",\n				\"value\": -1\n			}],\n      \"publishStatusDataSet\": [\n          {\n            \"label\": \"未发布\",\n            \"value\": -1\n          },\n          {\n            \"label\": \"已下线\",\n            \"value\": 0\n          },\n          {\n            \"label\": \"已发布\",\n            \"value\": 1\n          }                 \n      ],\n      \"mappingTypeDataSet\": [\n          {\n            \"label\": \"参数映射\",\n            \"value\": \"ParamSchema\"\n          },\n          {\n            \"label\": \"结果映射\",\n            \"value\": \"ResultSchema\"\n          },\n          {\n            \"label\": \"固定值\",\n            \"value\": \"FixedValue\"\n          } \n      ],\n      \"requestParamModuleDataSet\": [\n          {\n            \"label\": \"参数透传\",\n            \"value\": \"Transparent\"\n          },\n          {\n            \"label\": \"参数映射\",\n            \"value\": \"Mapping\"\n          }\n      ]\n		},\n    \"messages\":{\n      \"notContainsApi\": \"Api组合里不包含任何Api\",\n      \"lessOneApi\": \"至少选择一个API\",\n      \"orderApi\": \"调整Api的顺序会导致部分映射参数丢失，是否确认？\",\n      \"requestPathFormat\": \"格式错误，请求路径请以\\\"/\\\"开头，例如：/user/info\",\n      \"apiGroupPublished\": \"Api组合已发布，不可编辑、删除或发布\",\n      \"apiGroupInvalid\": \"Api组合已作废，不可编辑、删除或发布\",\n      \"apiGroupNotFound\": \"Api组合不存在\",\n      \"cannotOffline\": \"非发布状态不可下线\",\n      \"resultDataTypeNotMapping\": \"第{0}行字段数据类型映射不匹配\",\n      \"apiParamDataTypeNotMapping\":\"Api：{0}，第{1}行参数数据类型映射不匹配\"\n    }    \n	},\n  \"rulesEngine\": {\n    \"fields\":{\n	  \"constantName\":\"常量名\",\n	  \"constantValue\":\"常量\",\n	  \"constantType\":\"常量类型\",\n	  \"addConstant\":\"新增规则常量\",\n	  \"editConstant\":\"编辑规则常量\",\n    \"variableName\":\"类型名\",\n	  \"variableValue\":\"类型\",\n	  \"remark\":\"说明\",\n	  \"addVariable\":\"新增参数类型\",\n	  \"editVariable\":\"编辑参数类型\",    \n    \"variableFieldName\":\"字段名\",\n	  \"variableFieldValue\":\"字段\",\n	  \"variableFieldType\":\"字段类型\",\n	  \"addVariableField\":\"新增字段\",\n	  \"editVariableField\":\"编辑字段\",\n    \"parameterName\":\"参数名\",\n	  \"parameterValue\":\"参数\",\n	  \"parameterType\":\"类型\",\n	  \"addParameter\":\"新增规则参数\",\n	  \"editParameter\":\"编辑规则参数\",\n    \"setName\":\"规则集名\",\n	  \"publishStatus\":\"发布状态\",\n	  \"lockedStatus\":\"锁定状态\",\n	  \"lastPublishDate\":\"最近发布时间\",\n	  \"versionNo\":\"版本号\",\n    \"addSet\":\"新增规则集\",\n	  \"editSet\":\"编辑规则集\"\n    },\n    \"buttons\":{\n      \"setField\":\"设置字段\",\n      \"setRule\":\"设置规则\",\n      \"setRef\":\"设置引用\",\n      \"publish\": \"发布\",\n      \"lock\":\"锁定\",\n      \"unlock\":\"解锁\",\n      \"cancelUnlock\":\"撤销解锁\"\n    }	   \n  }\n}\n','18781645dfaec8f6aa4082bdc6b21fa4','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(14,'en.customer.json','i18n','{\n    \"designers\": {\n        \"form-designer\": {\n            \"presets\": {\n                \"basic\": \"Basic Form Template\",\n                \"master\": \"Master Form Template\"\n            },\n            \"name\": \"Form Designer\",\n            \"save-success\": \"Save Success\",\n            \"container-loop-error\": \"Container component can not contains another container compnent!\",\n            \"base-info\": \"Basic Info\",\n            \"approval-info\": \"Approval Info\",\n            \"relation\": \"Relations\",\n            \"attachment\": \"Attachment\",\n            \"record\": \"Record\",\n            \"comment\": \"Comment\"\n        },\n        \"group\": {\n            \"base-components\": \"Base Components\",\n			\"business-components\": \"Business Components\",\n            \"layout-components\": \"Layout Components\",\n            \"detail-components\": \"Detail Components\",\n            \"component-property\": \"Component Property\",\n            \"form-property\": \"Form Property\"\n        },\n        \"control-property\": {\n            \"id\": \"ID\",\n            \"name\": \"Name\",\n            \"code\": \"Code\",\n            \"required\": \"Required\",\n            \"value\": \"Default Value\",\n            \"colspan\": \"ColSpan\",\n            \"colspan-1\": \"One\",\n            \"colspan-2\": \"Two\",\n            \"dataset\": \"Dataset\",\n            \"control-type\": \"Control\",\n            \"tips\": \"Tips\",\n            \"limit\": \"Limit\",\n            \"business-type\": \"Business Type\",\n            \"business-object\": \"Business Object\",\n            \"field\": \"Field\",\n            \"custom\": \"Custom\",\n            \"custom-field\": \"Custom Field\",\n            \"add-custom-field\": \"Add Custom Field\",\n            \"non-business-object\": \"No Business Object\",\n            \"child-table\": \"Child Table\",\n            \"setting\": \"Setting\"\n        },\n        \"components\": {\n            \"text\": \"Text\",\n            \"input\": \"Input\",\n            \"textarea\": \"Textarea\",\n            \"number\": \"Number\",\n            \"autocomplete\": \"Autocomplete\",\n            \"password\": \"Password\",\n            \"select\": \"Select\",\n            \"datetime\": \"Datetime\",\n            \"switch\": \"Switch\",\n            \"checkbox\": \"Checkbox\",\n            \"radio\": \"Radio\",\n            \"picture\": \"Picture\",\n            \"attchment\": \"Attchment\",\n            \"link\": \"Link\",\n            \"modal\": \"Modal Select\",\n            \"cascader\": \"Cascader\",\n            \"table\": \"Table\",\n            \"group\": \"Group\",\n            \"chart\": \"Chart\",\n            \"history\": \"History\"\n        }\n    },\n    \"framework\": {\n        \"enabled\": \"Enabled\",\n        \"disabled\": \"Disabled\",\n        \"loading\": \"Loading\",\n        \"favorite\": \"Favorite Processes\",\n        \"helper\": \"Process Helper\",\n        \"refreshCache\": \"Refresh Cache\",\n        \"stop-impersonate\": \"Stop Impersonate\",\n        \"authorization\": \"Authorization\",\n        \"authorizated\": \"Authorizated\",\n        \"approval\": \"Approval\",\n        \"handover\": \"Handover\",\n        \"proxy\": \"Proxy\",\n        \"logout\": \"Logout\",\n        \"state-timeout\": \"Login state timeout\",\n        \"state-timeout-relogin\": \"Login state timeout, please re-login.\",\n        \"state-timeout-relogin-help\": \"Click \\\"OK\\\" button below to re-login.\",\n        \"request-failed\": \"Request failed.\",\n        \"upload-failed\": \"Upload failed\",\n        \"notification\": \"Notification\",\n        \"nonpermission\": \"Permission denied!\",\n        \"add\": \"Add\",\n        \"export\": \"Export\",\n        \"delete\": \"Delete\",\n        \"delete-info\": \"Are you sure to delete this task?\",\n        \"recall-info\": \"Are you sure to recall this task\",\n        \"recall-counter-sign-info\": \"Are you sure to recall this task\",\n        \"save\": \"Save\",\n        \"load-more\": \"Load More\",\n        \"create\": \"Create\",\n        \"ok\": \"OK\",\n        \"operate\": \"Operate\",\n        \"cancel\": \"Cancel\",\n        \"empty\": \"Empty\",\n        \"unauthorized\": \"You are not authorized to access this page\",\n        \"no\": \"No\",\n        \"prepositions\": {\n            \"s\": \"\'s \",\n            \"space\": \" \",\n            \"colon\": \": \",\n            \"hour\": \"h\",\n            \"total\": \"Total\",\n            \"items\": \"Items\"\n        },\n        \"systemAutomatic\": \"System Automatic\",\n        \"download\": \"Download\"\n    },\n	\"messages\": {\n		\"success\": \"The operation was successful\",\n		\"fail\": \"The operation was fail\"\n	},\n    \"controlPoint-columns\": {\n        \"no\": \"No\",\n        \"name\": \"Control Point\",\n        \"actual\": \"Actual\",\n        \"light\": \"Light\",\n        \"standard\": \"Standard\",\n        \"lightsType\": [{\n                \"label\": \"Red\",\n                \"value\": \"red\"\n            }, {\n                \"label\": \"Yellow\",\n                \"value\": \"yellow\"\n            }, {\n                \"label\": \"Green\",\n                \"value\": \"green\"\n            }\n        ]\n    },\n    \"relation-columns\": {\n        \"no\": \"No\",\n        \"number\": \"Number\",\n        \"topic\": \"Topc\",\n        \"organization\": \"Organization\",\n        \"start-user\": \"Start User\",\n        \"start-time\": \"Start Time\"\n    },\n    \"attachment-columns\": {\n        \"no\": \"No\",\n        \"name\": \"Name\",\n        \"size\": \"Size\",\n        \"upload-user\": \"Upload User\",\n        \"upload-step\": \"Upload Step\",\n        \"upload-time\": \"Upload Time\"\n    },\n    \"approver-columns\": {\n        \"step-name\": \"Step Name\",\n        \"user-name\": \"Approver\",\n        \"state-name\": \"State Name\",\n        \"comment-text\": \"Comment Text\",\n        \"resolve-time\": \"Resolve Time\"\n    },\n    \"inbox-columns\": {\n        \"instance-number\": \"Instance Number\",\n        \"topic\": \"Topic\",\n        \"start-date\": \"Start Date\",\n        \"start-username\": \"Start UserName\",\n        \"lastest-activity-name\": \"Latest Activity Name\",\n        \"lastest-arrive-time\": \"Latest Arrive Time\",\n        \"approve-time\": \"Approve Time\",\n        \"status\": \"Status\",\n        \"operation\": \"Operate\",\n        \"reason\": \"Reason\",\n        \"attachment\": \"Attachment\",\n        \"actions\": \"Actions\",\n        \"add-date\": \"Add Date\",\n        \"start-begin-date\": \"Start Begin Date\",\n        \"start-end-date\": \"Start End Date\",\n        \"begin-date\": \"Begin Date\",\n        \"end-date\": \"End Date\",\n        \"start-user-organization\": \"Start User Organization\",\n        \"originator-username\": \"Originator Username\",\n        \"retention-time\": \"Retention Time\",\n        \"retention-time-unit\": {\n            \"retention-time-hour\":\"h\",\n            \"retention-time-day\": \"d\",\n            \"retention-time-minute\":\"min\"\n        },\n        \"current-user\": \"Current User\",\n        \"approve-status\": \"Approve Status\",\n        \"have-attachment\": \"Have Attachment\",\n        \"business-type\": \"Business Type\",\n        \"read\":\"Read/UnRead\",\n        \"readOptions\":[\n            {\n                \"label\":\"Read\",\n                \"value\":\"read\"\n            },\n            {\n                \"label\":\"UnRead\",\n                \"value\":\"unRead\"\n            }\n        ]\n    },\n    \"authorization-helper\": {\n        \"current-user\": \"Current User\",\n        \"my-angents\": \"My Agents\",\n        \"enable\": \"Enable\",\n        \"user\": \"User\",\n        \"authorizated-user\": \"Authorizated User\",\n        \"authorizated-process\": \"Authorizated Process\",\n        \"start-time\": \"Start Time\",\n        \"end-time\": \"End Time\",\n        \"description\": \"Description\",\n        \"detail\": \"Detail\",\n        \"noSelectProcess\": \"No Select process\",\n        \"no\":\"No\",\n        \"business-type\":\"Business Type\",\n        \"process-name\":\"Process Name\"\n    },\n    \"components\": {\n        \"search\": \"Search\",\n        \"reset\": \"Reset\",\n        \"add\": \"Add\",\n        \"edit\": \"Edit\",\n        \"cancel\": \"Cancel\",\n        \"delete\": \"Delete\",\n        \"advanced-search\": \"Advanced Search\",\n        \"text\": \"Please enter\",\n        \"select\": \"Please select\",\n        \"department\": \"Department\",\n        \"user\": \"User\",\n        \"process\": \"Process\",\n        \"back\": \"Back\",\n        \"select-btn\": \"Select\",\n        \"authorization-process-view\":\"Authorization Process View\"\n    },\n    \"main\": {\n        \"process-map\": \"Process Map\",\n        \"todo\": \"To Do\",\n        \"cc-to-me\": \"CC To Me\",\n        \"my-processes\": \"My Processes\",\n        \"exceptions\": \"Exceptions\",\n        \"done\": \"Done\",\n        \"draft\": \"Draft\",\n        \"select-all\": \"Select All\",\n        \"batch-approve\": \"Batch Approve\",\n        \"batch-reject\": \"Batch Reject\",\n        \"favorites\": \"Favorites\",\n        \"print\": \"Print\",\n        \"printSettings\": \"Print Settings\",\n        \"notice\": \"CC\",\n        \"flow-map\": \"Flow Map\",\n        \"process-choose\": \"Select the process\",\n        \"ready-status\": \"Ready\",\n        \"form\": {\n            \"module\": [\n                {\n                \"label\": \"BaseInfo\",\n                \"value\": \"baseInfo\"\n                },\n                {\n                \"label\": \"Relation\",\n                \"value\": \"relation\"\n                },\n                {\n                \"label\": \"Attachment\",\n                \"value\": \"attachment\"\n                },\n                {\n                \"label\": \"Record\",\n                \"value\": \"record\"\n                }\n            ]\n        },\n        \"SelectPrintModule\": \"Select Print Module\"\n    },\n    \"process-map\": {\n        \"favorite-processes\": \"Favorite Processes\",\n        \"all-processes\": \"All Processes\",\n        \"process-name\": \"Process Name\",\n        \"belong-company\": \"Belong Organization\",\n        \"hot-words\": \"Hot Words\",\n        \"top-type\": \"Top Type\"\n    },\n    \"anchor-items\": {\n        \"base-info\": \"Base Info\",\n        \"form-info\": \"Form Info\",\n        \"attachment\": \"Attachments\",\n        \"comment\": \"Comment\",\n        \"approveProcess\": \"Approve Process\",\n        \"process-type\":\"Process Type\",\n        \"processDeduction\": \"Process Deduction\"\n    },\n    \"instance\": {\n        \"states\": {\n            \"start\": \"Start\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"processing\": \"Processing\",\n            \"todo\": \"Todo\"\n        },\n        \"errors\": {\n            \"invalid-form\": \"Invalid form, can\'t start.\"\n        },\n        \"number\": \"Number\",\n        \"actions\": {\n            \"start\": \"Start\",\n            \"restart\": \"ReStart\",\n            \"save\": \"Save\",\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"handover\": \"Handover\",\n            \"counter-sign\": \"Counter Sign\",\n            \"notice\": \"Notice\",\n            \"discuss\": \"Discuss\",\n            \"cancel\": \"Cancel\",\n            \"refuse\": \"Refuse\",\n            \"recall\": \"Recall\",\n            \"urging\": \"Urging\",\n            \"recall-counter-sign\":\"Recall Counter Sign\",\n            \"receive\": \"Receive\"\n        },\n        \"action-page\": {\n            \"sign\": {\n                \"sign-type\": \"Sign Type: \",\n                \"extra-append\": \"Extra Append\",\n                \"extra-insert\": \"Extra Insert\",\n                \"user\": \"Sign User\",\n                \"comment\": \"Please enter sign comment\",\n                \"info\": \"\",\n                \"info-insert\": \"\",\n                \"info-append\": \"\",\n                \"limit\":\"The number of opinions has reached the upper limit\"\n            },\n            \"reject\": {\n                \"reject-step\": \"Reject Step\",\n                \"reject-type\": \"Reject Type\",\n                \"comment\": \"Please enter reject comment\",\n                \"reject-new\": \"Reject\",\n                \"reject-direct\": \"Reject Direct\",\n                \"info\": \"\",\n                \"reject-new-info\": \"\",\n                \"reject-direct-info\": \"\"\n            },\n            \"notice\": {\n                \"comment\": \"Please enter notic comment\",\n                \"user\": \"Notice User\"\n            },\n            \"handover\": {\n                \"comment\": \"Please enter handover comment\",\n                \"user\": \"Handover User\",\n                \"info\": \"\",\n                \"info-handover\": \"\",\n                \"info-user\": \"\",\n                \"notAsignSelf\":\"The approver can not sign and hand it over to himself\",\n                \"limit\":\"The number of entrustments has reached the upper limit\"\n            },\n            \"discuss\": {\n                \"comment\": \"Please enter discuss comment\",\n                \"user\": \"Discuss User\"\n            },\n            \"approve\": {\n                \"tips\": \"\",\n                \"comment\": \"Please enter approve comment\"\n            },\n            \"cancel\": {\n                \"tips\": \"\",\n                \"comment\": \"Please enter cancel comment\"\n            },\n            \"refuse\": {\n                \"tips\": \"\",\n                \"comment\": \"Please enter reufse comment\"\n            },\n            \"re-start\": {\n                \"title\": \"Start Comment\",\n                \"comment\": \"Please enter start comment\",\n                \"not-agent\":\"You cannot initiate on behalf of this user\"\n            },\n            \"delay\": {\n               \"limit\":\"The number of delay has reached the upper limit\" \n            }\n        },\n        \"notice\": {\n            \"start-page\": \"Start Page\",\n            \"approve-page\": \"Approve Page\",\n            \"start-success\": \"Start Success\",\n            \"start-warning\": \"According to the form data, no matching process is found. Please check if there are any required items, or contact the administrator.\",\n            \"matching-process-fail\": \"Matching Process Fail\",\n            \"start-steps-unhealthy\": \"Step approvers can\'t be empty, start failed.\",\n            \"start-steps-repeat\": \"Duplicate process step name\",\n            \"urging-success\": \"Urging Success\",\n            \"save-success\": \"Save Success\",\n            \"done-success\": \"Done Success\",\n            \"sign-success\": \"Sign Success\",\n            \"reject-success\": \"Reject Success\",\n            \"notice-success\": \"Notice Success\",\n            \"handover-success\": \"Handover Success\",\n            \"discuss-success\": \"Discuss Success\",\n            \"collection-success\": \"Collection Success\",\n            \"collection-cancel\": \"Collection Cancel\",\n            \"cancel-success\": \"Cancel Success\",\n            \"refuse-success\": \"Refuse Success\",\n            \"delay-success\": \"Delay Success\",\n            \"receive-success\": \"Receive Success\",\n            \"todo\": \"Approve Page\",\n            \"approve\": \"Detail Page\",\n            \"cc-to-me\": \"Detail Page\",\n            \"my-processes\": \"Detail Page\",\n            \"done\": \"Detail Page\",\n            \"start\": \"Start Page\",\n            \"version-inconsistency\": \"Version inconsistency\",\n            \"data-exists\": \"Process data already exists and cannot be initiated. Please check whether the boid is duplicate!\",\n            \"batch-done-success\": \"Batch Done Success\",\n            \"invalid-comments\":\"After the rework, the sponsor modifies the key fields, resulting in changes in the approval process and automatic invalidation of the original process.\",\n            \"change-process1\":\"The newly submitted content will lead to changes in the approval process and personnel. Please confirm whether to switch to the new process？\",\n            \"change-process2\":\"Click \'OK\' : the new process will be re-approved step by step, and the original process will automatically become invalid,\",\n            \"change-process3\":\"Click \'Cancel\' : you can return to the business system to modify and initiate again,\",\n            \"change-process-title\":\"Confirm whether to switch to the new process\",\n            \"step\":\"step\",\n            \"process-approver-limited\":\"The number of process approvers cannot be greater than\",\n            \"node-approver-limited\":\"The number of approvers in the step cannot be greater than\",\n            \"node-approver-limited-message\":\"This node cannot have more than {0} approvers. Please contact the administrator to adjust!\",\n            \"process-approver-limited-message\":\"Please note that there are more than {0} persons in charge of the process you initiated. Please confirm the effectiveness of the process initiation and launch it cautiously!\",\n            \"continue-start\":\"Continue start\",\n            \"reminder\":\"Warm prompt\",\n             \"info\":\"Info\",\n            \"rejectConfirm\": \"Are you sure you want to return this task?\"\n        },\n        \"preview\": {\n            \"name\": \"Preview\"\n        },\n        \"form-container\": {\n            \"name\": \"Approval Info\",\n            \"user\": \"User\",\n            \"organization\": \"Organization\",\n            \"addCheck\": \"Please select organization\",\n            \"selectedOrganization\": \"Selected Organization\"\n        },\n        \"base-info\": {\n            \"name\": \"Instance Info\",\n            \"topic\": \"Topic\",\n            \"start-user\": \"Applicant\",\n            \"organization\": \"Organization\",\n            \"start-user-position\": \"Applicant Position\",\n            \"start-time\": \"Start Time\"\n        },\n        \"record\": {\n            \"name\": \"Approval Record\",\n            \"duration\": \"Duration\",\n            \"user-name\": \"User Name\",\n            \"step-name\": \"Step Name\",\n              \"skippedWhenEmptyResolver\": \"Skipped When Empty Resolver\",\n            \"skippedWhenSameApprover\": \"Skipped When Same Approver\"\n        },\n        \"comment\": {\n            \"detail-name\": \"Approal Comment\",\n            \"start-name\": \"Start Comment\",\n            \"can-input\": \"You can also type \",\n            \"word\": \" characters\"\n        },\n        \"relation\": {\n            \"name\": \"Relation Instance\",\n            \"operate-name\": \"Relation Instance\",\n            \"relation\": \"Relation\",\n            \"type\": \"Relation Type\",\n            \"my-start\": \"My Start\",\n            \"my-cc\": \"My CC\",\n            \"my-done\": \"My Done\"\n        },\n        \"deduction\": {\n            \"name\": \"Process Deduction\"\n        },\n        \"controlPoint\": {\n            \"name\": \"Control Point\"\n        },\n        \"attachment\": {\n            \"name\": \"Attachment\",\n            \"operate-name\": \"Upload Attachment\"\n        },\n        \"approver\": {\n            \"name\": \"Approval Progress\"\n        },\n        \"compentions\": {\n            \"process-topic\": {\n                \"new\": \"New\",\n                \"summary\": \"Summary\"\n            }\n        },\n        \"approval-comment\": {\n            \"title\": \"Commonly Used Comment: \",\n            \"define\": \"Define\",\n            \"needSave\": \"Please save\",\n            \"no\": \"No.\",\n            \"subject\": \"Subject\",\n            \"comment\": \"Comment\",\n            \"operation\": \"Operation\",\n            \"modalTitle\": \"Define Comment\",\n            \"add\": \"Add\",\n            \"info\": \"Info：Please input the title and detail. You can drag and drop to sort items.\",\n            \"more\": \"More\"\n        },\n        \"send-recv-record\":{\n            \"name\":\"send recv record\"\n        }\n    },\n    \"instance-start\": {\n        \"actions\": {\n            \"start\": \"Start\",\n            \"save\": \"Save\"\n        },\n        \"start-comment\": {\n            \"name\": \"Start Comment\"\n        },\n        \"preview\": {\n            \"name\": \"Preview\",\n            \"deafult-step-name\": \"New Step\",\n            \"approve-user\": \"Approve User\",\n            \"cc-user\": \"Carbon Copy User\",\n            \"role-name\": \"Role Name\",\n            \"step\": \"Step\"\n        },\n        \"start-success\": \"Start success\"\n    },\n    \"table\": {\n        \"table-category\": \"TableCategory\",\n        \"table-urgency\": \"Tableurgency\"\n    },\n    \"main-title\": {\n        \"top-title\": \"BPM Approval platform\"\n    },\n    \"login\": {\n        \"title\": \"Mengtuo BPM Process Management Platform\",\n        \"user-input\": \"User name\",\n        \"psd-input\": \"Password\",\n        \"user-title\": \"User Login\",\n        \"login-btn\": \"Login\",\n        \"code\": \"Code\",\n        \"code-change\": \"Change\",\n        \"leave\":\"Leave\",\n        \"inValid\":\"InValid\"\n    },\n    \"user\": {\n        \"phone-number\": \"Phone\",\n        \"department-name\": \"Department\",\n        \"email\": \"Email\",\n        \"position-level\": \"Position Level\",\n        \"organization\": \"Organization\"\n    },\n    \"todo\": {\n        \"batch-operate-info\": \"Please select instance! \"\n    },\n    \"copyright\": \"© 2020All rights reserved version of mentor software: v4.0 if you have any questions, please contact it service center  400 1234 1111\",\n    \"libang\": {\n        \"purchase-order\": {\n            \"name\": \"Purchase Order\",\n            \"is-completion\": \"IsCompletion\",\n            \"supplier\": \"Supplier Name\",\n            \"input-factory\": \"InputFactory\",\n            \"material-number\": \"Material Number\",\n            \"material-name\": \"Material Name\",\n            \"project-number\": \"Project Number\",\n            \"quantity\": \"Quantity\",\n            \"transfer\": \"Transfer\",\n            \"discount\": \"Discount\",\n            \"price\": \"Price\",\n            \"new-price\": \"New Price\",\n            \"report-name\": \"Purchase Order Report\"\n        },\n        \"retry-info\": \"Are you sure to retry this task? \",\n	    \"actions\": {\n            \"retry\": \"Retry\"\n        }\n    }\n}','185c9c55673ef9eaaf1c604e2b5a2b33','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(15,'medusa.service.rules.engine.appsettings.json','rules-engine','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Debug\",\n      \"System\": \"Information\",\n      \"Microsoft\": \"Information\"\n    }\n  },\n  \"Persistence\": {\n    \"RulesEngine\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=RulesEngine;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"LogSettings\": {\n    \"QueryDbType\": \"MongoDB\",\n    \"DBName\": \"NLog\",\n    \"MongoDBContext\": \"mongodb://localhost:27017\",\n    \"OperationLog\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    }\n  },\n    \"CronJob\": {\n        \"Host\": \"http://*************\",\n        \"Port\": \"32004\",\n        \"Uri\": \"/job\",\n        \"BasicUserName\": \"bpm\",\n        \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n        \"DefaultTimeOut\": 180000\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": 30,\n        \"IsHttps\": false\n    },\n    \"ApiUrl\": {\n        \"BusinessObject\": \"/modeling/v1/business-objects?is-all=true\",\n        \"BusinessObjectField\": \"/modeling/v1/business-objects/{0}\",\n        \"BusinessObjectQuery\": \"/modeling/v1/query/{0}\",\n        \"BusinessObjectData\": \"/modeling/v1/update/{0}\"\n    }  \n}','488ebaefe73d62fde11b741306872265','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(16,'en.json','i18n','{\n  \"registrationFailed\": \"Product registration failed!\",\n  \"notRegistered\": \"The product has not been registered.\",\n  \"notFound\": \"{0} data does not exist!\",\n  \"notFoundFunction\": \"No implementation method found!\",\n  \"entityNotFound\": \"Entity does not exist! \",\n  \"modelCheck\": \"{0} is null! \",\n  \"deleteCheck\": \"Please delete {0} children! \",\n  \"existNameOrCore\": \"Name or Code is existed! \",\n  \"existCore\": \"Code is existed! \",\n  \"main-title\": {\n    \"top-title\": \"BPM Management\"\n  },\n  \"instance\": {\n    \"notice\": {\n      \"version-inconsistency\": \"Version inconsistency\",\n      \"data-exists\": \"Process data already exists and cannot be initiated. Please check whether the boid is duplicate!\",\n      \"data-repeat-submit\":\"Please do not resubmit!\",\n      \"data-repeat-recall\":\"Please do not recall it again!\",\n      \"data-repeat-delay\":\"Please do not delay it again!\",\n      \"data-invalid-resubmit\": \"Data {0} is invalid, please do not submit it again!\",\n      \"data-approve-resubmit\": \"Data {0} is done, please do not submit it again!\",\n      \"no-allowed-recall\": \"No allowed Recall\"\n    }\n  },\n  \"framework\": {\n    \"enabled\": \"Enabled\",\n    \"disabled\": \"Disabled\",\n    \"loading\": \"Loading\",\n    \"favorite\": \"Favorite Processes\",\n    \"helper\": \"Process Helper\",\n    \"stop-impersonate\": \"Stop Impersonate\",\n    \"authorization\": \"Authorization\",\n    \"handover\": \"Handover\",\n    \"proxy\": \"Proxy\",\n    \"logout\": \"Logout\",\n    \"state-timeout\": \"Login state timeout\",\n    \"state-timeout-relogin\": \"Login state timeout, please re-login.\",\n    \"state-timeout-relogin-help\": \"Click \\\"OK\\\" button below to re-login.\",\n    \"request-failed\": \"Request failed.\",\n    \"upload-failed\": \"Upload failed\",\n    \"notification\": \"Notification\",\n    \"nonpermission\": \"Permission denied!\",\n    \"add\": \"Add\",\n    \"export\": \"Export\",\n    \"delete\": \"Delete\",\n    \"delete-info\": \"Are you sure to delete this task?\",\n    \"delete-info-param\": \"Are you sure to delete this {{value}}?\",\n    \"recall-info\": \"Are you sure to recall this task\",\n    \"save\": \"Save\",\n    \"load-more\": \"Load More\",\n    \"create\": \"Create\",\n    \"ok\": \"OK\",\n    \"operate\": \"Operate\",\n    \"cancel\": \"Cancel\",\n    \"empty\": \"Empty\",\n    \"unauthorized\": \"You are not authorized to access this page\",\n    \"prepositions\": {\n      \"s\": \"\'s \",\n      \"space\": \" \",\n      \"colon\": \": \",\n      \"hour\": \"h\",\n      \"total\": \"Total\",\n      \"items\": \"Items\"\n    },\n    \"headerUserNotfound\": \"Missing Header[\\\"current_user\\\"], please put user info (base64) in it.\"\n  },\n  \"boost\": {\n    \"layout\": {\n      \"home\": \"Home\",\n      \"logout\": \"Logout\",\n      \"refreshCache\": \"Refresh Cache\",\n      \"login\": {\n        \"form\": {\n          \"title\": \"Mengtuo BPM Process Management Platform\",\n          \"user-input\": \"User name\",\n          \"psd-input\": \"Password\",\n          \"user-title\": \"User Login\",\n          \"login-btn\": \"Login\",\n          \"code\": \"Code\",\n          \"code-change\": \"Change\"\n        },\n        \"timeout\": {\n          \"title\": \"Login timeout\",\n          \"message\": \"You are going to redirect to the login page...\"\n        }\n      }\n    },\n    \"utils\": {\n      \"selectPromptImage\": \"/assets/images/no-result-tips-en.png\"\n    }\n  },\n  \"frontButtons\": {\n    \"post\": \"Post\",\n    \"save\": \"Save\",\n    \"cancel\": \"Cancel\",\n    \"reject\": \"Reject\"\n  },\n  \"buttons\": {\n    \"read\": \"Read\",\n    \"add\": \"Add\",\n    \"agent\": \"Agent\",\n    \"back\": \"Back\",\n    \"cancel\": \"Cancel\",\n    \"delete\": \"Delete\",\n    \"edit\": \"Edit\",\n    \"export\": \"Export\",\n    \"import\": \"Import\",\n    \"handover\": \"Handover\",\n    \"iam\": \"IAM\",\n    \"ok\": \"Ok\",\n    \"reset\": \"Reset\",\n    \"save\": \"Save\",\n    \"search\": \"Search\",\n    \"searching\": \"Searching\",\n    \"user\": \"User\",\n    \"notuser\": \"Not User\",\n    \"parameterType\": \"Parameter Type\",\n    \"startUserLogic\": \"StartUser logical\",\n    \"unStartUserLogic\": \"Non startUser logical fetching\",\n    \"cancel-modify\": \"Cancel Modify\",\n    \"chooseUser\": \"Choose User\",\n    \"impersonate\": \"Impersonate\",\n    \"form\": \"Form Edit\",\n    \"node\": \"Node Edit\",\n    \"addNode\": \"Add Node\",\n    \"up\": \"Up\",\n    \"down\": \"Down\",\n    \"move\": \"Move to here\",\n    \"repush\": \"Repush\",\n    \"publish\": \"publish\",\n    \"save-publish\": \"save and publish\",\n    \"refresh\": \"Refresh\",\n    \"create\": \"Create\",\n    \"choose\": \"Choose\",\n    \"download\": \"Download\",\n    \"upload\": \"Upload\",\n    \"all\": \"All\",\n    \"published\": \"Published\",\n    \"unpublished\": \"Unpublished\",\n    \"changing\": \"Changing\",\n    \"user-iam\": \"Role Members\",\n    \"menu-iam\": \"Menu IAM\",\n    \"data-iam\": \"Data IAM\",\n    \"createMatrix\": \"Create Matrix\",\n    \"binding\": \"Binding\",\n    \"unbundling\": \"Unbundling\",\n    \"enable\": \"Enable\",\n    \"disable\": \"Disable\",\n    \"batchDisable\": \"Batch Disable\",\n    \"newVersion\": \"Create New Version\",\n    \"startFieldRight\": \"Start Field Right\",\n    \"endFieldRight\": \"End Field Right\",\n    \"fieldRight\": \"Field Right\",\n    \"query\": \"Query\",\n    \"design\": \"Design\",\n    \"close\": \"Close\",\n    \"finish\": \"Finish\",\n    \"batchAdd\": \"Batch Add\",\n    \"syncAuth\": \"Sync Auth\",\n    \"retry\": \"Retry\",\n    \"batchRetry\": \"Batch Retry\",\n    \"promote\": \"Promote\",\n    \"batchDelete\": \"Batch Delete\",\n    \"archiveAll\": \"Archive All Queues\",\n    \"archive\": \"Archive current Queue\",\n    \"addEntityObject\": \"Add Entity Object\",\n    \"batch-void\": \"Batch Void\",\n    \"batch-cc\": \"Batch CC\",\n    \"process-intervention\": \"Process Intervention\",\n    \"interv-step-user\": \"Step User Intervention\",\n    \"interv-form-data\": \"Form Data Intervention\",\n    \"interv-approval-comments\": \"Approval Comments Intervention\",\n    \"intervention-record\": \"Intervention Record\",\n    \"process-info\": \"Process Info\",\n    \"AuthApi\": \"Auth Api\",\n    \"authApiGroup\": \"Auth Api Group\",\n    \"offline\": \"Offline\",\n    \"nextStep\": \"Next Step\",\n    \"previousStep\": \"Previous Step\",\n    \"node-event\": \"Node Event\",\n    \"submit-after\": \"Submit After\",\n    \"batch-import\": \"Batch Import\",\n    \"batch-export\": \"Batch Export\",\n    \"exportApi\": \"Export API\",\n    \"refreshApi\": \"Refresh API\",\n    \"bindApi\": \"Bind API\",\n    \"batch-auth\": \"Batch Authorize\",\n    \"batch-publish\": \"Batch Publish\",\n    \"copyLink\": \"Copy Link\",\n    \"copyDownload\": \"Copy Download\"\n  },\n  \"paginations\": {\n    \"total\": \"Total {{value}} items\"\n  },\n  \"controls\": {\n    \"input\": \"Please input \",\n    \"select\": \"Please select \",\n    \"upload\": \"Please upload \",\n    \"selectItems\": \"Please select data \",\n    \"serachText\": \"Please input search text \"\n  },\n  \"units\": {\n    \"hour\": \" Hour\"\n  },\n  \"messages\": {\n    \"delete\": \"Sure to delete? \",\n    \"cancel-modify\": \"Sure to cancel modify? \",\n    \"interven\": \"Sure to jump?\",\n    \"success\": \"The operation was successful\",\n    \"fail\": \"The operation was fail\",\n    \"exists\": \"{{value}} already exists\",\n    \"reset\": \"Sure to reset?\",\n    \"publish\": \"Sure to pubulish?\",\n    \"offline\": \"Sure to offline\",\n    \"intervention-save\": \"After the intervention is saved, it will take effect in about 5 seconds.\",\n    \"canNotEmpty\": \"{{value}} cannot be empty\",\n    \"copySuccess\":\"Copy succeeded\",\n    \"copyFailed\":\"Copy failed\"\n  },\n  \"platform\": {\n    \"role-list\": \"Role List\",\n    \"columns\": {\n      \"organization\": \"Organization\",\n      \"role\": \"Role\",\n      \"des\": \"Descripiton\",\n      \"user\": \"User\",\n      \"operate\": \"Operate\"\n    },\n    \"routes\": {\n      \"user\": \"User Management\",\n      \"log\": \"Log Management\",\n      \"operationLog\": \"Operation Log\",\n      \"organization\": \"Organization\",\n      \"company\": \"Company\",\n      \"department\": \"Department\",\n      \"positionLevel\": \"PositionLevel\",\n      \"project\": \"Project\",\n      \"system\": \"System Management\",\n      \"dataPermission\": \"Data Permission\",\n      \"dictionary\": \"Dictionary\",\n      \"menu\": \"Menu\",\n      \"platform\": \"Platform\",\n      \"home\": \"Home\",\n      \"login\": \"Login\",\n      \"loginout\": \"Loginout\",\n      \"roleMenu\": \"Role Menu\",\n      \"userHome\": \"User Center\",\n      \"userEdit\": \"User Edit\",\n      \"userDetail\": \"User Detail\",\n      \"message\": \"Message\",\n      \"messageRule\": \"Message Rule\",\n      \"messageTemplate\": \"Message Template\"\n    },\n    \"fields\": {\n      \"id\": \"ID\",\n      \"company\": \"Company\",\n      \"position\": \"Position\",\n      \"department\": \"Department\",\n      \"organization\": \"Organization\",\n      \"project\": \"Project\",\n      \"positionLevel\": \"PositionLevel\",\n      \"role\": \"Role\",\n      \"leader\": \"Leader\",\n      \"phone\": \"Phone\",\n      \"city\": \"City\",\n      \"remark\": \"Remark\",\n      \"code\": \"Code\",\n      \"name\": \"Name\",\n      \"menu\": \"Menu\",\n      \"englishName\": \"English Name\",\n      \"chineseName\": \"Chinese Name\",\n      \"type\": \"Type\",\n      \"illustration\": \"Illustration\",\n      \"description\": \"Description\",\n      \"keyword\": \"Keyword\",\n      \"operation\": \"Operation\",\n      \"parameter\": \"Parameter\",\n      \"user\": \"User\",\n      \"order\": \"Order\",\n      \"yes\": \"Yes\",\n      \"no\": \"No\",\n      \"detail\": \"Detail\",\n      \"level\": \"Level\",\n      \"account\": \"Account\",\n      \"password\": \"Password\",\n      \"status\": \"Status\",\n      \"message\": \"Message\",\n      \"inUse\": \"InUse\",\n      \"language\": \"Language\",\n      \"inStart\": \"InStart\",\n      \"frequency\": \"Frequency\",\n      \"orgLevel\": \"Organition Level\",\n      \"valid\": \"Valid\",\n      \"inValid\": \"InValid\",\n      \"primaryPosition\": \"Primary Position\",\n      \"roleMenu\": \"Role Menu\",\n      \"bizid\": \"Bizid\",\n      \"companyId\": \"Company Id\",\n      \"departmentId\": \"Department Id\",\n      \"positionUser\": \"Position User\",\n      \"cityId\": \"City Id\",\n      \"date\": \"Date\",\n      \"organizationLevel\": \"Organization Level\",\n      \"organizationBusinessType\": \"Business Type\",\n      \"captcha\": \"Captcha\",\n      \"label\": \"Dimension\",\n      \"domainName\": \"Domain Name\",\n      \"domainCode\": \"Domain Code\",\n      \"domain\": \"Domain\",\n      \"domainLeve\": \"Domain Level\",\n      \"domainLevelCode\": \"Levle Code\",\n      \"domainLevelName\": \"Levle Name\",\n      \"domainLevelCList\": \"Level List\",\n      \"domainLevelCName\": \"Grade level\",\n      \"standDomainLevelCode\": \"Standard Levle Code\",\n      \"standDomainLevelName\": \"Standard Level Name\",\n      \"standDomainLevelWeight\": \"Standard Level Weight\",\n      \"isPcShow\": \"Is PC Display\",\n      \"isMobileShow\": \"Is APP Display\",\n      \"levelMaintenance\": \"Level Maintenance\",\n      \"items\": \"Items\",\n      \"itemsMaintenance\": \"Items Maintenance\",\n      \"isSysDefine\": \"IsSysDefine\",\n      \"isShrink\": \"Is In More\",\n      \"style\": \"Style\",\n      \"enabled\": \"Enabled\",\n      \"category\": \"Category\",\n      \"function\": \"Function\",\n      \"hasUsed\": \"The data has been used\",\n      \"businessSystem\": \"Business System\",\n      \"isCanView\": \"Is can view\",\n      \"createDate\": \"Create Date\",\n      \"createUser\": \"Create User\",\n      \"modifyDate\": \"Modify Date\",\n      \"modifyUser\": \"Modify User\",\n      \"isStandardRole\": \"is Standard Role\",\n      \"commonFile\": \"Common File\",\n      \"application\": \"Application\"\n    },\n    \"application\": {\n      \"pageName\": \"Application Settings\",\n      \"statusDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"InValid\",\n          \"value\": \"0\"\n        }\n      ],\n      \"existedCode\": \"Application is existed!\"\n    },\n    \"externalSystems\": {\n      \"domain\": \"Domain\",\n      \"secretKey\": \"Secret Key\",\n      \"appKey\": \"App Key\",\n      \"dataValidationUrl\": \"Data Verification Url\",\n      \"canStartInvalid\": \"Can Start or Invalid\",\n      \"settingOfCreate\": \"Create Callback\",\n      \"settingOfAudit\": \"Audit Callback\",\n      \"settingOfApprove\": \"Approve Callback\",\n      \"apiTodoTaskBatchApprovalCallbackUrl\": \"Todo Task BatchApproval CallbackUrl\",\n      \"apiTodoTaskBatchTransferCallbackUrl\": \"Todo Task BatchTransfer CallbackUrl\",\n      \"apiTodoMyStartUrgeCallbackUrl\": \"Todo MyStart Urge CallbackUrl\",\n      \"apiTodoMyStartWithdrawCallbackUrl\": \"Todo MyStart Withdraw CallbackUrl\",\n      \"apiTodoArchivedTaskWithdrawCallbackUrl\": \"Todo ArchivedTask Withdraw CallbackUrl\",\n      \"apiTodoDraftDeleteCallbackUrl\": \"Todo Draft Delete CallbackUrl\",\n      \"settingOfTaskCreate\": \"Task Create Callback\",\n      \"systemTypeCodes\": \"system Type\",\n      \"apiTodo\":\"api Todo\",\n      \"interface\": \"Interface\",\n      \"params\": \"Callback Parameters\",\n      \"code\": \"System Code\",\n      \"name\": \"System Name\",\n      \"baseInfo\": \"Base Information\",\n      \"generate\": \"Generate Secretkey\",\n      \"preview\": \"Preview\",\n      \"detail\": \"Detail\",\n      \"registerInfo\": \"Register Info\",\n      \"apiRegister\": \"API Register\",\n      \"mqRegister\": \"MQ Register\",\n      \"serviceRegister\": \"WebService Register\",\n      \"server\": \"Server\",\n      \"port\": \"Port\",\n      \"publishMQ\": \"Publish MQ\",\n      \"receiveMQ\": \"Receive MQ\",\n      \"apiType\": [\n        {\n          \"label\": \"WebService\",\n          \"value\": \"webservice\"\n        },\n        {\n          \"label\": \"WebApi\",\n          \"value\": \"webapi\"\n        }\n      ],\n      \"requestMethod\": [\n        {\n          \"label\": \"GET\",\n          \"value\": \"get\"\n        },\n        {\n          \"label\": \"POST\",\n          \"value\": \"post\"\n        }\n      ],\n      \"statusDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"InValid\",\n          \"value\": \"0\"\n        }\n      ]\n    },\n    \"log\": {\n      \"projectName\": \"Project Name\",\n      \"module\": \"Module\",\n      \"largeModule\": \"LargeModule\",\n      \"smallModule\": \"SmallModule\",\n      \"logType\": \"LogType\",\n      \"firstFilterField\": \"First filter-field\",\n      \"secondFilterField\": \"Second filter-field\",\n      \"lately\": \"Lately\",\n      \"interval\": \"Interval\",\n      \"time\": \"Time\",\n      \"message\": \"Message\",\n      \"detail\": \"Message Info\",\n      \"operator\": \"Operator\",\n      \"serverName\": \"Server Name\",\n      \"interfaceName\": \"Interface Name\",\n      \"responseData\": \"Response Data\",\n      \"interfaceTime\": \"Interface Time\",\n      \"info\": \"Message Info\",\n      \"interfaceAddress\": \"Interface Address\",\n      \"requestParam\": \"Request Parameters\",\n      \"requestBody\": \"Request Data\",\n      \"errorMessage\": \"Error Message\",\n      \"filter\": \"Filter\"\n    },\n    \"monitor\": {\n      \"service\": \"Service\",\n      \"middleWare\": \"MiddleWare\",\n      \"html\": \"Html\",\n      \"address\": \"Probe Address\",\n      \"mailAddress\": \"Mail Address\",\n      \"templeteCode\": \"Templete Code\",\n      \"cron\": \"expression\",\n      \"failCount\": \"Fail Counts\",\n      \"type\": \"Probe Type\",\n      \"typeDateset\": [\n        {\n          \"label\": \"Service\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"MiddleWare\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Html\",\n          \"value\": 2\n        }\n      ],\n      \"cronDateset\": [\n        {\n          \"label\": \"Every minute\",\n          \"value\": \"*/1 * * * *\"\n        },\n        {\n          \"label\": \"Every two minutes\",\n          \"value\": \"*/2 * * * *\"\n        },\n        {\n          \"label\": \"Every three minutes\",\n          \"value\": \"*/3 * * * *\"\n        }\n      ]\n    },\n    \"company\": {\n      \"list\": \"Comapny List\",\n      \"info\": \"Comapny Info\",\n      \"subsidiary\": \"Subsidiary\",\n      \"topCompanies\": \"Top-Companies\",\n      \"superiorCompany\": \"Superior-Company\",\n      \"address\": \"Address\",\n      \"contact\": \"Contact\",\n      \"fax\": \"Fax\",\n      \"domain\": \"Domain\",\n      \"typeDataset\": [\n        {\n          \"label\": \"Group\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Group headquarters\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Business Department\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"Division headquarters\",\n          \"value\": 4\n        },\n        {\n          \"label\": \"Project Company\",\n          \"value\": 5\n        }\n      ]\n    },\n    \"organization\": {\n      \"org\": \"Organization\",\n      \"list\": \"Organization List\",\n      \"info\": \"Organization Info\",\n      \"subdivision\": \"Sub Organization\",\n      \"topOrganization\": \"Top Organization\",\n      \"isPrimary\": \"IsPrimary\",\n      \"inUse\": \"InUse\",\n      \"user\": \"User\",\n      \"addCheck\": \"Please select organization! \",\n      \"selectedOrganization\": \"Selected Organization\",\n      \"organizationUser\": \"Organization Users\",\n      \"organizationCode\": \"Organization Code\",\n      \"organizationName\": \"Organization Name\",\n      \"organizationFullPathText\": \"Organization Full Name\",\n      \"organizationFullPathCode\": \"Organization Full Code\",      \n      \"positionCode\": \"Position Code\",\n      \"positionName\": \"Position Name\",\n      \"positionInfo\": \"Position Info\",\n      \"superiorOrganization\": \"Superior-Organization\",\n      \"businessType\": \"Business Type\",\n      \"businessTypeLevel\": \"Business Type Level\",\n      \"positionType\": \"Position Type\",\n      \"positionTypeDataset\": [\n        {\n          \"label\": \"Normal\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"LeaderInCharge\",\n          \"value\": 1\n        }\n      ]\n    },\n    \"position\": {\n      \"addCheck\": \"Please select Company or Department! \"\n    },\n    \"positionLevel\": {\n      \"shortName\": \"ShortName\",\n      \"setting\": \"PositionLevel Setting\",\n      \"list\": \"PositionLevel List\",\n      \"companyName\": \"Company Name\",\n      \"departmentName\": \"Department Name\",\n      \"user\": \"User\"\n    },\n    \"dictionary\": {\n      \"category\": \"Dictionary Category\",\n      \"list\": \"Dictionary List\",\n      \"edit\": \"Type Edit\",\n      \"topType\": \"Top-type\",\n      \"subtype\": \"Subtype\",\n      \"superiorParameter\": \"Superior-parameter\",\n      \"value\": \"Value\",\n      \"selectCheck\": \"Please select Type\",\n      \"deleteCheck\": \"Please delete subdata! \"\n    },\n    \"menu\": {\n      \"link\": \"Link\",\n      \"nameCheck\": \"Please save menu data! \",\n      \"action\": \"Actions\",\n      \"permission\": \"Permission\",\n      \"list\": \"Menu List\",\n      \"info\": \"Menu Info\",\n      \"icon\": \"Icon\",\n      \"application\": \"Application\",\n      \"route\": \"Route\",\n      \"isMobile\": \"Is APP\",\n      \"hidden\": \"Menu Hidden\",\n      \"path\": \"Path\",\n      \"codePath\": \"Code Path\",\n      \"namePath\": \"Name Path\",\n      \"subModule\": \"Submodule\",\n      \"topModule\": \"Top Module\",\n      \"typeDataset\": [\n        {\n          \"label\": \"Module\",\n          \"value\": \"M\"\n        },\n        {\n          \"label\": \"Page\",\n          \"value\": \"P\"\n        }\n      ]\n    },\n    \"role\": {\n      \"keyword\": \"Name(Code)\",\n      \"permission\": \"Menu Permission\",\n      \"show\": \"Show\",\n      \"action\": \"Actions\",\n      \"orgLevel\": \"Organization Level\",\n      \"businessType\": \"Business Type\",\n      \"tagType\": \"Tag Type\",\n      \"tag\": \"Tag\",\n      \"inUse\": \"Status\",\n      \"user\": \"User\",\n      \"editTag\": \"Edit Tags/BusinessType\",\n      \"includeChildren\": \"Include Children\",\n      \"dataRights\": \"Edit data rights\",\n      \"organizationLevelLabel\": \"Organization Level\",\n      \"organizationGroupLabel\": \"Organization Group\",\n      \"personal\": \"Personal\",\n      \"custom\": \"Custom\",\n      \"organization\": \"Organization\",\n      \"exists\": \"Exists\",\n      \"dataRightsType\": [\n        {\n          \"label\": \"Organization Level\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"Organization Group\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Myself\",\n          \"value\": 2\n        }\n      ],\n      \"inUseDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Invalid\",\n          \"value\": 0\n        }\n      ],\n      \"memberDataset\": [\n        {\n          \"label\": \"User\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Company\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Department\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"Position\",\n          \"value\": 4\n        }\n      ]\n    },\n    \"user\": {\n      \"company\": \"Company\",\n      \"department\": \"Department\",\n      \"position\": \"Position\",\n      \"role\": \"Role\",\n      \"addTime\": \"AddTime\",\n      \"name\": \"Name\",\n      \"agentEndTime\": \"EndTime\",\n      \"agenter\": \"Agenter\",\n      \"dynamicSetting\": \"Dynamic Setting\",\n      \"currentUser\": \"CurrentUser\",\n      \"phone\": \"Phone\",\n      \"baseInfo\": \"Base Information\",\n      \"extendInfo\": \"Extend Information\",\n      \"reportRelation\": \"Report Relation\",\n      \"orgRelation\": \"Organization Relation\",\n      \"roleRelation\": \"Role Relation\",\n      \"isPrimaryPosition\": \"IsPrimaryPosition\",\n      \"lock\": \"Lock\",\n      \"gender\": \"Gender\",\n      \"man\": \"Male\",\n      \"girl\": \"Female\",\n      \"birthday\": \"Birthday\",\n      \"email\": \"Email\",\n      \"lessOneOragization\": \"Choose less one organization! \",\n      \"lessOneRole\": \"Choose less one role!\",\n      \"workNumber\": \"Work Number\",\n      \"upperUser\": \"Immediate Superior\",\n      \"jobGrade\": \"Job Grade\",\n      \"education\": \"Education\",\n      \"joinDate\": \"Join Date\",\n      \"leaderName\": \"Leader Name\",\n      \"businessLineName\": \"Business Line\",\n      \"lineLeader\": \"Line Leader\",\n      \"genderDataset\": [\n        {\n          \"label\": \"Male\",\n          \"value\": \"M\"\n        },\n        {\n          \"label\": \"Female\",\n          \"value\": \"F\"\n        }\n      ],\n      \"lockDataset\": [\n        {\n          \"label\": \"None\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"Temporary\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"LongTime\",\n          \"value\": 2\n        }\n      ],\n      \"statusDataset\": [\n        {\n          \"label\": \"None\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"Valid\",\n          \"value\": 1\n        }\n      ],\n      \"workingStatusDataset\": [\n        {\n          \"label\": \"Working\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Leaving\",\n          \"value\": 0\n        }\n      ],\n      \"workingState\": \"WorkingState\",\n      \"dataSecurity\": \"DataSecurity\",\n      \"loginCheck\": \"The user account password is incorrect!\",\n      \"captchaCheck\": \"Captcha not exist or error!\",\n      \"impersonateCheck\": \"Impersonate login failed!\",\n      \"lessOnePrimaryPosition\": \"At least one primary position is required\",\n      \"notFound\": \"User does not exist!\",\n      \"existAccount\": \"The account or work number has been used！\",\n      \"existReportRelation\": \"The same reporting relationship exists！\",\n      \"leave\": \"Leave\"\n    },\n    \"message\": {\n      \"message\": \"Message\",\n      \"keyValue\": \"Bussiness Key\",\n      \"msgType\": \"Message Type\",\n      \"category\": \"Message Category\",\n      \"fromSys\": \"From System\",\n      \"msgTitle\": \"Title\",\n      \"msgBody\": \"Content\",\n      \"sendTo\": \"Send To\",\n      \"priority\": \"Priority\",\n      \"sendTime\": \"Send Time\",\n      \"sendingStatus\": \"Send Status\",\n      \"tryTimes\": \"Try Times\",\n      \"messageTemplate\": \"Message Template\",\n      \"templateCode\": \"Code\",\n      \"templateName\": \"Name\",\n      \"templateContent\": \"Content\",\n      \"templateType\": \"Type\",\n      \"templateSubject\": \"Subject\",\n      \"sendStatus\": [\n        {\n          \"label\": \"Sending\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"Success\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Failed\",\n          \"value\": -1\n        }\n      ],\n      \"messageTypeItems\": [\n        {\n          \"label\": \"Short Message\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Email\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Wechat\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"Dingtalk\",\n          \"value\": 4\n        }\n      ],\n      \"dataStatus\": [\n        {\n          \"label\": \"Disable\",\n          \"value\": 0\n        },\n        {\n          \"label\": \"Enable\",\n          \"value\": 1\n        }\n      ],\n      \"templateTypeItems\": [\n        {\n          \"label\": \"Short Message\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Email\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Wechat\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"Dingtalk\",\n          \"value\": 4\n        }\n      ]\n    },\n    \"init\": {\n      \"getglobalbtn\": \"Get global setting\",\n      \"global\": \"Global\",\n      \"customer\": \"Customer\",\n      \"mobile\": \"Mobile\",\n      \"admin\": \"Admin\",\n      \"name\": \"System Name\",\n      \"logo\": \"Logo\",\n      \"logoThumbnail\": \"Logo Thumbnail\",\n      \"icon\": \"Icon\",\n      \"enableWatermark\": \"Enable Watermark\",\n      \"watermark\": \"Watermark\",\n      \"watermarkType\": \"Watermark Type\",\n      \"watermarkTypeDataset\": [\n        {\n          \"label\": \"User Name + Account + Data\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"Customize\",\n          \"value\": \"99\"\n        }\n      ],\n      \"theme\": \"Theme\",\n      \"copyright\": \"Copyright\",\n      \"help\": \"Help\",\n      \"trafficLightUnit\": \"TrafficLight Unit\",\n      \"trafficLightUnitDataset\": [\n        {\n          \"label\": \"hour\",\n          \"value\": \"hour\"\n        },\n        {\n          \"label\": \"day\",\n          \"value\": \"day\"\n        }\n      ],\n      \"initProcessEnableOrgFilter\": \"Initiate process enable organization filtering\",\n      \"helpText\": {\n        \"name\": \"Title of the page\",\n        \"logo\": {\n          \"customer\": \"The logo of the page. It is recommended to upload PNG pictures below 200 * 48 in width and height\",\n          \"mobile\": \"The logo of the page. It is recommended to upload PNG pictures below 200 * 48 in width and height\",\n          \"admin\": \"Requirement: upload 128px * 30px jpeg, png or svg file\"\n        },\n        \"logoThumbnail\": {\n          \"admin\": \"Requirement: upload 30px * 30px jpeg, png or svg file\"\n        },\n        \"icon\": \"Requirement: upload 32px * 32px or 64px * 64px icon file\",\n        \"theme\": \"Theme color of the page\",\n        \"copyright\": \"Does the page display copyright information\",\n        \"help\": \"Help text for the page\",\n        \"trafficLightUnit\": \"Traffic light unit of the system\",\n        \"initProcessEnableOrgFilter\": \"Whether to enable organization filtering for initiating process\",\n        \"canGetGloablSetting\": \"You can get the following information from the global configuration:\"\n      },\n      \"message\": {\n        \"onlyJpg\": \"Only can upload jpeg、png or svg file\",\n        \"imageSize\": \"File size must less then 150KB\",\n        \"onlyIcon\": \"Only can upload icon file\"\n      }\n    },\n    \"gateway\": {\n      \"name\": \"name\",\n      \"address\": \"address\",\n      \"operation\": \"operation\"\n    },\n    \"dataPermission\": {\n      \"permission\": \"Data Permission\",\n      \"descripton\": \"Descripton\",\n      \"dataRequied\": \"Conditional configuration is incomplete\",\n      \"filterCondition\": \"Filter Condition\",\n      \"conditionLogic\": \"Condition Logic\",\n      \"addCondition\": \"Add Condition\",\n      \"statusDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Invalid\",\n          \"value\": 0\n        }\n      ],\n      \"operators\": [\n        {\n          \"label\": \"Equal\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Be Contained In\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Start With\",\n          \"value\": 3\n        }\n      ],\n      \"conditionType\": [\n        {\n          \"label\": \"Fixed value\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"System variables\",\n          \"value\": 2\n        }\n      ],\n      \"systemVariables\": [\n        {\n          \"label\": \"Current user\",\n          \"value\": \"currentUser\"\n        },\n        {\n          \"label\": \"Current department\",\n          \"value\": \"currentDepart\"\n        }\n      ],\n      \"existedCode\": \"The code is existed！\"\n    },\n    \"apis\": {\n      \"moduleCode\": \"Module Code\",\n      \"path\": \"Path\",\n      \"summary\": \"Summary\",\n      \"operationId\": \"Action\",\n      \"permission\": \"Data Permission\",\n      \"impostApi\": \"Import API\",\n      \"apiJson\": \"Api Data\",\n      \"dataError\": \"Data Format Error\",\n      \"importFaild\": \"Import Failure\",\n      \"tableRelation\": \"Relation Tables\",\n      \"moduleCodeDataset\": [\n        {\n          \"label\": \"Platform\",\n          \"value\": \"platform\"\n        },\n        {\n          \"label\": \"Process\",\n          \"value\": \"process\"\n        }\n      ],\n      \"notNull\": \"Import data is null！\",\n      \"nullModuleCode\": \"Module code is null！\"\n    },\n    \"dataPermissionTable\": {\n      \"table\": \"Table\",\n      \"tableName\": \"Table Name\",\n      \"descCn\": \"Chinese Description\",\n      \"descEn\": \"English Description\",\n      \"column\": \"Column Information\",\n      \"columnName\": \"Column Name\",\n      \"columnType\": \"Column Type\",\n      \"addColumn\": \"Add Column\",\n      \"columnTypes\": [\n        {\n          \"label\": \"String\",\n          \"value\": \"string\"\n        },\n        {\n          \"label\": \"Number\",\n          \"value\": \"number\"\n        },\n        {\n          \"label\": \"GUID\",\n          \"value\": \"uniqueidentifier\"\n        }\n      ],\n      \"existedName\": \"Table name is existed！\"\n    },\n    \"tableRelations\": {\n      \"tableRelation\": \"Table Relation\",\n      \"tableName\": \"Table Name\",\n      \"columnName\": \"Column Name\",\n      \"targetTableName\": \"Target Table Name\",\n      \"targetColumnName\": \"Target Column Name\",\n      \"statusDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Invalid\",\n          \"value\": 0\n        }\n      ],\n      \"existedRelation\": \"Table map relation is existed！\"\n    },\n    \"product\": {\n      \"registration\": {\n        \"registe\": \"Registered\",\n        \"productKey\": \"Product Key\",\n        \"licenseFile\": \"License File\",\n        \"productInfo\": \"Product Information\",\n        \"productId\": \"Product Number\",\n        \"productName\": \"Product Name\",\n        \"productVersion\": \"Product Version\",\n        \"customerName\": \"Customer Name\",\n        \"licenseType\": \"License Type\",\n        \"expirationDate\": \"Expiration Date\",\n        \"licenseStatus\": \"License Status\",\n        \"manufacturer\": \"Manufacturer\",\n        \"active\": \"Active\",\n        \"expiration\": \"Expired\",\n        \"trial\": \"Trial\",\n        \"commercial\": \"Commercial\"\n      }\n    },\n    \"sso\": {\n      \"unauthorized\": \"Unauthorized!\"\n    },\n    \"commonRole\": {\n      \"keyword\": \"Name(Code)\",\n      \"permission\": \"Permission\",\n      \"show\": \"Show\",\n      \"action\": \"Actions\",\n      \"orgLevel\": \"Organization Level\",\n      \"businessType\": \"Business Type\",\n      \"tagType\": \"Tag Type\",\n      \"tag\": \"Tag\",\n      \"inUse\": \"Status\",\n      \"user\": \"User\",\n      \"valid\": \"Valid\",\n      \"invalid\": \"None\",\n      \"descripiton\": \"Descripiton\",\n      \"hasUsed\": \"Has been used\",\n      \"process\": \"process\",\n      \"postNameCode\": \"post Name/Code\",\n      \"btId\": \"BTId\",\n      \"info\": \"Organization Info\",\n      \"codeOrName\": \"Role Code or Role Name\",\n      \"Notfound\": \"Data not found!\",\n      \"Import\": \"Import\",\n      \"OrgDomainError\": \"The organization level is abnormal\",\n      \"OrgError\": \"The organization to which the role belongs is abnormal\",\n      \"CommonRoleNotInThisLevlel\": \"The role does not belong to the current Domain or DomainLevel\",\n      \"CommonRoleNameNotNull\": \"When role Code exists, the role name cannot be empty\",\n      \"CommonRoleCodeNotNull\": \"When role Name exists, the role Code cannot be empty\",\n      \"CommonRoleNameCodeNotMatching\": \"Role Code and Name do not match\",\n      \"RoleUserNameNotNull\": \"When RoleUser Code exists, the RoleUser name cannot be empty\",\n      \"RoleUserCodeNotNull\": \"When RoleUser name exists, the RoleUser Code cannot be empty\",\n      \"RoleUserCodeNameNotMatching\": \"RoleUser Code and Name do not match\",\n      \"RoleUserNameNotInvalid\": \"The staff is invalid or resigned\",\n      \"NoRoleImport\": \"You do not have permission to import the organization\'s data\",\n      \"UserNotFound\": \"User does not exist\",\n      \"ComonRoleDelayEffect\":\"Note: CommonRole changes will take effect 3 minutes later!\",\n      \"inUseDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": \"true\"\n        },\n        {\n          \"label\": \"None\",\n          \"value\": \"false\"\n        }\n      ],\n      \"memberDataset\": [\n        {\n          \"label\": \"User\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Company\",\n          \"value\": 2\n        },\n        {\n          \"label\": \"Department\",\n          \"value\": 3\n        },\n        {\n          \"label\": \"Position\",\n          \"value\": 4\n        }\n      ],\n      \"batchExport\": \"Batch Export\",\n      \"batchImport\": \"Batch Import\"\n    },\n    \"matrix\": {\n      \"source\": \"Source\",\n      \"matrix\": \"Matrix\",\n      \"dimension\": \"Dimension\",\n      \"dictionary\": \"Please input dictionary typecode\",\n      \"config\": \"Configuration\",\n      \"preview\": \"Generate Matrix\",\n      \"sync\": \"Sync View Dimension\",\n      \"sync-dictionary\": \"Sync Dictionary Dimension\",\n      \"inUse\": \"Status\",\n      \"inUseDataset\": [\n        {\n          \"label\": \"Valid\",\n          \"value\": \"true\"\n        },\n        {\n          \"label\": \"None\",\n          \"value\": \"false\"\n        }\n      ],\n      \"role\": \"Roles\",\n      \"status\": \"Status\",\n      \"statusDataset\": [\n        {\n          \"label\": \"Draft\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"Invalid\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"Valid\",\n          \"value\": \"1\"\n        }\n      ]\n    },\n    \"tag\": {\n      \"keyword\": \"名称或编码\",\n      \"category\": \"标签分类\"\n    },\n    \"process\": {\n      \"no\": \"NO.\",\n      \"processNo\": \"Process No.\",\n      \"title\": \"Title\",\n      \"applicant\": \"Applicant\",\n      \"creatDate\": \"Application Time\",\n      \"status\": \"Status\",\n      \"residenceTime\": \"Residence Time\",\n      \"formEdit\": \"Form Edit\",\n      \"nodeEdit\": \"Node Edit\",\n      \"step\": \"Step Name\",\n      \"approver\": \"Approver\",\n      \"typeName\": \"Type\",\n      \"dockingStatus\": \"Docking Status\",\n      \"dockingMessage\": \"Docking Message\",\n      \"dockingDate\": \"Docking Date\",\n      \"lastDockingDate\": \"Last Docking Date\",\n      \"dockingCount\": \"Docking Count\",\n      \"canNotNullify\": \"Instance：{{value}} cannot be nullify\",\n      \"selectProcess\": \"Please select process\",\n      \"nullify\": \"Nullify\",\n      \"batchNullify\": \"Batch Nullify\",\n      \"nullifyComment\": \"Nullify Comment\",\n      \"del\": \"删除\",\n      \"batchDel\": \"批量删除\",\n      \"batchDelComment\": \"删除原因\",\n      \"notice\": \"Notice\",\n      \"batchNotice\": \"Batch notice\",\n      \"authorizationTable\": \"AuthorizationTable\",\n      \"processIntervention\": \"Process intervention\",\n      \"formDataIntervention\": \"FormData intervention\",\n      \"approveHistoryIntervention\": \"Approve History Intervention\",\n      \"formData\": \"FormData\",\n      \"user\": \"User\",\n      \"stepUser\": \"Step User\",\n      \"interventionHistory\": \"Intervention History\",\n      \"processInfo\": \"Process Info\",\n      \"dataNotBeNull\": \"Data cannot be null\",\n      \"jsonFormatError\": \"Abnormal Json format\",\n      \"keyWordNotBeModfiy\": \"The keyword cannot be modified\",\n      \"keyWords\": \"Key Words\",\n      \"keywordsWarning\": \"(Do not adjust key Words of the form. If you need to adjust key Words, return to the process！)\",\n      \"attachmentMaintenance\": \"Attachment Maintenance\"\n    },\n    \"jobManage\": {\n      \"jobCatetory\": \"Job Category\",\n      \"jobName\": \"Job Name\",\n      \"keyWords\": \"Key Words\",\n      \"createTime\": \"Create Time\",\n      \"finishTime\": \"Finish Time\",\n      \"jobData\": \"Job Data\",\n      \"jobResult\": \"Excute Result\",\n      \"jobDetail\": \"Job Detail\",\n      \"jobStatus\": [\n        {\n          \"label\": \"Enqueued\",\n          \"value\": \"Enqueued\"\n        },\n        {\n          \"label\": \"Succeeded\",\n          \"value\": \"Succeeded\"\n        },\n        {\n          \"label\": \"Failed\",\n          \"value\": \"Failed\"\n        }\n      ]\n    },\n    \"cacheManage\": {\n      \"cacheCode\": \"Cache Key\",\n      \"desc\": \"Description\",\n      \"createTime\": \"Last Refresh Time\",\n      \"initSystemCache\": \"Init System Cache\"\n    },\n    \"internalApi\": {\n      \"apiInfo\": \"API Information\",\n      \"apiCategory\": \"API Category\",\n      \"system\": \"System\",\n      \"category\": \"Category\",\n      \"systemCode\": \"System Code\",\n      \"systemName\": \"System Name\",\n      \"requestMethod\": \"Request Method\",\n      \"requestPath\": \"Request Path\",\n      \"summary\": \"Summary\",\n      \"export\": \"Export\",\n      \"requestParams\": \"Request Params\",\n      \"requestBodySchema\": \"Request Body Schema\",\n      \"responseSchema\": \"Response Schema\",\n      \"paramName\": \"Param Name\",\n      \"paramType\": \"Param Type\",\n      \"dataType\": \"Data Type\",\n      \"paramDesc\": \"Description\",\n      \"required\": \"Required\",\n      \"requestBodyType\": \"Request Body Type\",\n      \"requestBody\": \"Request Body\",\n      \"responseBodyType\": \"Response Body Type\",\n      \"responseBody\": \"Response Body\",\n      \"categoryName\": \"Category Name\",\n      \"exportDataSource\": [  \n        {\n          \"label\": \"Yes\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"No\",\n          \"value\": 0\n        }\n      ],\n      \"messages\":{\n          \"lessOneApi\": \"Chose less one API! \",\n          \"choseSystem\": \"Please chose a system first!\"\n      }        \n    },\n    \"holiday\":{\n      \"maintain\": \"Maintain\",\n      \"date\": \"Date\",\n      \"type\": \"Type\",\n      \"remark\": \"Remark\",\n      \"name\":\"holiday\",\n      \"weekday\": \"weekday\",\n      \"statusDataset\": [\n        {\n          \"label\": \"Holiday\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Weekday\",\n          \"value\": 0\n        }\n      ]\n    }    \n  },\n  \"bpm\": {\n    \"user\": {\n      \"phone-number\": \"Phone\",\n      \"department-name\": \"Department\",\n      \"email\": \"Email\",\n      \"position-level\": \"Position Level\",\n      \"organization\": \"Organization\"\n    },\n    \"bpmn\": {\n      \"start\": \"Start\",\n      \"end\": \"End\",\n      \"save\": \"Save\",\n      \"defaultStep\": \"Step\",\n      \"normal\": \"Normal\",\n      \"sub_process\": \"Sub Process\",\n      \"automator\": \"Automator\",\n      \"line\": \"Line\",\n      \"autoAlignment\": \"Auto Alignment\",\n      \"fullScreen\": \"Full Screen\",\n      \"quitFullScreen\": \"Quit Full Screen\",\n      \"delete\": \"Delete\",\n      \"confirm\": \"Confirm\",\n      \"cancel\": \"Cancel\",\n      \"duplicateStart\": \"Duplicate Start Node!\",\n      \"duplicateEnd\": \"Duplicate End Node!\",\n      \"noDeleteTarget\": \"Please select an element!\",\n      \"deleteConfirm\": \"Are you sure to delete?\",\n      \"cannotTargetStart\": \"Line can not target Start Node!\",\n      \"cannotSourceEnd\": \"Line can not source from End Node!\",\n      \"resetZoom\": \"Fit viewport\",\n      \"realignment\": \"Realignment\",\n      \"locate\": \"Locate\",\n      \"zoom\": \"Zoom\",\n      \"zoomIn\": \"Zoom in\",\n      \"zoomOut\": \"Zoom out\",\n      \"align\": \"Align\",\n      \"alignCenter\": \"Align center\",\n      \"alignLeft\": \"Align left\",\n      \"alignRight\": \"Align right\",\n      \"alignMiddle\": \"Align middle\",\n      \"alignTop\": \"Align top\",\n      \"alignBottom\": \"Align bottom\",\n      \"distributeHorizontal\": \"Distribute horizontal\",\n      \"distributeVertical\": \"Distribute vertical\",\n      \"undo\": \"Undo\",\n      \"redo\": \"Redo\",\n      \"userTask\": \"User task\",\n      \"copy\": \"Copy to clipboard\",\n      \"paste\": \"Paste from clipboard\",\n      \"processDefaultValue\": \"Process Default Value\",\n      \"helper\": {\n        \"title\": \"Operation helper\",\n        \"selectAll\": \"Select all\",\n        \"editSelection\": \"Edit Selection\",\n        \"dragSelect\": \"Drag select\",\n        \"lasso\": \"Lasso tool\",\n        \"space\": \"Space tool\"\n      },\n      \"messages\": {\n        \"copySuccess\": \"Copy Success\",\n        \"pasteSuccess\": \"Paste success\",\n        \"noStart\": \"No Start nnode\",\n        \"pasteSecureTips\": \"Use <Ctrl + V> to paste content\"\n      }\n    },\n    \"fields\": {\n      \"domain\": \"Domain\",\n      \"branch\": \"Branch\",\n      \"role\": \"Role\",\n      \"businessObject\": \"Business Object\",\n      \"version\": \"Version\",\n      \"formCode\": \"Form Code\",\n      \"formName\": \"Form Name\",\n      \"moduleInfo\": \"Module Info\",\n      \"code\": \"Code\",\n      \"name\": \"Name\",\n      \"authorizedUserId\": \"Authorized User\",\n      \"startDate\": \"Start Date\",\n      \"endDate\": \"End Date\",\n      \"inPar\": \"Request Parameter\",\n      \"outPar\": \"Returns the parameter\",\n      \"dataId\": \"The input parameters\",\n      \"dataType\": \"The parameter types\",\n      \"currentUser\": \"CurrentUser\"\n    },\n    \"routes\": {\n      \"processCategory\": \"Process Category\",\n      \"business-type\": \"Business Type\",\n      \"processDefaultSetting\": \"Process Default Setting\",\n      \"process\": \"Process\",\n      \"businessObject\": \"Business Object\",\n      \"designProcess\": \"Process Design\",\n      \"dataAuthority\": \"Data Permission\"\n    },\n    \"category\": {\n      \"list\": \"Process Category List\",\n      \"categoryName\": {\n        \"label\": \"Category Name\",\n        \"validation\": \"Please enter Category Name\"\n      },\n      \"inUse\": \"Disabled\",\n      \"organizationName\": \"Organization Name\",\n      \"parentIdName\": \"Primary Classification\",\n      \"editCategory\": \"Edit Category\",\n      \"addPrimaryCategory\": \"Add Primary Classification\",\n      \"addSecondaryCategory\": \"Add Secondary Classification\"\n    },\n    \"imitateStart\": {\n      \"BSID\": \"BSID\",\n      \"BTID\": \"BTID\",\n      \"BOID\": \"BOID\",\n      \"startUrl\": \"Start Url\",\n      \"start\": \"Start\"\n    },\n    \"processEvent\": {\n      \"serviceUrl\": \"Service Url\",\n      \"interfaceName\": \"Interface Name\",\n      \"type\": \"Request Type\",\n      \"requestExample\": \"Request Example\",\n      \"returnExample\": \"Return Example\",\n      \"authList\": \"Auth List\"\n    },\n    \"processAuth\": {\n      \"type\": \"Auth Type\",\n      \"memberName\": \"Auth Target\",\n      \"types\": [\n        {\n          \"label\": \"Org Auth\",\n          \"value\": 1\n        },\n        {\n          \"label\": \"Role Auth\",\n          \"value\": 3\n        }\n      ]\n    },\n    \"ruleSet\": {\n      \"controlPoint\": \"Control Point\",\n      \"ruleCode\": \"Control Point Code\",\n      \"ruleName\": \"Control Point Name\",\n      \"ruleCount\": \"Control Point Count\",\n      \"displayCondition\": \"Display Condition\",\n      \"standard\": \"Standard\",\n      \"actualState\": \"Actual State\",\n      \"ruleDetailName\": \"Rule Name\",\n      \"condition\": \"Rule Condition\",\n      \"earlyWarning\": \"Early Warning\",\n      \"tipsInfo\": \"Tips Info\",\n      \"importance\": \"Importance\",\n      \"chooseBO\": \"Please Choose BusinessObject\",\n      \"editExpression\": \"Edit Expression\",\n      \"systemBuilt-in\": \"System Built-in\",\n      \"sysParam\": \"System Param\",\n      \"lightsType\": [\n        {\n          \"label\": \"Red\",\n          \"value\": \"red\"\n        },\n        {\n          \"label\": \"Yellow\",\n          \"value\": \"yellow\"\n        },\n        {\n          \"label\": \"Green\",\n          \"value\": \"green\"\n        }\n      ],\n      \"priorityType\": [\n        {\n          \"label\": \"Urgent\",\n          \"value\": \"urgent\"\n        },\n        {\n          \"label\": \"Important\",\n          \"value\": \"important\"\n        },\n        {\n          \"label\": \"General\",\n          \"value\": \"general\"\n        }\n      ]\n    },\n    \"process\": {\n      \"setting\": {\n        \"addNode\": \"Add Approval Node\",\n        \"currentNode\": \"Current node\",\n        \"newNode\": \"New Approval Node\",\n        \"approver\": \"Approver\",\n        \"showButton\": \"Show Button\",\n        \"newStep\": \"New Step\",\n        \"copyNode\": \"Copy Node\",\n        \"nodeName\": \"Node Name\",\n        \"interveneReason\": \"Intervene Reason\",\n        \"saveSuccess\": \"Save Success\",\n        \"pleaseintervene\": \"Please intervene before saving\",\n        \"intervenelimit\": \"You can only intervene once at a time\",\n        \"emptyapprover\": \"The approver is empty\",\n        \"editapprover\": \"Edit approver\",\n        \"up\": \"Up\",\n        \"down\": \"Down\",\n        \"jumpInfo\": \"Please click the save button and wait for a moment!\"\n      },\n      \"list\": \"Process List\",\n      \"import\": \"Process Import\",\n      \"versionList\": \"Version List\",\n      \"newProcess\": \"New Process\",\n      \"selectProcessMsg\": \"At least one process has to be chosen!\",\n      \"published\": \"Process published,Can not publish again!\",\n      \"batchPublishMaxNumber\": \"A maximum of 40 articles can be published in batch at a time!\",\n      \"publishing\": \"Publishing is in progress. Please refresh the page later or uncheck!\",\n      \"authorizing\": \"Authorizing is in progress. Please refresh the page later or uncheck!\",\n      \"selectOrganizationMsg\": \"At least one organization has to be chosen!\",\n      \"batchAuthorizeMaxNumber\": \"A maximum of 10 articles can be authorize in batch at a time!\",\n      \"flowChartExport\": \"Flow chart,external link,inside inherit does not support export, please uncheck!\",\n      \"start-system\": \"Start system\",\n      \"start-user\": \"Start user\",\n      \"ProcessStatus\": [\n        {\n          \"label\": \"Draft\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"Valid\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"Invalid\",\n          \"value\": \"1\"\n        }\n      ],\n      \"listTable\": {\n        \"tagName\": \"Tag Name\",\n        \"businessTypeName\": \"Business Type\",\n        \"name\": \"Name\",\n        \"version\": \"Version\",\n        \"statusName\": \"Status\",\n        \"action\": \"Action\",\n        \"publish\": \"Publish\",\n        \"prompts\": {\n          \"importSuccess\": \"Import success\"\n        }\n      },\n      \"listVersionTable\": {\n        \"number\": \"Version\",\n        \"addTime\": \"Create Time\",\n        \"userName\": \"Create User\",\n        \"introduction\": \"Notes\",\n        \"action\": \"Action\",\n        \"restore\": \"Create new version\",\n        \"restoreTip\": \"Create a new version based on this version\",\n        \"currentNumber\": \"Current Version\"\n      },\n      \"fieldRight\": {\n        \"title\": \"Field Right\",\n        \"fieldName\": \"Field Name\",\n        \"fieldCode\": \"Field Code\",\n        \"useFormRight\": \"Use Form Right\",\n        \"see\": \"Show\",\n        \"unSee\": \"Invisible\",\n        \"write\": \"Write\",\n        \"edit\": \"Edit\",\n        \"unEdit\": \"Unedit\",\n        \"required\": \"Required\",\n        \"unRequired\": \"Unrequired\",\n        \"print\": \"Print\",\n        \"hideTable\": \"Hide Table\"\n      },\n      \"nodeEvent\": {\n        \"apiName\": \"Api Name\",\n        \"apiPath\": \"Api Path\",\n        \"apiMethod\": \"Api Method\",\n        \"afterEvent\": \"After Event\",\n        \"beforeEvent\": \"Before Event\"\n      },\n      \"oneStop\": {\n        \"tabs\": {\n          \"title\": \"Process Manage\",\n          \"baseInfo\": {\n            \"title\": \"Base Info\",\n            \"name\": \"Name\",\n            \"nameValidation\": \"Please enter Name\",\n            \"namePlaceholder\": \"Please enter Name\",\n            \"shortName\": \"Short Name\",\n            \"shortNamePlaceholder\": \"Please enter Short Name\",\n            \"source\": \"Source System\",\n            \"sourceValidation\": \"Please select Source System\",\n            \"sourcePlaceholder\": \"Please select Source System\",\n            \"businessTypeId\": \"Business Type\",\n            \"businessTypeIdValidation\": \"Please select Business Type\",\n            \"businessTypeIdPlaceholder\": \"Please select Business Type\",\n            \"tagId\": \"Tag\",\n            \"tagIdValidation\": \"Please select Tag\",\n            \"tagIdPlaceholder\": \"Please select Tag\",\n            \"btIdValidation\": \"Please enter BTID\",\n            \"btIdPlaceholder\": \"Please enter BTID\",\n            \"inUse\": \"In Use\",\n            \"code\": \"Process Code\",\n            \"codePlaceholder\": \"Please enter Process Code\",\n            \"typePlaceholder\": \"Please Select Process Type\"\n          },\n          \"businessObject\": {\n            \"title\": \"Business Object\",\n            \"binding\": \"Binding\",\n            \"add\": \"New\",\n            \"save\": \"Save\",\n            \"cancel\": \"Cancel\",\n            \"businessType\": \"Business Type\",\n            \"businessTypeValidation\": \"Please select Business Type\",\n            \"chooseBusinessObject\": \"Select Business Object\",\n            \"chooseBusinessObjectValidation\": \"Please select Business Object\",\n            \"confirmTitle\": \"Prompt\",\n            \"confirmContent\": \"Business Object changed, forms will unbind.\",\n            \"view\": {\n              \"baseInfo\": \"Base Info\",\n              \"property\": \"Property\",\n              \"name\": \"Name\",\n              \"nameValidation\": \"Please enter Business Object Name\",\n              \"namePlaceholder\": \"Please enter Business Object Name\",\n              \"code\": \"Code\",\n              \"codeValidation\": \"Please enter Business Object Code\",\n              \"codePlaceholder\": \"Please enter Business Object Code (can\'t modify after publish)\",\n              \"businessTypeId\": \"Business Type\",\n              \"businessTypeIdValidation\": \"Please select Business Type\",\n              \"version\": \"Version\"\n            },\n            \"property\": {\n              \"name\": \"Name\",\n              \"code\": \"Code\",\n              \"type\": \"Type\",\n              \"default\": \"Default\",\n              \"action\": \"Action\",\n              \"addTips\": \"Please add data\"\n            }\n          },\n          \"formInfo\": {\n            \"title\": \"Form Info\",\n            \"businessObjectForms\": \"Business Object Forms\",\n            \"processConfiguredForms\": \"Process Configured Forms\",\n            \"nameAndCode\": \"Name/Code\",\n            \"version\": \"Version\",\n            \"state\": \"State\",\n            \"action\": \"Action\",\n            \"node\": \"Node\",\n            \"chooseForm\": \"Select PC form\",\n            \"chooseMobileForm\": \"Select Mobile form\",\n            \"formCode\": \"Form Code\",\n            \"formCodePlaceholder1\": \"Please enter Form Code\",\n            \"formCodePlaceholder2\": \"Auto import\",\n            \"formName\": \"Form Name\",\n            \"formNamePlaceholder\": \"Please enter Form Name\",\n            \"formModules\": \"Module Info\",\n            \"formModuleItems\": {\n              \"baseInfo\": \"Base Info\",\n              \"relations\": \"Related Processes\",\n              \"attachments\": \"Attachments\",\n              \"records\": \"Audit Records\",\n              \"uoloadFileError\": \"Upload file extension error!\",\n              \"importForm\": \"Import Form\",\n              \"importFormValidation\": \"Please import Form!\",\n              \"newDesignForm\": \"New Design Form\",\n              \"newDesignFormValidation\": \"Please enter both Code and Name!\"\n            },\n            \"showHistoryVersion\": \"Show History Version\",\n            \"sameStepDifferentStepTypes\":\"same Step Different StepType\",\n            \"sameStepTypeDifferentSteps\":\"same StepType Different Step\"\n          },\n          \"processBpmn\": {\n            \"title\": \"Process Diagram\",\n            \"fixed\": \"Fixed Process\",\n            \"code\": \"Business Code\",\n            \"diagram\": {\n              \"save\": \"Save diagram\",\n              \"upload\": \"Import Auth Excel\",\n              \"missData\": \"Please add data!\",\n              \"missNode\": \"Please add node!\",\n              \"missLine\": \"Please add line!\",\n              \"missStart\": \"Please add Start node!\",\n              \"missEnd\": \"Please add End node!\",\n              \"missNodeName\": \"Please enter node name!\",\n              \"perfectNodePolicy\": \"Please perfect node policy!\"\n            },\n            \"baseInfo\": {\n              \"title\": \"Base\",\n              \"name\": \"Node Name\",\n              \"nameValidation\": \"Please enter Node Name\",\n              \"namePlaceholder\": \"Please enter Node Name\",\n              \"formType\": \"Form Type\",\n              \"processForm\": \"Process Form\",\n              \"independForm\": \"Independence Form\",\n              \"formCode\": \"Form\",\n              \"formCodeValidation\": \"Please select a Form\",\n              \"formCodePlaceholder\": \"Please select a Form\",\n              \"actionIds\": \"Actions\",\n              \"actionIdsConsult\": \"ActionIds Consult\",\n              \"actionIdsConsultSingle\": \"ActionIds Single Consult\",\n              \"stepActionsAssignSingle\": \"ActionIds Single Assign\",\n              \"formEditable\": \"Form Editable\",\n              \"fixedNode\": \"Fixed Node\",\n              \"fixedResolver\": \"Fixed Resolver\"\n            },\n            \"policy\": {\n              \"title\": \"Policy\",\n              \"resolvePolicy\": \"Resolve Policy\",\n              \"ccPolicy\": \"CC Policy\",\n              \"resolverType\": \"Resolver Type\",\n              \"resolverTypeValidation\": \"Please select Resolve Type\",\n              \"resolverTypePlaceholder\": \"Please select Resolve Type\",\n              \"resolverUsers\": \"Resolver Users\",\n              \"resolverUsersValidation\": \"Please select Resolve Users\",\n              \"resolverUsersPlaceholder\": \"Please select Resolve Users\",\n              \"resolverPosition\": \"Resolver Position\",\n              \"resolverPositionValidation\": \"Please select Resolve Position\",\n              \"resolverPositionPlaceholder\": \"Please select Resolve Position\",\n              \"resolverCommonRole\": \"Common Role\",\n              \"resolverCommonRoleValidation\": \"Please select Common Role\",\n              \"resolverCommonRolePlaceholder\": \"Please select Common Role\",\n              \"resolverSpecialRole\": \"Special Role\",\n              \"resolverSpecialRoleValidation\": \"Please select Special Role\",\n              \"resolverSpecialRolePlaceholder\": \"Please select Special Role\",\n              \"resolverMatrixRole\": \"Matrix Role\",\n              \"resolverMatrixRoleValidation\": \"Please select Matrix Role\",\n              \"resolverMatrixRolePlaceholder\": \"Please select Matrix Role\",\n              \"resolverRoleType\": \"Role Limit\",\n              \"resolverOrganizationLevel\": \"Organization Level\",\n              \"resolverOrganizationLevelValidation\": \"Please select Organization Level\",\n              \"resolverOrganizationLevelPlaceholder\": \"Please select Organization Level\",\n              \"resolverOrganization\": \"Organization\",\n              \"resolverOrganizationValidation\": \"Please select Organization\",\n              \"resolverOrganizationPlaceholder\": \"Please select Organization\",\n              \"resolverBusinessVariableValue\": \"Variable\",\n              \"resolverBusinessVariableValueValidation\": \"Please enter Variable\",\n              \"resolverBusinessVariableValuePlaceholder\": \"Please enter Variable\",\n              \"resolverSystemVariableValue\": \"Variable\",\n              \"resolverSystemVariableValueValidation\": \"Please enter Variable\",\n              \"resolverSystemVariableValuePlaceholder\": \"Please enter Variable\",\n              \"participatingType\": \"Participating Type\",\n              \"participatingTypePlaceholder\": \"Please select Participating Type\",\n              \"participatingPercent\": \"Participating Percent\",\n              \"participatingPercentValidation\": \"Please enter Participating Percent\",\n              \"participatingPercentPlaceholder\": \"Please enter Participating Percent\",\n              \"participatingUsers\": \"Participating Veto User\",\n              \"participatingUsersValidation\": \"Please select Participating Veto User\",\n              \"participatingUsersPlaceholder\": \"Please select Participating Veto User\",\n              \"concurrentType\": \"Concurrent Type\",\n              \"concurrentTypePlaceholder\": \"Please select Concurrent Type\",\n              \"concurrentPercent\": \"Concurrent Percent\",\n              \"concurrentPercentValidation\": \"Please enter Concurrent Percent\",\n              \"concurrentPercentPlaceholder\": \"Please enter Concurrent Percent\",\n              \"sameApproverType\": \"Same Approver Type\",\n              \"sameApproverTypePlaceholder\": \"Please select Same Approver Type\",\n              \"emptyApproverType\": \"Empty Approver Type\",\n              \"emptyApproverTypePlaceholder\": \"Please select Empty Approver Type\",\n              \"emptyApproverUsers\": \"Empty Approver Agent\",\n              \"emptyApproverUsersValidation\": \"Please select Empty Approver Agent\",\n              \"emptyApproverUsersPlaceholder\": \"Please select Empty Approver Agent\",\n              \"ccResolverUsers\": \"CC Resolver Users\",\n              \"ccResolverUsersPlaceholder\": \"Please select CC Resolver Users\",\n              \"no\": \"No\"\n            },\n            \"line\": {\n              \"name\": \"Name\",\n              \"namePlaceholder\": \"Please enter Name\",\n              \"expression\": \"Expression\",\n              \"exoressionPlaceholder\": \"Please endter Expression\",\n              \"exoressionTitle\": \"Click to edit Expression\",\n              \"example\": \"Example\"\n            }\n          },\n          \"organizationAuth\": {\n            \"title\": \"Organization Auth\",\n            \"name\": \"Organization Name\",\n            \"code\": \"Organization Code\",\n            \"include\": \"Include children\",\n            \"selectValidation\": \"Please select Organization\"\n          },\n          \"roleAuth\": {\n            \"title\": \"Role Auth\",\n            \"name\": \"Role Name\",\n            \"selectValidation\": \"Please select role\",\n            \"authList\": \"Auth List\"\n          },\n          \"controlPoint\": {\n            \"title\": \"Control Point\"\n          },\n          \"extendInfo\": {\n            \"title\": \"Extend Info\",\n            \"entryCondition\": \"Entry Condition\",\n            \"entryConditionValidation\": \"Please enter entry condition\",\n            \"entryConditionPlaceholder\": \"Please enter entry condition\",\n            \"customProcessTopic\": \"Custom process topic\",\n            \"customProcessTopicValidation\": \"Please enter custom process topic\",\n            \"customProcessTopicPlaceholder\": \"Please enter custom process topic\",\n            \"customApprovalTopic\": \"Custom approval topic\",\n            \"customApprovalTopicValidation\": \"Please enter custom approval topic\",\n            \"customApprovalTopicPlaceholder\": \"Please enter custom approval topic\",\n            \"introduction\": \"Introduction\",\n            \"introductionValidation\": \"Please enter introduction\",\n            \"introductionPlaceholder\": \"Please enter introduction\",\n            \"autoApproveType\": \"Auto approve\",\n            \"autoApproveTypeValidation\": \"Please select auto approve type\",\n            \"autoApproveTypePlaceholder\": \"Please select auto approve type\",\n            \"sameApproverAfterAuto\": \"The following steps of the same examiner will be approved automatically\",\n            \"sameApproverPreAuto\": \"The previous steps of the same examiner are automatically approved\",\n            \"businessUrl\": \"Business PC Url\",\n            \"businessMobileUrl\": \"Business Mobile Url\",\n            \"isAllowMobileStart\": \"Is Allow Mobile Start\",\n            \"isAllowStructuredStorage\": \"Is Allow Structured Storage\",\n            \"isShouFaWen\": \"是否收发文流程\",\n            \"approverEmptyEdit\": \"Approver Empty Edit\",\n            \"virtualObjectDataWriteback\": \"Virtual Object Data Writeback\",\n            \"consultInheritActions\": \"Consult Inherit Actions\",\n            \"param1\": \"Process Type\",\n            \"parentProcess\": \"Parent Process\",\n            \"parentProcessPlaceholder\": \"Please select the parent process\",\n            \"isWatermark\": \"Whether to add watermark for batch download\",\n            \"isIntervenAfter\": \"Whether to intervene after\",\n            \"lC_IsContract\": \"Display contract or not\",\n            \"lC_ContractNo\": \"Contract No.\",\n            \"lC_ContractNoPlaceholder\": \"Please fill in the contract number\",\n            \"lC_IsContractTrue\": \"Contract approval required\",\n            \"lC_IsPaync\": \"NC payment or not\",\n            \"lC_IsPlaceOnFile\": \"Do you need to archive\",\n            \"lC_RiskIsDisplay\": \"Whether to display risk control\",\n            \"lC_RiskType\": \"Risk type\",\n            \"lC_RiskDescribe\": \"Risk describe\",\n            \"lC_DescribeContent\": \"Describe Content\"\n          },\n          \"fillNotes\": {\n            \"title\": \"Fill Notes\"\n          },\n          \"institutionalNotes\": {\n            \"title\": \"Institutional Notes\"\n          },\n          \"fileNotes\": {\n            \"title\": \"File Notes\"\n          },\n          \"onlineProcessTable\": {\n            \"title\": \"Online Process Table\"\n          },\n          \"externalLink\": {\n            \"title\": \"External Link\"\n          },\n          \"insideInherit\": {\n            \"title\": \"Inside Inherit\"\n          },\n          \"keyParam\": {\n            \"title\": \"Key Param\",\n            \"keyParameterType\": [\n              {\n                \"label\": \"Branch\",\n                \"value\": 1\n              },\n              {\n                \"label\": \"Node\",\n                \"value\": 2\n              },\n              {\n                \"label\": \"Entry Condition\",\n                \"value\": 3\n              },\n              {\n                \"label\": \"Custom Topic\",\n                \"value\": 4\n              },\n              {\n                \"label\": \"Control Point\",\n                \"value\": 5\n              }\n            ],\n            \"source\": [\n              {\n                \"label\": \"Business Object\",\n                \"value\": 1\n              },\n              {\n                \"label\": \"System Param\",\n                \"value\": 2\n              }\n            ]\n          }\n        },\n        \"prompts\": {\n          \"publishSuccess\": \"Publish success\",\n          \"saveSuccess\": \"Save success\"\n        },\n        \"processPublish\": {\n          \"empty\": \"Empty data\",\n          \"notes\": \"Explain: all data will publish after click \'OK\'.\"\n        }\n      },\n      \"messages\": {\n        \"chooseOrganization\": \"Please choose Organization\"\n      },\n      \"form\": {\n        \"number\": \"Number\",\n        \"text\": \"Text\",\n        \"table\": \"Table\"\n      },\n      \"business-type\": {\n        \"add\": \"Add Business Type\",\n        \"parent\": \"Parent Business Type\",\n        \"ExistedCode\": \"Business type code already exists！\",\n        \"CheckUsed\": \"The Business type is in use and cannot be deleted！\"\n      },\n      \"domain-level-module\": {\n        \"existedCode\": \"Domain level code already exists！\"\n      },\n      \"excel-process\": {\n        \"list\": {\n          \"no\": \"No\",\n          \"item\": \"Item\",\n          \"dept\": \"Department\",\n          \"creator\": \"Creator\",\n          \"type\": \"Type\",\n          \"name\": \"Process Code\",\n          \"introduction\": \"Introduction\",\n          \"branch\": \"Branch\",\n          \"branchDescription\": \"Branch Description\",\n          \"inuse\": \"In Use\",\n          \"code\": \"Code\",\n          \"entryCondition\": \"Entry Condition\",\n          \"method\": \"Method\",\n          \"processConfigCondition\": \"Process Config\"\n        },\n        \"operation\": {\n          \"save\": \"Save\",\n          \"preview\": \"Preview\",\n          \"previewDescription\": \"Data\",\n          \"done\": \"Complete\",\n          \"success\": \"Success\"\n        },\n        \"import\": {\n          \"upload\": \"Upload\",\n          \"description\": \"Upload excel process\",\n          \"notFound\": \"Please upload excel process\",\n          \"message\": \"Import message\",\n          \"success\": \"Import Success\",\n          \"messageParm\": \"Import Process：{{value}}\"\n        },\n        \"steps\": {\n          \"previous\": \"Previous\",\n          \"next\": \"Next\"\n        }\n      },\n      \"business-object\": {\n        \"business-type\": \"Business Type\",\n        \"version\": \"Version\",\n        \"statusDataset\": [\n          {\n            \"label\": \"All\",\n            \"value\": \"\"\n          },\n          {\n            \"label\": \"Published\",\n            \"value\": \"published\"\n          },\n          {\n            \"label\": \"Unpublished\",\n            \"value\": \"unpublished\"\n          },\n          {\n            \"label\": \"Changing\",\n            \"value\": \"changing\"\n          }\n        ],\n        \"baseInfo\": \"Base Information\",\n        \"attribute\": \"Attribute\",\n        \"defaultValue\": \"Default value\",\n        \"type\": \"Type\",\n        \"addData\": \"Please add data\",\n        \"detailTable\": \"Detail Table\",\n        \"attributeType\": [\n          {\n            \"label\": \"Text\",\n            \"value\": \"text\"\n          },\n          {\n            \"label\": \"Number\",\n            \"value\": \"number\"\n          },\n          {\n            \"label\": \"Table\",\n            \"value\": \"table\"\n          }\n        ],\n        \"pleaseSaveFieldFirst\": \"Please Save Field First\",\n        \"ExistedCode\": \"Business object code is existed！\",\n        \"HasUsed\": \"Has been used\"\n      },\n      \"notfound\": \"Process Data not found! \",\n      \"rollBackCheck\": \"The current process is under modification and cannot be restored！\",\n      \"startUserNotfount\": \"Start User not found! \",\n      \"bpmnUrlNotfount\": \"Process diagram url not found！\",\n      \"btidBotfound\": \"btid not null!\",\n      \"json\": {\n        \"FormatError\": \"Process json struct error！\",\n        \"NotNull\": \"Process design data is null! \"\n      },\n      \"branch\": {\n        \"NotNull\": \"The workflow branch data is empty！\"\n      },\n      \"step\": {\n        \"NotNull\": \"The workflow step data is empty！\",\n        \"ToDoState\": \"not reach\"\n      },\n      \"processVersion\": {\n        \"Notfound\": \"The process version data does not exist! \",\n        \"RollBackInfo\": \"copy version from {0}\"\n      },\n      \"authorization\": {\n        \"NullProcessId\": \"Process Data is null! \",\n        \"Notfound\": \"Authorization Data not found! \"\n      },\n      \"draft\": {\n        \"Notfound\": \"Draft data does not exist!\"\n      },\n      \"userAgent\": {\n        \"EmptyUserAgent\": \"There are empty User Agent!\",\n        \"EmptyUserId\": \"There are empty UserId!\"\n      },\n      \"dataAuthority\": {\n        \"ExistedName\": \"Data authority name is existed！\"\n      },\n      \"ugreTips\": \"It has been urged that day. Please do not repeat the urging!\"\n    },\n    \"messages\": {\n      \"chooseOrganization\": \"Please choose Organization\"\n    },\n    \"processDefaultSetting\": {\n      \"canCC\": \"Enable CC\",\n      \"autoComplete\": \"Auto Complete\",\n      \"postion\": \"Position\"\n    },\n    \"form\": {\n      \"number\": \"Number\",\n      \"text\": \"Text\",\n      \"table\": \"Table\",\n      \"list\": \"Form List\",\n      \"searchPlaceholder\": \"Search Model Name/Code\",\n      \"editTitle\": \"New Design Form\",\n      \"user\": \"User\",\n      \"module\": [\n        {\n          \"label\": \"BaseInfo\",\n          \"value\": \"baseInfo\"\n        },\n        {\n          \"label\": \"Relation\",\n          \"value\": \"relation\"\n        },\n        {\n          \"label\": \"Attachment\",\n          \"value\": \"attachment\"\n        },\n        {\n          \"label\": \"Record\",\n          \"value\": \"record\"\n        },\n        {\n          \"label\": \"ControlPoint\",\n          \"value\": \"controlPoint\"\n        },\n         {\n          \"label\": \"Document\",\n          \"value\": \"document\"\n        }\n      ]\n    },\n    \"business-type\": {\n      \"add\": \"Add Business Type\",\n      \"parent\": \"Parent Business Type\",\n      \"scope\":\"Scope\"\n    },\n    \"excel-process\": {\n      \"title\": {\n        \"addBranch\": \"Add Process Branch\",\n        \"addRole\": \"Add Process Role\",\n        \"baseInfo\": \"Base Info\",\n        \"editStep\": \"Edit Step\",\n        \"nodeConfig\": \"Node Config\",\n        \"property\": \"Process Property\",\n        \"roleConfig\": \"Role Config\",\n        \"relevantCountersign\": \"Relevant countersignature\"\n      },\n      \"field\": {\n        \"branchName\": \"Branch Name\",\n        \"condition\": \"Condition\",\n        \"roleType\": \"Processing Type\",\n        \"domain\": \"Domain\",\n        \"roleLimitType\": \"Limit Type\",\n        \"notLimit\": \"Not Limit\",\n        \"organizationLevelLabel\": \"Organization Level\",\n        \"organizationGroupLabel\": \"Organization Group\",\n        \"standardRole\": \"Standard Role\",\n        \"stepIndex\": \"Step Index\",\n        \"stepType\": \"Step Type\",\n        \"stepName\": \"Step Name\",\n        \"resolver\": \"Resolver\",\n        \"directUser\": \"Direct User\",\n        \"matrixRole\": \"Matrix Role\",\n        \"position\": \"Position\",\n        \"specificRole\": \"Special Role\",\n        \"systemVariable\": \"System Variable\",\n        \"businessVariable\": \"Business Variable\",\n        \"enableEditForm\": \"Enable Edit Form\",\n        \"fixedStep\": \"Fixed Step\",\n        \"fixedApprover\": \"Fixed Approver\",\n        \"signStrategy\": \"Sign Strategy\",\n        \"signPercent\": \"Sign Percent\",\n        \"parallelStrategy\": \"Parallel Strategy\",\n        \"parallelPercent\": \"Parallel Percent\",\n        \"sameStartApproverStrategy\": \"Same Start Approver Strategy\",\n        \"sameApproverStrategy\": \"Same Approver Strategy\",\n        \"noneApproverStrategy\": \"None Approver Strategy\",\n        \"approvalButtons\": \"Approval Buttons\",\n        \"satisfactionCondition\": \"Satisfaction Condition\",\n        \"limitApprover\": \"Limit Approver\",\n        \"batchApproval\": \"Batch Approval\",\n        \"organizationVariable\": \"Organization Variable\",\n        \"fixedProcess\": \"Fixed Process\",\n        \"freeProcess\": \"Free Process\",\n        \"fixedProcessUsers\": \"Fixed User\",\n        \"startUser\": \"Start User\",\n        \"startUserSuperior\": \"Start User Superior\",\n        \"startUserDeptLeader\": \"Start User Department Leader\",\n        \"startUserDeptVP\": \"Start User Department VP\",\n        \"startUserCompanyLeader\": \"Start User Company Leader\",\n        \"startUserCompanyVP\": \"Start User Company VP\",\n        \"startUserProjectLeader\": \"Start User Project Leader\",\n        \"startUserProjectVP\": \"Start User Project VP\",\n        \"rejectAutoCancel\": \"Reject Auto Cancel\",\n        \"autoCancelTime\": \"Auto Cancel Time\",\n        \"hour\": \"Hour\",\n        \"timeoutAutoApproval\":\"Timeout Auto Approval\",\n        \"autoApprovalTime\":\"Auto Approval Time\",\n        \"processMaxApprovalCount\":\"Process Max Approval Count\",\n        \"nodeMaxApprovalCount\":\"Node Max Approval Count\",\n        \"nodeMaxConsultCount\":\"Node Max Consult Count\",\n        \"nodeMaxHandoverCount\":\"Node Max Handover Count\",\n        \"delayTime\":\"Delay Time（Day）\",\n        \"warningLight\": \"Warning Lights Setting\",\n        \"timeoutReminders\": \"Timeout Reminders\",\n        \"timeoutRemindersRemark\": \"（Multiple reminders separated by \';\' in hours）\",\n        \"timeoutRemindersMsg\": \"Only positive integers can be entered, separated by semicolons\"\n      },\n      \"list\": {\n        \"no\": \"No\",\n        \"item\": \"Item\",\n        \"dept\": \"Department\",\n        \"creator\": \"Creator\",\n        \"type\": \"Type\",\n        \"name\": \"Process Code\",\n        \"introduction\": \"Introduction\",\n        \"branch\": \"Branch\",\n        \"branchDescription\": \"Branch Description\",\n        \"inuse\": \"In Use\",\n        \"code\": \"Code\",\n        \"entryCondition\": \"Entry Condition\",\n        \"method\": \"Method\",\n        \"processConfigCondition\": \"Process Config\"\n      },\n      \"operation\": {\n        \"addBranch\": \"Add Branch\",\n        \"editBranch\": \"Edit Branch\",\n        \"deleteBranch\": \"Delete Branch\",\n        \"addRole\": \"Add Role\",\n        \"editRole\": \"Edit Role\",\n        \"deleteRole\": \"Delete Role\",\n        \"node\": \"Node: \",\n        \"editStep\": \"Edit Step\",\n        \"deleteStep\": \"Delete Step\",\n        \"save\": \"Save\",\n        \"preview\": \"Preview\",\n        \"previewDescription\": \"Data\",\n        \"doneDescription\": \"Done\",\n        \"description\": \"Data saved successfully\",\n        \"success\": \"Success\",\n        \"editStartStep\": \"Edit Start Step\",\n        \"editEndStep\": \"Edit End Step\",\n        \"copyName\": \"Copy Name\"\n      },\n      \"import\": {\n        \"upload\": \"Upload\",\n        \"description\": \"Upload excel process\",\n        \"notFound\": \"Please upload excel process\",\n        \"chooseDataTable\": \"Please select data permission table.\",\n        \"processData\": \"Import process authorization data.\",\n        \"processFinish\": \"Process import completed\"\n      },\n      \"steps\": {\n        \"previous\": \"Previous\",\n        \"next\": \"Next\"\n      },\n      \"message\": {\n        \"branchNameDuplicate\": \"Duplicate branch name\",\n        \"roleNameDuplicate\": \"Duplicate role name\",\n        \"inputErro\": \"Input format is incorrect\",\n        \"noBranches\": \"No branches\",\n        \"noRoles\": \"No roles\",\n        \"noValidData\": \"No valid data\"\n      },\n      \"api\": {\n        \"Name\": \"Excel Process\",\n        \"Order\": \"Order\",\n        \"BusinessType\": \"*Business Type\",\n        \"TagName\": \"Category Tag\",\n        \"InUse\": \"In Use\",\n        \"Condition\": \"Entrance Condition\",\n        \"ProcessCode\": \"*Process Code\",\n        \"ProcessName\": \"*Process Name\",\n        \"ShortName\": \"Short Name\",\n        \"BtId\": \"BTID\",\n        \"Authorizer\": \"Authorizer\",\n        \"AuthorizeType\": \"*Authorize Type\",\n        \"BranchIntroduction\": \"Branch Introduction\",\n        \"BranchName\": \"*Branch Name\",\n        \"Category\": \"Category\",\n        \"DepartmentName\": \"Department Name\",\n        \"Expression\": \"Expression\",\n        \"FormCode\": \"*Form Code\",\n        \"ManagerItem\": \"Manager Item\",\n        \"ErrorString\": \"The imported data contains special characters that are not recognized by the system!\",\n        \"Notfound\": \"Data not found!\",\n        \"EmptyBranch\": \"There are empty branches!\",\n        \"Start\": \"Start\",\n        \"End\": \"End\",\n        \"TagNotfound\": \"Sheet name:{0} not found in the system\",\n        \"ColumnNotfound\": \"Column:{0} not found in the sheet:{1}\",\n        \"ColumnEmpty\": \"Column:{1} value is empty in sheet:{0}\",\n        \"ColumnValueNotfound\": \"Column:{1} value:{2} not found in the system in sheet:{0} ！\",\n        \"GetTasError\": \"Get tags type:{0} failed!\",\n        \"DuplicateProcessName\": \"duplicate process name:{0} in sheet:{1}\",\n        \"ExportCheck\": \"\",\n        \"FillIntroduction\": {\n          \"Domain\": \"Domain\",\n          \"Role1\": \"Role1\",\n          \"Role2\": \"Role2\"\n        },\n        \"MatrixRole\": \"Matrix Role\",\n        \"DirectUser\": \"Direct User\",\n        \"Position\": \"Position\",\n        \"SpecificRole\": \"Specific Role\",\n        \"SystemVariable\": \"System Variable\",\n        \"BusinessVariable\": \"Business Variable\",\n        \"CommonRole\": \"CommonRole\",\n        \"MatrixRoleNotfound\": \"Matrix role:{1} in sheet:{0} does not exist\",\n        \"DirectUserNotfound\": \"Direct user:{1} in sheet:{0} does not exist\",\n        \"PositionNotfound\": \"Position:{1} in sheet:{0} does not exist\",\n        \"SpecificRoleNotfound\": \"Specific role:{1} in sheet:{0} does not exist\",\n        \"SystemVariableNotfound\": \"System variable:{1} in sheet:{0} does not exist\",\n        \"BusinessVariableNotfound\": \"Business variable:{1} in sheet:{0} does not exist\",\n        \"CommonRoleNotfound\": \"Common role:{1} in sheet:{0} does not exist\",\n        \"OrgLevelNotfound\": \"Organizational level:{1} in sheet:{0} does not exist\",\n        \"StartUser\": \"StartUser\",\n        \"StartUserSuperior\": \"StartUserSuperior\",\n        \"StartUserDeptLeader\": \"StartUserDeptLeader\",\n        \"StartUserDeptVP\": \"StartUserDeptVP\",\n        \"StartUserCompanyLeader\": \"StartUserCompanyLeader\",\n        \"StartUserCompanyVP\": \"StartUserCompanyVP\",\n        \"StartUserProjectLeader\": \"StartUserProjectLeader\",\n        \"StartUserProjectVP\": \"StartUserProjectVP\",\n        \"DomainLevelFormatError\": \"Domain level:{1} in sheet:{0} format error\",\n        \"DomainNotfound\": \"Domain:{1} in sheet:{0} does not exist\",\n        \"noBindForms\": \"No bind forms\"\n      }\n    },\n    \"business-object\": {\n      \"business-type\": \"Business Type\",\n      \"version\": \"Version\",\n      \"statusDataset\": [\n        {\n          \"label\": \"All\",\n          \"value\": \"\"\n        },\n        {\n          \"label\": \"Published\",\n          \"value\": \"published\"\n        },\n        {\n          \"label\": \"Unpublished\",\n          \"value\": \"unpublished\"\n        },\n        {\n          \"label\": \"Changing\",\n          \"value\": \"changing\"\n        }\n      ],\n      \"baseInfo\": \"Base Information\",\n      \"attribute\": \"Attribute\",\n      \"defaultValue\": \"Default value\",\n      \"length\": \"Lenght\",\n      \"decimal\": \"Decimal Separator\",\n      \"type\": \"Type\",\n      \"addData\": \"Please add data\",\n      \"detailTable\": \"Detail Table\",\n      \"attributeType\": [\n        {\n          \"label\": \"Text\",\n          \"value\": \"text\"\n        },\n        {\n          \"label\": \"Number\",\n          \"value\": \"number\"\n        },\n        {\n          \"label\": \"Table\",\n          \"value\": \"table\"\n        }\n      ],\n      \"pleaseSaveFieldFirst\": \"Please Save Field First\"\n    },\n    \"agent\": {\n      \"authorized\": \"Authorized person\",\n      \"agent\": \"agent\",\n      \"enabled\": \"Enable\"\n    },\n    \"data-authority\": {\n      \"name\": \"Authority name\",\n      \"config\": \"Authority config\",\n      \"role\": \"Authority role\",\n      \"organization\": \"Organization authority\",\n      \"business-type\": \"BusinessType  authority\",\n      \"systems\": \"System  authority\"\n    },\n    \"work-handover\": {\n      \"role-replace\": \"Role replace\",\n      \"todo-handover\": \"Todo Handover\",\n      \"fixed-handover\": \"Fixed Handover\",\n      \"change-person\": \"Change person\",\n      \"replacement\": \"Replacement\",\n      \"approver\": \"Approver\",\n      \"batch-change\": \"Batch change\",\n      \"batch-modify\": \"Batch modify\",\n      \"process-no\": \"Process No\",\n      \"process-topic\": \"Process topic\",\n      \"start-user\": \"Start user\",\n      \"start-time\": \"Start time\",\n      \"notice-user\": \"Notice User\",\n      \"current-step\": \"Current step\",\n      \"role\": \"Role\",\n      \"todo\": \"Todo\",\n      \"number\": \"Number\",\n      \"todo-type\": \"Todo type\",\n      \"arrive-time\": \"Arrive Time\",\n      \"selectAddUser\": \"Please select the undersigned!\",\n      \"selectHandUser\": \"Please select the hand User!\"\n    }\n  },\n  \"components\": {\n    \"comp-expression-designer\": {\n      \"title\": \"Expression Editor\",\n      \"defaultPromptText\": \"Click \\\"+\\\" to add an expression\",\n      \"explain\": \"explain:<br /> 1.xxx<br /> 2.xxx\",\n      \"text\": \"text\",\n      \"user\": \"user\",\n      \"number\": \"number\",\n      \"businessObjects\": \"Business Objects\",\n      \"globalObjects\": \"Global Objects\",\n      \"custom\": \"Custom\",\n      \"simpleEdit\": \"simple\",\n      \"seniorEdit\": \"senior\",\n      \"customEditPlaceholder\": \"Please enter a custom expression\",\n      \"badExpression\": \"Bad Expression\",\n      \"logicalOperatorList\": [\n        {\n          \"label\": \"and\",\n          \"value\": \"&&\",\n          \"expression\": \"&&\"\n        },\n        {\n          \"label\": \"or\",\n          \"value\": \"||\",\n          \"expression\": \"||\"\n        }\n      ],\n      \"operatorList\": [\n        {\n          \"label\": \"equal\",\n          \"value\": \"==\",\n          \"expression\": \"==\"\n        },\n        {\n          \"label\": \"not equal\",\n          \"value\": \"!=\",\n          \"expression\": \"!=\"\n        },\n        {\n          \"label\": \"greater\",\n          \"value\": \">\",\n          \"expression\": \">\"\n        },\n        {\n          \"label\": \"greater or equal\",\n          \"value\": \">=\",\n          \"expression\": \">=\"\n        },\n        {\n          \"label\": \"less\",\n          \"value\": \"<\",\n          \"expression\": \"<\"\n        },\n        {\n          \"label\": \"less or equal\",\n          \"value\": \"<=\",\n          \"expression\": \"<=\"\n        },\n        {\n          \"label\": \"contains\",\n          \"value\": \"Contains\",\n          \"expression\": \".Contains\"\n        },\n        {\n          \"label\": \"not contains\",\n          \"value\": \"Not Contains\",\n          \"expression\": \".Contains\"\n        },\n        {\n          \"label\": \"startsWith\",\n          \"value\": \"StartsWith\",\n          \"expression\": \".StartsWith\"\n        },\n        {\n          \"label\": \"not startsWith\",\n          \"value\": \"Not StartsWith\",\n          \"expression\": \".StartsWith\"\n        },\n        {\n          \"label\": \"endsWith\",\n          \"value\": \"EndsWith\",\n          \"expression\": \".EndsWith\"\n        },\n        {\n          \"label\": \"not endsWith\",\n          \"value\": \"Not EndsWith\",\n          \"expression\": \".EndsWith\"\n        }\n      ]\n    }\n  },\n  \"todoCenter\": {\n    \"archive\": {\n      \"BadParams\": \"Parameters \\\"business-id\\\" and \\\"business-number + business-id-extra\\\" can not both undefined.\"\n    },\n      \"fields\": {\n\"id\": \"id\",\n\"systemCode\": \"system Code\",\n\"subSystemCode\": \"sub System Code\",\n\"subSystemName\": \"sub System Name\",\n\"groupId\": \"group Id\",\n\"groupCode\": \"group Code\",\n\"groupName\": \"group Name\",\n\"groupIdPath\": \"group Id Path\",\n\"groupNamePath\": \"group Name Path\",\n\"businessID\": \"business ID\",\n\"businessNumber\": \"business Number\",\n\"businessTitle\": \"business Title\",\n\"businessStatusCode\": \"business Status Code\",\n\"businessStatusName\": \"business Status Name\",\n\"startTime\": \"start Time\",\n\"processId\": \"process Id\",\n\"processCode\": \"process Code\",\n\"processName\": \"process Name\",\n\"processVersion\": \"process Version\",\n\"summary\": \"summary\",\n\"remarks\": \"remarks\",\n\"startUserLoginId\": \"start User LoginId\",\n\"startUserName\": \"start User Name\",\n\"startUserOrgCode\": \"start User Org Code\",\n\"startUserOrgName\": \"start User Org Name\",\n\"startUserOrgCodePath\": \"start User Org Code Path\",\n\"startUserOrgNamePath\": \"start User Org Name Path\",\n\"ownerUserLoginId\": \"owner User LoginId\",\n\"ownerUserName\": \"owner User Name\",\n\"ownerUserOrgCode\": \"owner User Org Code\",\n\"ownerUserOrgName\": \"owner User Org Name\",\n\"tripartiteViewUrl\": \"tripartite View Url\",\n\"tripartiteViewMobileUrl\": \"tripartite View Mobile Url\",\n\"msgType\": \"msgType\",\n\"param1\": \"param1\",\n\"param2\": \"param2\",\n\"param3\": \"param3\",\n\"param4\": \"param4\",\n\"param5\": \"param5\",\n\"param6\": \"param6\",\n\"param7\": \"param7\",\n\"param8\": \"param8\",\n\"splitTb1\": \"split Tb1\",\n\"status\": \"status\",\n\"statusName\":\"status Name\",\n\"updateProcesingTime\": \"update Procesing Time\",\n\"createUserId\": \"create User Id\",\n\"createUserName\": \"create User Name\",\n\"createTime\": \"create Time\",\n\"lastModifyUserId\": \"last Modify UserId\",\n\"lastModifyUserName\": \"last ModifyUser Name\",\n\"lastModifyTime\": \"last Modify Time\",\n\"currentStepName\": \"current Step Name\",\n\"taskId\": \"taskId\",\n\"userLoginId\": \"user LoginId\",\n\"userName\": \"user Name\",\n\"userOrgCodePath\": \"user Org Code Path\",\n\"userOrgNamePath\": \"user Org Name Path\",\n\"urgency\": \"urgency\",\n\"isBatchApprove\": \"is BatchApprove\",\n\"isApprove\": \"is Approve\",\n\"isBatchHandOver\": \"is BatchHandOver\",\n\"overDateTime\": \"overDateTime\",\n\"isRead\": \"isRead\",\n\"specialLabel\": \"specialLabel\",\n\"useForGroupName\": \"use For Group Name\",\n\"arriveTime\": \"arrive Time\"\n}\n  },\n  \"forms\": {\n    \"modules\": {\n      \"Form\": \"Form\",\n      \"Model\": \"Model\",\n      \"Template\": \"Template\"\n    },\n    \"fields\": {\n      \"Code\": \"Code\",\n      \"Name\": \"Name\",\n      \"Data\": \"Data\",\n      \"BusinessObjectCode\": \"Business Object\",\n      \"BusinessTypeId\": \"Business Type\"\n    },\n    \"form\": {\n      \"placeholder\": {\n        \"Input\": \"Please input {0}\",\n        \"Select\": \"Please select {0}\"\n      },\n      \"publishCheck\": {\n        \"ExistPublishedData\": \"Published Data is not publishing! \",\n        \"ExistForbidData\": \"Forbid Data is not publishing! \"\n      },\n      \"existCode\": \"Form Code is exist! \",\n      \"checkOldVersionSave\": \"Old version does not allow saving! \",\n      \"processFormExisted\": \"Process form is exist! \",\n      \"checkFormBind\": \"Form is bound to the process and cannot be deleted!\"\n    },\n    \"designForm\": {\n      \"fields\": {\n        \"Number\": \"Number\",\n        \"Operation\": \"Operation\",\n        \"Total\": \"Total\",\n        \"Export\": \"Export\"\n      },\n      \"buttons\": {\n        \"Add\": \"Add\",\n        \"Delete\": \"Delete\",\n        \"CopyAndAdd\": \"Copy And Add\",\n        \"Export\": \"Export\"\n      }\n    },\n    \"model\": {\n      \"ExistCode\": \"Model Code is exist! \"\n    },\n    \"template\": {\n      \"fileNotfound\": \"Template File is not exist!\",\n      \"filePathNotfound\": \"Template Path is not exist! \",\n      \"onlineEditorUrlhNotfound\": \"Online Editor Url is not exist! \"\n    },\n    \"task\": {\n      \"apiUrlNotfound\": \"Task state api is not exist! \"\n    }\n  },\n  \"engine\": {\n    \"Instance\": {\n      \"StartActivityName\": \"Start\",\n      \"Forbidden\": \"Forbidden\"\n    }\n  },\n  \"modeling\": {\n    \"common\": {\n      \"total\": \"合计\",\n      \"operation\": \"操作\",\n      \"serial-number\": \"序号\",\n      \"create-date\": \"创建时间\",\n      \"update-date\": \"更新时间\",\n      \"edit\": \"编辑\",\n      \"delete\": \"删除\",\n      \"search\": \"查询\",\n      \"reset\": \"重置\",\n      \"up\": \"收起\",\n      \"down\": \"展开\",\n      \"add\": \"新增\",\n      \"save\": \"保存\",\n      \"publish\": \"发布\",\n      \"please-select\": \"请选择\",\n      \"please-input\": \"请输入\",\n      \"delTips\": \"请确认是否删除此数据?\"\n    },\n    \"data-base\": {\n      \"name\": \"数据库名称\",\n      \"description\": \"数据库描述\",\n      \"localhost\": \"数据库地址\",\n      \"account\": \"数据库账号\",\n      \"password\": \"数据库密码\",\n      \"type\": \"数据库类型\",\n      \"data-table\": \"数据表\",\n      \"btns\": {\n        \"generate-data-table\": \"生成数据表\"\n      }\n    },\n    \"data-table\": {\n      \"name\": \"数据表名称\",\n      \"description\": \"数据表描述\",\n      \"version-no\": \"版本号\",\n      \"sync-status\": \"同步状态\",\n      \"data-base\": \"数据库\",\n      \"field-name\": \"字段名称\",\n      \"field-description\": \"字段描述\",\n      \"field-length\": \"字段长度\",\n      \"field-decimal\": \"小数点\",\n      \"field-type\": \"字段类型\",\n      \"field-is-primary-key\": \"主键\",\n      \"field-is-nullable\": \"不为NULL\",\n      \"btns\": {\n        \"generate-modeling\": \"生成建模\",\n        \"sync-data-base\": \"同步数据库\"\n      },\n      \"tips\": {\n        \"length\": \"请至少录入一行数据\",\n        \"repeat\": \"存在重复字段名称\"\n      }\n    },\n    \"data-table-related\": {\n      \"data-base-name\": \"数据库\",\n      \"main-table-name\": \"主表\",\n      \"main-table-field\": \"主表字段\",\n      \"related-table-name\": \"关联表\",\n      \"related-table-field\": \"关联表字段\",\n      \"related-type\": \"关联类型\"\n    },\n    \"page-modeling\": {\n      \"name\": \"页面名称\",\n      \"type\": \"页面类型\",\n      \"data-source-type\": \"数据源类型\",\n      \"combination-page\": \"组合模板\",\n      \"one-page\": \"单页模板\",\n      \"list\": \"查询列表\",\n      \"form\": \"表单\",\n      \"start-desgin\": \"开始设计\",\n      \"control-attribute\": \"控件属性\",\n      \"page-attribute\": \"页面属性\",\n      \"empty-tips\": \"请先选择控件\",\n      \"btns\": {\n        \"priview\": \"预览\",\n        \"desgin\": \"设计\",\n        \"data-source\": \"数据源\"\n      }\n    },\n    \"module\": {\n      \"name\": \"模块名称\",\n      \"page-modeling\": \"建模页面\",\n      \"level\": \"层级\",\n      \"order-number\": \"排序号\",\n      \"parentModule\": \"父级模块\",\n      \"btns\": {\n        \"children\": \"新增子模块\"\n      }\n    }\n  },\n \"apiManage\": {\n    \"addApi\": \"Add API\",\n    \"apiCode\": \"Code\",\n    \"apiName\": \"API\",\n    \"showName\":\"Name\",\n    \"apiDesc\":\"Description\",\n    \"apiVersion\":\"Version\",\n    \"apiCategory\":\"Category\",  \n    \"buttons\": {\n      \"uploadPlugin\": \"Plugin Upload\",\n      \"pluginHelp\": \"Plugin develop help\"\n    },    \n    \"statusDataset\": [\n        {\n          \"label\": \"Deleted\",\n          \"value\": \"-1\"\n        },\n        {\n          \"label\": \"Disable\",\n          \"value\": \"0\"\n        },\n        {\n          \"label\": \"Enable\",\n          \"value\": \"1\"\n        }        \n      ],\n    \"apiCategoryDataset\": [\n        {\n          \"label\": \"Code\",\n          \"value\": \"1\"\n        },\n        {\n          \"label\": \"Plugin\",\n          \"value\": \"2\"\n        }        \n      ],\n      \"message\": {\n        \"onlyZipFile\": \"Only upload zip file\"\n      }      \n  },\n  \"integration\": {\n    \"titles\": {\n      \"addApp\": \"Add Application\",\n      \"editApp\": \"Edit Application\",\n      \"authApi\": \"Authorized  Api\",\n      \"cancelAuth\": \"Cancel Authorization\",\n      \"authApiGroup\": \"Authorized Api Group\",\n      \"apiList\": \"Api List\",\n      \"apiGroupList\": \"Api Group List\",\n      \"selectedApi\": \"Selected Api\",\n      \"selectedApiGroup\": \"Selected Api Groups\",       \n      \"apiGroupRequestDesign\": \"Group Request Design\",\n      \"apiGroupServerDesign\": \"Group Server Design\",\n      \"apiGroupResultDesign\": \"Group Result Design\",\n      \"resultMapping\": \"Result Mapping\",\n      \"requestBody\": \"Request Body\"\n    },    \n		\"fields\": {\n			\"service\": \"Service\",\n			\"serviceType\": \"Service Type\",\n			\"serviceCode\": \"Service Code\",\n			\"serviceName\": \"Service Name\",\n			\"appCode\": \"App Code\",\n			\"appName\": \"App Name\",\n      \"appPassword\": \"App Password\",\n			\"description\": \"Description\",\n			\"modelCode\": \"Model Code\",\n			\"modelName\": \"Model Name\",\n			\"remark\": \"Remark\",\n			\"hostUrl\": \"Host URL\",\n			\"wsdlUrl\": \"WSDL URL\",\n			\"dbHost\": \"DB Host\",\n			\"userName\": \"User Name\",\n			\"password\": \"Password\",\n			\"dbName\": \"Database\",\n			\"authenticationMode\": \"Authentication Mode\",\n			\"certifiedAPI\": \"Certified API\",\n			\"staticPassword\": \"Static Password\",\n			\"healthInterval\": \"Health Interval\",\n			\"healthTimeout\": \"Health Timeout\",\n			\"healthIntervalUnit\": \"Health Interval Unit\",\n			\"healthTimeoutUnit\": \"Health Timeout Unit\",\n			\"basicInfo\": \"Basic Information\",\n			\"connectionConf\": \"Connection Conf\",\n			\"completed\": \"Completed\",\n      \"affiliatedService\": \"Service\",\n      \"apiCode\": \"API Code\",\n      \"apiName\": \"API Name\",\n      \"apiUrl\": \"API URL\",\n      \"requestUrl\": \"Request Url\",\n      \"requestModel\": \"Request Model\",\n      \"apiGroupName\": \"Api Group Name\",\n      \"publishStatus\": \"Publish Status\",\n      \"dataType\": \"Data Type\",\n      \"requestSample\": \"Request Sample\",\n      \"fieldAttribute\": \"Field Attribute\",\n      \"isRequired\": \"Required\",\n      \"requestParamModule\": \"Request Param Module\",\n      \"mappingType\": \"Mapping Type\",\n      \"fixedValue\": \"Fixed Value\",\n      \"paramMapping\": \"Param Mapping\",\n      \"resultMapping\": \"Result Mapping\",\n      \"mappingValue\": \"Mapping Value\",\n      \"rootNode\": \"Root Node\"                  \n		},\n		\"validationExtend\": {\n			\"invalid\": \" invalid\",\n      \"existed\": {\n        \"serviceCode\": \"Service code already exists\",\n        \"apiCode\": \"API code already exists\"\n      }\n		},\n		\"actions\": {\n			\"previous\": \"Previous\",\n			\"next\": \"Next\",\n			\"viewDetail\": \"View\",\n			\"cancelTable\": \"Back\",\n			\"addApi\": \"Conf API\"\n		},\n		\"dicts\": {\n      \"serviceTypeDataset\": [\n        {\n					\"label\": \"Http(s)\",\n					\"value\": \"Https\"\n				},\n				{\n					\"label\": \"Web Service\",\n					\"value\": \"WebService\"\n				},\n				{\n					\"label\": \"SQL Server\",\n					\"value\": \"SqlServer\"\n				},\n				{\n					\"label\": \"My SQL\",\n					\"value\": \"MySql\"\n				}\n			],\n      \"exchangeModelStatus\": [\n        {\n				\"label\": \"Draft\",\n				\"value\": 0\n        },\n        {\n				\"label\": \"Enabled\",\n				\"value\": 1\n        },\n        {\n				\"label\": \"Disabled\",\n				\"value\": -1\n        }\n      ],\n      \"publishStatusDataSet\": [\n          {\n            \"label\": \"Unpublished\",\n            \"value\": -1\n          },\n          {\n            \"label\": \"Offline\",\n            \"value\": 0\n          },\n          {\n            \"label\": \"Published\",\n            \"value\": 1\n          }                 \n      ],\n      \"mappingTypeDataSet\": [\n          {\n            \"label\": \"Param Mapping\",\n            \"value\": \"ParamSchema\"\n          },\n          {\n            \"label\": \"Result Mapping\",\n            \"value\": \"ResultSchema\"\n          },\n          {\n            \"label\": \"Fixed Value\",\n            \"value\": \"FixedValue\"\n          } \n      ],\n      \"requestParamModuleDataSet\": [\n          {\n            \"label\": \"Transparent\",\n            \"value\": \"Transparent\"\n          },\n          {\n            \"label\": \"Mapping\",\n            \"value\": \"Mapping\"\n          }\n      ]            \n		},\n    \"messages\":{\n      \"notContainsApi\": \"Api group not contains any api\",\n      \"lessOneApi\": \"Choose less one API! \",\n      \"orderApi\": \"Adjusting the order of APIs will cause some mapping parameters to be lost. Are you sure?\",\n      \"requestPathFormat\": \"Format error, please start the request path with \\\"/\\\", for example: /user/info\",\n      \"apiGroupPublished\": \"Can\'t modify,delete or publish because the api group is published\",\n      \"apiGroupInvalid\": \"Can\'t  modify,delete or publish because the api group is invalid\",\n      \"apiGroupNotFound\": \"The api group is not found\",\n      \"cannotOffline\": \"Can\'t be offline for the status is not published\",\n      \"resultDataTypeNotMapping\": \" Field:{0}, data type mapping does not match\",\n      \"apiParamDataTypeNotMapping\":\"Api：{0}, Parameter:{1}, data type mapping does not match\"      \n    }\n	},\n  \"rulesEngine\": {\n    \"fields\":{\n      \"constantName\":\"常量名\",\n      \"constantValue\":\"常量字段\",\n      \"constantType\":\"常量类型\",\n      \"addConstant\":\"新增规则常量\",\n      \"editConstant\":\"编辑规则常量\"\n    }\n  }\n}','5f5f9bd67f75beb6e0c03c81a04427b1','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(17,'zh.customer.json','i18n','{\n    \"designers\": {\n        \"form-designer\": {\n            \"presets\": {\n                \"basic\": \"基础表单模版\",\n                \"master\": \"主子表单模版\"\n            },\n            \"name\": \"表单设计器\",\n            \"save-success\": \"保存成功\",\n            \"container-loop-error\": \"容器组件中不能再放入容器组件！\",\n            \"base-info\": \"基本信息\",\n            \"approval-info\": \"审批信息\",\n            \"relation\": \"相关流程\",\n            \"attachment\": \"附件信息\",\n            \"record\": \"审批记录\",\n            \"comment\": \"审批意见\"\n        },\n        \"group\": {\n            \"base-components\": \"基础组件\",\n            \"business-components\": \"业务组件\",\n            \"layout-components\": \"布局组件\",\n            \"detail-components\": \"明细表组件\",\n            \"component-property\": \"组件属性\",\n            \"form-property\": \"表单属性\"\n        },\n        \"control-property\": {\n            \"id\": \"ID\",\n            \"name\": \"名称\",\n            \"code\": \"编码\",\n            \"required\": \"必填\",\n            \"value\": \"默认值\",\n            \"colspan\": \"栏宽\",\n            \"colspan-1\": \"一列\",\n            \"colspan-2\": \"两列\",\n            \"dataset\": \"数据源\",\n            \"control-type\": \"控件\",\n            \"tips\": \"提示\",\n            \"limit\": \"限制字数\",\n            \"business-type\": \"业务类型\",\n            \"business-object\": \"业务对象\",\n            \"field\": \"字段\",\n            \"custom\": \"自定义\",\n            \"custom-field\": \"自定义字段\",\n            \"add-custom-field\": \"添加自定义字段\",\n            \"non-business-object\": \"未配置业务对象\",\n            \"child-table\": \"子表\",\n            \"setting\": \"配置\"\n        },\n        \"components\": {\n            \"text\": \"纯文本\",\n            \"input\": \"单行文本框\",\n            \"textarea\": \"多行文本框\",\n            \"number\": \"数字输入框\",\n            \"autocomplete\": \"自动完成\",\n            \"password\": \"密码输入框\",\n            \"select\": \"下拉框\",\n            \"datetime\": \"日期时间\",\n            \"switch\": \"逻辑开关\",\n            \"checkbox\": \"多选框\",\n            \"radio\": \"单选框\",\n            \"picture\": \"图片\",\n            \"attchment\": \"附件\",\n            \"link\": \"链接\",\n            \"modal\": \"弹窗选择\",\n            \"cascader\": \"级联选择\",\n            \"table\": \"表格\",\n            \"group\": \"分组\",\n            \"chart\": \"图表\",\n            \"history\": \"历史\"\n        }\n    },\n    \"framework\": {\n        \"enabled\": \"启用\",\n        \"disabled\": \"禁用\",\n        \"loading\": \"加载中\",\n        \"favorite\": \"常用流程\",\n        \"helper\": \"流程助手\",\n        \"refreshCache\": \"刷新缓存\",\n        \"stop-impersonate\": \"停止模拟\",\n        \"authorization\": \"流程授权\",\n        \"authorizated\": \"授权\",\n        \"approval\": \"审批\",\n        \"handover\": \"待办移交\",\n        \"proxy\": \"发起代理\",\n        \"logout\": \"退出\",\n        \"state-timeout\": \"登录超时\",\n        \"state-timeout-relogin\": \"登录超时，请重新登录。\",\n        \"state-timeout-relogin-help\": \"点击“确定”按钮，前往登录页面重新登录。\",\n        \"request-failed\": \"请求失败\",\n        \"upload-failed\": \"上传失败\",\n        \"notification\": \"提示信息\",\n        \"nonpermission\": \"你没有权限访问！\",\n        \"add\": \"添加\",\n        \"export\": \"导出\",\n        \"delete\": \"删除\",\n        \"delete-info\": \"确定要删除？\",\n        \"recall-info\": \"确定要撤回？\",\n        \"recall-counter-sign-info\": \"确定要撤回加签？\",\n        \"save\": \"保存\",\n        \"load-more\": \"加载更多\",\n        \"create\": \"新建\",\n        \"ok\": \"确定\",\n        \"operate\": \"操作\",\n        \"cancel\": \"取消\",\n        \"empty\": \"无\",\n        \"unauthorized\": \"你没有此页面的访问权限\",\n        \"no\": \"序号\",\n        \"prepositions\": {\n            \"s\": \"的\",\n            \"space\": \"\",\n            \"colon\": \"：\",\n            \"hour\": \"小时\",\n            \"total\": \"共\",\n            \"items\": \"条数据\"\n        },\n        \"systemAutomatic\": \"系统抄送\",\n        \"download\": \"下载\"\n    },\n	\"messages\": {\n		\"success\": \"操作成功\",\n		\"fail\": \"操作失败\"\n	},	\n    \"controlPoint-columns\": {\n        \"no\": \"序号\",\n        \"name\": \"管控要点\",\n        \"actual\": \"实际情况\",\n        \"light\": \"亮灯情况\",\n        \"standard\": \"判定标准\",\n        \"lightsType\": [{\n                \"label\": \"红灯\",\n                \"value\": \"red\"\n            }, {\n                \"label\": \"黄灯\",\n                \"value\": \"yellow\"\n            }, {\n                \"label\": \"绿灯\",\n                \"value\": \"green\"\n            }\n        ]\n    },\n    \"relation-columns\": {\n        \"no\": \"序号\",\n        \"number\": \"编号\",\n        \"topic\": \"流程主题\",\n        \"organization\": \"组织\",\n        \"start-user\": \"申请人\",\n        \"start-time\": \"申请时间\"\n    },\n    \"attachment-columns\": {\n        \"no\": \"序号\",\n        \"name\": \"附件名称\",\n        \"size\": \"大小\",\n        \"upload-user\": \"上传人\",\n        \"upload-step\": \"上传步骤\",\n        \"upload-time\": \"上传时间\"\n    },\n    \"approver-columns\": {\n        \"step-name\": \"步骤名称\",\n        \"user-name\": \"审批人\",\n        \"state-name\": \"审批结果\",\n        \"comment-text\": \"审批意见\",\n        \"resolve-time\": \"审批时间\"\n    },\n    \"inbox-columns\": {\n        \"instance-number\": \"流程编号\",\n        \"topic\": \"流程主题\",\n        \"start-date\": \"发起时间\",\n        \"start-username\": \"发起人\",\n        \"lastest-activity-name\": \"当前步骤\",\n        \"lastest-arrive-time\": \"流程到达时间\",\n        \"approve-time\": \"审批时间\",\n        \"status\": \"状态\",\n        \"operation\": \"操作\",\n        \"reason\": \"异常原因\",\n        \"attachment\": \"附件\",\n        \"actions\": \"操作\",\n        \"start-begin-date\": \"发起开始时间\",\n        \"start-end-date\": \"发起结束时间\",\n        \"begin-date\": \"开始时间\",\n        \"add-date\": \"添加时间\",\n        \"end-date\": \"结束时间\",\n        \"start-user-organization\": \"发起人组织\",\n        \"originator-username\": \"发起人\",\n        \"retention-time\": \"停留时长\",\n        \"retention-time-unit\": {\n            \"retention-time-hour\":\"时\",\n            \"retention-time-day\": \"天\",\n            \"retention-time-minute\":\"分钟\"\n        },\n        \"current-user\": \"当前处理人\",\n        \"approve-status\": \"审批状态\",\n        \"have-attachment\": \"是否有附件\",\n        \"business-type\": \"业务类型\",\n        \"read\":\"已读/未读\",\n        \"readOptions\":[\n            {\n                \"label\":\"已读\",\n                \"value\":\"read\"\n            },\n            {\n                \"label\":\"未读\",\n                \"value\":\"unRead\"\n            }\n        ]\n    },\n    \"authorization-helper\": {\n        \"current-user\": \"当前用户\",\n        \"my-angents\": \"我的代理人\",\n        \"enable\": \"启用\",\n        \"user\": \"授权人\",\n        \"authorizated-user\": \"被授权人\",\n        \"authorizated-process\": \"授权流程\",\n        \"start-time\": \"开始时间\",\n        \"end-time\": \"结束时间\",\n        \"description\": \"描述\",\n        \"detail\": \"明细\",\n        \"noSelectProcess\": \"没有选项流程\",\n        \"no\":\"序号\",\n        \"business-type\":\"业务类型\",\n        \"process-name\":\"流程名称\"\n    },\n    \"components\": {\n        \"search\": \"查询\",\n        \"reset\": \"重置\",\n        \"add\": \"添加\",\n        \"edit\": \"编辑\",\n        \"cancel\": \"取消\",\n        \"delete\": \"删除\",\n        \"advanced-search\": \"高级搜索\",\n        \"text\": \"请输入\",\n        \"select\": \"请选择\",\n        \"department\": \"部门\",\n        \"user\": \"人员\",\n        \"process\": \"流程\",\n        \"back\": \"返回\",\n        \"select-btn\": \"选择\",\n        \"authorization-process-view\":\"授权流程查看\"\n    },\n    \"main\": {\n        \"process-map\": \"发起流程\",\n        \"todo\": \"待办事项\",\n        \"cc-to-me\": \"待阅事项\",\n        \"my-processes\": \"我的发起\",\n        \"exceptions\": \"有异常\",\n        \"done\": \"已办事项\",\n        \"draft\": \"我的草稿\",\n        \"select-all\": \"全选\",\n        \"batch-approve\": \"批量同意\",\n        \"batch-reject\": \"批量退回\",\n        \"favorites\": \"常用流程\",\n        \"print\": \"打印\",\n        \"printSettings\": \"打印设置\",\n        \"notice\": \"通知\",\n        \"flow-map\": \"流程图\",\n        \"process-choose\": \"选择流程\",\n        \"ready-status\": \"待发起\",\n        \"form\": {\n            \"module\": [\n                {\n                \"label\": \"基本信息\",\n                \"value\": \"baseInfo\"\n                },\n                {\n                \"label\": \"相关流程\",\n                \"value\": \"relation\"\n                },\n                {\n                \"label\": \"相关附件\",\n                \"value\": \"attachment\"\n                },\n                {\n                \"label\": \"审批记录\",\n                \"value\": \"record\"\n                }\n            ]\n        },\n        \"SelectPrintModule\": \"选择打印模块\"\n    },\n    \"process-map\": {\n        \"favorite-processes\": \"常用流程\",\n        \"all-processes\": \"全部流程\",\n        \"process-name\": \"流程名称\",\n        \"belong-company\": \"所属组织\",\n        \"hot-words\": \"热词搜索\",\n        \"top-type\": \"所有分类\"\n    },\n    \"anchor-items\": {\n        \"base-info\": \"基本信息\",\n        \"form-info\": \"表单信息\",\n        \"attachment\": \"相关附件\",\n        \"comment\": \"审批意见\",\n        \"approveProcess\": \"审批记录\",\n        \"processDeduction\":\"流程推演\"\n    },\n    \"instance\": {\n        \"states\": {\n            \"start\": \"发起\",\n            \"approved\": \"通过\",\n            \"rejected\": \"退回\",\n            \"processing\": \"进行中\",\n            \"todo\": \"未审批\"\n        },\n        \"errors\": {\n            \"invalid-form\": \"表单无效，无法发起\"\n        },\n        \"number\": \"流程编号\",\n        \"actions\": {\n            \"start\": \"发起\",\n            \"restart\": \"重新发起\",\n            \"save\": \"保存\",\n            \"approve\": \"同意\",\n            \"reject\": \"退回\",\n            \"handover\": \"交办\",\n            \"counter-sign\": \"加签\",\n            \"notice\": \"通知\",\n            \"discuss\": \"加签\",\n            \"cancel\": \"作废\",\n            \"refuse\": \"拒绝\",\n            \"recall\": \"撤回\",\n            \"urging\": \"催办\",\n            \"recall-counter-sign\":\"撤回加签\",\n            \"receive\": \"提交\"\n        },\n        \"action-page\": {\n            \"sign\": {\n                \"sign-type\": \"加签类型：\",\n                \"extra-append\": \"向后加签\",\n                \"extra-insert\": \"向前加签\",\n                \"comment\": \"请输入加签意见\",\n                \"user\": \"加签人员\",\n                \"info\": \"提示：加签类型分为\'前置加签\'及\'后置加签\'。\",\n                \"info-insert\": \"前置加签：被加签人审批通过后，流程回到当前审批人。\",\n                \"info-append\": \"后置加签：被加签人审批通过后，流程进入当前审批人下一审批节点。\",\n                \"limit\":\"意见征询次数已达上限\"\n            },\n            \"reject\": {\n                \"reject-step\": \"退回步骤\",\n                \"reject-type\": \"退回类型\",\n                \"comment\": \"请输入退回意见\",\n                \"reject-new\": \"需重新逐级审批\",\n                \"reject-direct\": \"直接提交给退回人\",\n                \"info\": \"提示：退回类型分为\'需重新逐级审批\'及\'直接提交给驳回人\'。\",\n                \"reject-new-info\": \"需重新逐级审批：流程退回经发起后，继续一级一级审批。\",\n                \"reject-direct-info\": \"直接提交给驳回人：流程退回经发起后，直接到退回节点审批。\"\n            },\n            \"notice\": {\n                \"comment\": \"请输入通知意见\",\n                \"user\": \"通知人员\"\n            },\n            \"handover\": {\n                \"comment\": \"请输入交办意见\",\n                \"user\": \"交办人员\",\n                \"info\": \"提示：将当前审批{0}给选择的人，由被{0}人进行审批处理。\",\n                \"info-handover\": \"被{0}人待办列表中可以看到当前流程。\",\n                \"info-user\": \"当前审批人待办列表中不再显示当前流程\",\n                \"notAsignSelf\":\"本人不可以加签、交办给自己\",\n                \"limit\":\"加签次数已达上限\"\n            },\n            \"discuss\": {\n                \"comment\": \"请输入加签意见\",\n                \"user\": \"加签人员\"\n            },\n            \"approve\": {\n                \"tips\": \"您确认审批通过吗？\",\n                \"comment\": \"请输入审批意见\"\n            },\n            \"cancel\": {\n                \"tips\": \"您确认作废此流程吗？\",\n                \"comment\": \"请输入作废原因\"\n            },\n            \"refuse\": {\n                \"tips\": \"您确认拒绝此流程吗？\",\n                \"comment\": \"请输入拒绝原因\"\n            },\n            \"re-start\": {\n                \"title\": \"发起备注\",\n                \"comment\": \"请输入发起备注\",\n                \"not-agent\":\"您不能代理该用户发起\"\n            },\n            \"delay\": {\n               \"limit\":\"延时次数已达上限\" \n            }\n        },\n        \"notice\": {\n            \"start-page\": \"发起页面\",\n            \"approve-page\": \"审批页面\",\n            \"start-success\": \"发起成功\",\n            \"start-warning\": \"依据表单数据未找到符合的流程，请检查是否有必填项未填，或联系管理员。\",\n            \"matching-process-fail\": \"匹配流程失败\",\n            \"start-steps-unhealthy\": \"节点处理人不完善，无法发起\",\n            \"start-steps-repeat\": \"流程步骤名称存在重复\",\n            \"urging-success\": \"催办成功\",\n            \"save-success\": \"保存成功\",\n            \"done-success\": \"审批成功\",\n            \"sign-success\": \"意见征询成功\",\n            \"reject-success\": \"退回成功\",\n            \"notice-success\": \"通知成功\",\n            \"handover-success\": \"转交成功\",\n            \"discuss-success\": \"加签成功\",\n            \"collection-success\": \"收藏成功\",\n            \"collection-cancel\": \"取消收藏成功\",\n            \"cancel-success\": \"作废成功\",\n            \"refuse-success\": \"拒绝成功\",\n            \"delay-success\": \"延时成功\",\n            \"receive-success\": \"提交成功\",\n            \"todo\": \"审核页面\",\n            \"approve\": \"查看页面\",\n            \"cc-to-me\": \"查看页面\",\n            \"my-processes\": \"查看页面\",\n            \"done\": \"查看页面\",\n            \"start\": \"发起页面\",\n            \"version-inconsistency\": \"当前业务数据已发生变更,请到业务系统中重新核实提交!\",\n            \"data-exists\": \"流程数据已存在，不能发起,请检查BOID是否重复!\",\n            \"batch-done-success\": \"批量同意成功\",\n            \"invalid-comments\":\"退回后发起人修改关键字段，导致审批流程发生变化，原流程自动失效。\",\n            \"change-process1\":\"新提交的内容将导致审批流程及人员发生改变，请确认是否切换至新流程？\",\n            \"change-process2\":\"点击【确认】：会重新按新流程逐级审批，原流程自动失效；\",\n            \"change-process3\":\"点击【取消】：可返回到业务系统修改并重新发起；\",\n            \"change-process-title\":\"确认是否切换至新流程\",\n            \"step\":\"步骤\",\n            \"process-approver-limited\":\"流程审批人数不能大于\",\n            \"node-approver-limited\":\"步骤审批人数不能大于\",\n            \"node-approver-limited-message\":\"此节点审批人不能超过{0}人,请联系管理员调整!\",\n            \"process-approver-limited-message\":\"请注意,您发起的该流程总办理人员超过{0}人,请确认流程发起的有效性,慎重发起!\",\n            \"continue-start\":\"继续发起\",\n            \"reminder\":\"温馨提示\",\n            \"info\":\"信息\",\n            \"rejectConfirm\": \"您确认要退回该任务吗？\"\n        },\n        \"preview\": {\n            \"name\": \"审批流程\"\n        },\n        \"form-container\": {\n            \"name\": \"审批信息\",\n            \"user\": \"人员\",\n            \"organization\": \"组织\",\n            \"addCheck\": \"请选择组织\",\n            \"selectedOrganization\": \"已选择的组织\"\n        },\n        \"base-info\": {\n            \"name\": \"基本信息\",\n            \"topic\": \"流程主题\",\n            \"start-user\": \"申请人\",\n            \"organization\": \"所属组织\",\n            \"start-user-position\": \"申请人职位\",\n            \"start-time\": \"申请时间\",\n            \"process-type\":\"流程类别\"\n        },\n        \"record\": {\n            \"name\": \"审批记录\",\n            \"duration\": \"审批时长\",\n            \"user-name\": \"用户名\",\n            \"step-name\": \"步骤名称\",\n            \"skippedWhenEmptyResolver\": \"空审批人自动跳过\",\n            \"skippedWhenSameApprover\": \"相同审批人自动跳过\"\n        },\n        \"comment\": {\n            \"detail-name\": \"办理意见\",\n            \"start-name\": \"发起备注\",\n            \"can-input\": \"您还可以输入\",\n            \"word\": \"字\"\n        },\n        \"relation\": {\n            \"name\": \"相关流程\",\n            \"operate-name\": \"关联流程\",\n            \"relation\": \"关联实例\",\n            \"type\": \"关联类型\",\n            \"my-start\": \"我发起\",\n            \"my-cc\": \"抄送我\",\n            \"my-done\": \"我审批\"\n        },\n        \"deduction\": {\n            \"name\": \"流程推演\"\n        },\n        \"controlPoint\": {\n            \"name\": \"管控点\"\n        },\n        \"attachment\": {\n            \"name\": \"相关附件\",\n            \"operate-name\": \"上传附件\"\n        },\n        \"approver\": {\n            \"name\": \"办理情况\"\n        },\n        \"compentions\": {\n            \"process-topic\": {\n                \"new\": \"新\",\n                \"summary\": \"摘要\"\n            }\n        },\n        \"step-compar\": {\n            \"step-name\":\"步骤名\",\n            \"approver\":\"审批人\",\n            \"cc-user\":\"抄送人\",\n            \"step-compar-title\":\"审批步骤调整记录\",\n            \"title-default\":\"默认\",\n            \"title-act\":\"实际\"\n        },\n        \"approval-comment\": {\n            \"title\": \"常用意见\",\n            \"define\": \"自定义\",\n            \"needSave\": \"请保存\",\n            \"no\": \"序号\",\n            \"subject\": \"标题\",\n            \"comment\": \"意见\",\n            \"operation\": \"操作\",\n            \"modalTitle\": \"自定义常用意见\",\n            \"add\": \"新增\",\n            \"info\": \"说明：请输入常用意见的标题和详情，可拖拽排序。\",\n            \"more\": \"更多\"\n        },\n        \"send-recv-record\":{\n            \"name\":\"分发记录\"\n        }\n    },\n    \"instance-start\": {\n        \"actions\": {\n            \"start\": \"发起\",\n            \"save\": \"保存\"\n        },\n        \"start-comment\": {\n            \"name\": \"发起备注\"\n        },\n        \"preview\": {\n            \"name\": \"审批流程\",\n            \"deafult-step-name\": \"新步骤\",\n            \"approve-user\": \"审批人\",\n            \"cc-user\": \"抄送人\",\n            \"role-name\": \"角色名称\",\n            \"step\": \"步骤\"\n        },\n        \"start-success\": \"发起成功\"\n    },\n    \"table\": {\n        \"table-category\": \"按类别\",\n        \"table-urgency\": \"按时间\"\n    },\n    \"main-title\": {\n        \"top-title\": \"BPM审批平台\"\n    },\n    \"login\": {\n        \"title\": \"水务BPM审批平台\",\n        \"user-input\": \"用户名\",\n        \"psd-input\": \"密码\",\n        \"user-title\": \"用户登录\",\n        \"login-btn\": \"登录\",\n        \"code\": \"验证码\",\n        \"code-change\": \"换一张\"\n    },\n    \"user\": {\n        \"phone-number\": \"电 话\",\n        \"department-name\": \"部 门\",\n        \"email\": \"邮 箱\",\n        \"position-level\": \"职 位\",\n        \"organization\": \"组 织\",\n        \"leave\":\"离职\",\n        \"inValid\":\"无效\"\n    },\n    \"todo\": {\n        \"batch-operate-info\": \"请选择单据！\"\n    },\n    \"copyright\": \"\",\n    \"libang\": {\n        \"purchase-order\": {\n            \"name\": \"采购单\",\n            \"is-completion\": \"是否过账\",\n            \"supplier\": \"供应商\",\n            \"input-factory\": \"收货工厂\",\n            \"material-number\": \"物料编码\",\n            \"material-name\": \"物料名称\",\n            \"project-number\": \"项目号\",\n            \"quantity\": \"数量\",\n            \"transfer\": \"调拨类型\",\n            \"discount\": \"折扣\",\n            \"price\": \"原价格\",\n            \"new-price\": \"新价格\",\n            \"report-name\": \"采购单报表\"\n        },\n        \"retry-info\": \"确定要重推？\",\n        \"actions\": {\n            \"retry\": \"重推\"\n        }\n    },\n    \"paginations\": {\n        \"total\": \"共 {{value}} 条\"\n    }\n}','93adf7725b0ff10effbbe18568705e33','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(18,'zh.mobile.json','i18n','{\n  \"router\":{\n    \"default\":\"水务客户服务平台\",\n    \"todo\":\"待办中心\",\n    \"map\":\"发起流程\",\n    \"start\":\"流程发起\",\n    \"detailTodo\":\"流程审批\",\n    \"detailCcToMe\":\"流程查看\",\n    \"detailMyProcesses\":\"流程查看\",\n    \"detailDone\":\"流程查看\",\n    \"todoList\":\"待办列表\",\n    \"ccToMeList\":\"抄送我列表\",\n    \"doneList\":\"已办列表\",\n    \"draftList\":\"草稿列表\",\n    \"myStartList\":\"本人发起列表\",\n    \"workbench\":\"工作台\"\n  },\n  \"todoCenterTabs\":{\n    \"todo\":\"待办理\",\n    \"ccToMe\":\"抄送我\",\n    \"done\":\"已办理\",\n    \"myStart\":\"本人发起\",\n    \"draft\":\"草稿\"\n  },\n  \"fields\":{\n    \"systemName\": \"水务客户服务平台\",\n    \"todoCenter\":\"待办中心\",\n    \"workbench\":\"工作台\",\n    \"startProcess\":\"发起流程\",\n    \"processStart\":\"流程发起\",\n    \"baseInfo\":\"基本信息\",\n    \"approvalInfo\":\"表单信息\",\n    \"approvalRecord\":\"审批记录\",\n    \"sendRecvRecord\":\"分发记录\",\n    \"approvalProcess\":\"审批流程\",\n    \"startComment\":\"发起备注\",\n    \"attachmentInfo\":\"附件信息\",\n    \"relatedProcesses\":\"相关流程\",\n    \"controlPoint\":\"管控点\",\n    \"ccRecord\":\"抄送记录\",\n    \"todoMode\":{\n      \"urgent\":\"紧急模式\",\n      \"source\":\"来源模式\",\n      \"class\":\"分类模式\"\n    },\n    \"processMode\": \"流程模式\",\n    \"emptyData\":\"暂无数据\",\n    \"number\":\"编号\",\n    \"processNumber\":\"流程编号\",\n    \"noMore\":\"没有更多了\",\n    \"items\":\"项\",\n    \"commonProcesses\":\"我的常用\",\n    \"d\":\"的\",\n    \"g\":\"个\",\n    \"t\":\"共\",\n    \"processTopic\":\"流程主题\",\n    \"applicant\":\"申请人\",\n    \"organizationText\":\"所属组织\",\n    \"applicantPosition\":\"申请人职位\",\n    \"applicantTime\":\"申请时间\",\n    \"input\":\"输入\",\n    \"select\":\"选择\",\n    \"please\":\"请\",\n    \"agree\":\"同意\",\n    \"history\":\"历史\",\n    \"nothing\":\"无\",\n    \"approve\":\"审批\",\n    \"agent\":\"代\",\n    \"approvalDuration\":\"耗时\",\n    \"hour\":\"小时\",\n    \"read\":\"已读\",\n    \"unread\":\"未读\",\n    \"approvalPerson\":\"审批人\",\n    \"ccPerson\":\"抄送人\",\n    \"toReadPerson\":\"被抄送人\",\n    \"systemCC\":\"系统抄送\",\n    \"ccTime\":\"抄送时间\",\n    \"status\":\"状态\",\n    \"commonComment\":\"常用意见\",\n    \"stepName\":\"步骤名称\",\n    \"newStep\":\"新步骤\",\n    \"addStep\":\"新增步骤\",\n    \"editStep\":\"修改步骤\",\n    \"approvalStepName\":\"审批步骤名称\",\n    \"topLevel\":\"顶级\",\n    \"selectTree\":\"选择树\",\n    \"lowerLevel\":\"下级\",\n    \"selected\":\"已选择\",\n    \"selected2\": \"已选\",\n    \"time\":\"时间\",\n    \"beginTime\":\"开始时间\",\n    \"endTime\":\"结束时间\",\n    \"selectOrganization\":\"选择组织\",\n    \"selectPerson\":\"选择人员\",\n    \"changeOne\":\"换一张\",\n    \"lamp\":{\n      \"red\":\"红灯\",\n      \"yellow\":\"黄灯\",\n      \"green\":\"绿灯\"\n    },\n    \"controlPointKey\":\"管控要点\",\n    \"physicalTruth\":\"实际情况\",\n    \"lightingCondition\":\"亮灯情况\",\n    \"judgeStandard\":\"判定标准\",\n    \"userName\":\"用户名\",\n    \"password\":\"密码\",\n    \"verificationCode\":\"验证码\",\n    \"approvalComment\":\"审批意见\",\n    \"cancelComment\":\"作废意见\",\n    \"counterSignPerson\":\"加签人员\",\n    \"counterSignComment\":\"加签意见\",\n    \"counterSignMode\":\"加签方式\",\n    \"counterSignObject\":\"加签对象\",\n    \"forwardSignature\":\"向前加签\",\n    \"backwardSignature\":\"向后加签\",\n    \"handoverPerson\":\"转交人员\",\n    \"handoverComment\":\"转交意见\",\n    \"handoverObject\":\"转交对象\",\n    \"noticePerson\":\"通知人员\",\n    \"noticeObject\":\"通知对象\",\n    \"refuseComment\":\"拒绝原因\",\n    \"startPerson\":\"发起人\",\n    \"rejectComment\":\"退回意见\",\n    \"rejectStep\":\"退回步骤\",\n    \"rejectType\":\"退回类型\",\n    \"rejectDirect\":\"直接提交给退回人\",\n    \"rejectNew\":\"需重新逐级审批\",\n    \"emptyApprovalPerson\":\"空审批人\",\n    \"retentionTime\":{\n      \"stop\":\"停留\",\n      \"day\":\"天\",\n      \"hour\":\"时\",\n      \"minute\":\"分钟\"\n    },\n    \"process\": \"流程\",\n    \"company\": \"所属公司\",\n    \"processType\": \"流程类别\",\n    \"category\":\"类别\",\n    \"myStart\":\"我发起\",\n    \"myApproval\":\"我审批\",\n    \"ccToMe\":\"抄送我\",\n    \"startTime\":\"发起时间\",\n    \"class\": \"分类\",\n    \"top\":\"置顶\",\n    \"cancelTop\":\"取消置顶\",\n    \"surplus\":\"剩余\",\n    \"overtime\":\"超时\",\n    \"mailbox\": \"邮箱\",\n    \"post\":\"岗位\",\n    \"sendMessage\":\"发消息\",\n    \"Authorization\":\"授权\",\n    \"Authorized\":\"被授权\",\n    \"Transfer\":\"转交\",\n    \"Urge\":\"催办\",\n    \"ExtraInsert\":\"加签\",\n    \"ExtraAppend\":\"加签\",\n    \"ProcessDeduction\":\"流程推演\",\n    \"reminder\":\"温馨提示\",\n    \"info\":  \"信息\"\n  },\n  \"placeholders\":{\n    \"todoCenterSearch\":\"输入流程编号/流程主题/发起人\",\n    \"todoCenterSearch2\":\"输入流程编号/流程主题\",\n    \"draftCenterSearch\":\"输入流程主题/发起人\",\n    \"processStartSearch\":\"流程名称\",\n    \"searchKw\":\"请输入搜索关键词\",\n    \"instanceRelationSearch\":\"流程名称/流程编号/申请人\"\n  },\n  \"buttons\":{\n    \"selectAll\":\"全选\",\n    \"unselectAll\":\"取消全选\",\n    \"cancel\":\"取消\",\n    \"agree\":\"同意\",\n    \"saveDraft\":\"保存草稿\",\n    \"save\":\"保存\",\n    \"notice\":\"抄送\",\n    \"void\":\"作废\",\n    \"reStart\":\"重新发起\",\n    \"start\":\"发起\",\n    \"edit\":\"编辑\",\n    \"delete\":\"删除\",\n    \"preview\":\"预览\",\n    \"selectProcess\":\"选择流程\",\n    \"ok\":\"确定\",\n    \"confirm\":\"确认\",\n    \"filter\":\"筛选\",\n    \"download\":\"下载\",\n    \"selectAttachment\":\"选择附件\",\n    \"addStep\":\"添加步骤\",\n    \"remove\":\"移除\",\n    \"selectTime\":\"选择时间\",\n    \"login\":\"登录\",\n    \"reject\":\"退回\",\n    \"handover\":\"委托\",\n    \"counterSign\":\"加签\",\n    \"refuse\":\"拒绝\",\n    \"urging\":\"催办\",\n    \"withdraw\":\"撤回\",\n    \"submit\":\"提交\",\n    \"withdrawCounterSign\":\"撤回加签\",\n    \"close\":\"关闭\",\n    \"batchAgree\": \"批量同意原文意见\",\n    \"continue-start\":\"继续发起\",\n    \"receive\":\"提交\"\n  },\n  \"tips\":{\n    \"isAllowMobileStart\":\"此流程不允许在移动端发起，请至电脑端操作！\",\n    \"batchAgreeSuccess\":\"批量同意原文意见成功\",\n    \"startSuccess\":\"发起成功\",\n    \"hasBranch\":\"依据表单数据未找到符合的流程，请检查是否有必填项未填，或联系管理员。\",\n    \"requiredNotInput\":\"必填项未输入\",\n    \"uploadFormatError\":\"上传附件格式错误\",\n    \"uploading\":\"上传中...\",\n    \"uploadFail\":\"上传失败\",\n    \"areYouSureWithdraw\":\"请确认是否撤回\",\n    \"areYouSureUrge\":\"请确认是否催办\",\n    \"areYouSureWithdrawCounterSign\":\"确定要撤回加签?\",\n    \"urgingSuccess\":\"催办成功\",\n    \"approvalSuccess\":\"审批成功\",\n    \"cancelSuccess\":\"作废成功\",\n    \"counterSignSuccess\":\"加签成功\",\n    \"handoverSuccess\":\"转交成功\",\n    \"noticeSuccess\":\"抄送成功\",\n    \"refuseSuccess\":\"拒绝成功\",\n    \"rejectSuccess\":\"退回成功\",\n    \"withdrawSuccess\":\"撤回成功\",\n    \"alreadyStart\":\"该流程已成功发起。\",\n    \"saveDraftSuccess\":\"保存成功\",\n    \"tripartiteTodoMobileUrl\":\"三方待办移动端Url为空,请从pc端打开\",\n    \"start-steps-unhealthy\": \"节点处理人不完善，无法发起\",\n    \"start-steps-repeat\": \"流程步骤名称存在重复\",\n     \"matching-process-fail\": \"匹配流程失败\",\n     \"deleteSuccess\":\"删除成功\",\n     \"areYouSureDelete\":\"请确认是否删除?\",\n     \"rejectInfo\":\"提示：退回类型分为\'需重新逐级审批\'及\'直接提交给驳回人\'。\",\n      \"rejectDirect\":\"直接提交给退回人：申请人重新发起单据后，直接到退回人开始审批，不可对审批步骤做任何修改。\",\n     \"rejectNew\":\"需重新逐级审批退回：申请人重新发起单据后，需从头开始审批，可以重新添加相关审批人。\",\n     \"isAllowMobileRestart\":\"此流程不允许在移动端重新发起、作废，请至对应业务系统操作！\",\n     \"setTopSuccess\":\"置顶成功\",\n     \"cancelTopSuccess\":\"取消置顶成功\",\n     \"agreeConfirm\":\"请确认是否进行批量审批\",\n     \"handoveInfo\": \"提示：将当前审批{0}给选择的人，由被{0}人进行审批处理。\",\n      \"handoveInfoHandover\": \"被{0}人待办列表中可以看到当前流程。\",\n      \"handoveInfoUser\": \"当前审批人待办列表中不再显示当前流程\",\n      \"process-approver-limited\":\"流程审批人数不能大于\",\n      \"node-approver-limited\":\"步骤审批人数不能大于\",\n      \"sign-limit\":\"加签次数已达上限\",\n      \"handover-limit\":\"转交次数已达上限\",\n      \"delay-limit\":\"延时次数已达上限\",\n      \"delay-success\": \"延时成功\",\n      \"receive-success\": \"提交成功\",\n      \"node-approver-limited-message\":\"此节点审批人不能超过{0}人,请联系管理员调整!\",\n       \"process-approver-limited-message\":\"请注意,您发起的该流程总办理人员超过{0}人,请确认流程发起的有效性,慎重发起!\",\n        \"skippedWhenEmptyResolver\": \"空审批人自动跳过\",\n            \"skippedWhenSameApprover\": \"相同审批人自动跳过\",\n             \"rejectConfirm\":\"您确认要退回该任务吗？\",\n             \"deleteDraftSuccess\":\"删除草稿成功\"\n  }\n}','f5587dbaea82d6dfbd486692626d8a0e','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(19,'en.mobile.json','i18n','{\n  \"router\":{\n    \"default\":\"BPM流程管理平台\",\n    \"todo\":\"待办中心\",\n    \"map\":\"发起流程\",\n    \"start\":\"流程发起\",\n    \"detailTodo\":\"流程审批\",\n    \"detailCcToMe\":\"流程查看\",\n    \"detailMyProcesses\":\"流程查看\",\n    \"detailDone\":\"流程查看\",\n    \"todoList\":\"待办列表\",\n    \"ccToMeList\":\"抄送我列表\",\n    \"doneList\":\"已办列表\",\n    \"draftList\":\"草稿列表\",\n    \"myStartList\":\"本人发起列表\",\n    \"workbench\":\"工作台\"\n  },\n  \"todoCenterTabs\":{\n    \"todo\":\"待办理\",\n    \"ccToMe\":\"抄送我\",\n    \"done\":\"已办理\",\n    \"myStart\":\"本人发起\",\n    \"draft\":\"草稿\"\n  },\n  \"fields\":{\n    \"systemName\": \"盟拓移动端工作台\",\n    \"todoCenter\":\"待办中心\",\n    \"workbench\":\"工作台\",\n    \"startProcess\":\"发起流程\",\n    \"processStart\":\"流程发起\",\n    \"baseInfo\":\"基本信息\",\n    \"approvalInfo\":\"表单信息\",\n    \"approvalRecord\":\"审批记录\",\n    \"sendRecvRecord\":\"收发文记录\",\n    \"approvalProcess\":\"审批流程\",\n    \"startComment\":\"发起备注\",\n    \"attachmentInfo\":\"附件信息\",\n    \"relatedProcesses\":\"相关流程\",\n    \"controlPoint\":\"管控点\",\n    \"ccRecord\":\"抄送记录\",\n    \"todoMode\":{\n      \"urgent\":\"紧急模式\",\n      \"source\":\"来源模式\",\n      \"class\":\"分类模式\"\n    },\n    \"processMode\": \"流程模式\",\n    \"emptyData\":\"暂无数据\",\n    \"number\":\"编号\",\n    \"processNumber\":\"流程编号\",\n    \"noMore\":\"没有更多了\",\n    \"items\":\"项\",\n    \"commonProcesses\":\"我的常用\",\n    \"d\":\"的\",\n    \"g\":\"个\",\n    \"t\":\"共\",\n    \"processTopic\":\"流程主题\",\n    \"applicant\":\"申请人\",\n    \"organizationText\":\"所属组织\",\n    \"applicantPosition\":\"申请人职位\",\n    \"applicantTime\":\"申请时间\",\n    \"input\":\"输入\",\n    \"select\":\"选择\",\n    \"please\":\"请\",\n    \"agree\":\"同意\",\n    \"history\":\"历史\",\n    \"nothing\":\"无\",\n    \"approve\":\"审批\",\n    \"agent\":\"代\",\n    \"approvalDuration\":\"耗时\",\n    \"hour\":\"小时\",\n    \"read\":\"已读\",\n    \"unread\":\"未读\",\n    \"approvalPerson\":\"审批人\",\n    \"ccPerson\":\"抄送人\",\n    \"toReadPerson\":\"被抄送人\",\n    \"systemCC\":\"系统抄送\",\n    \"ccTime\":\"抄送时间\",\n    \"status\":\"状态\",\n    \"commonComment\":\"常用意见\",\n    \"stepName\":\"步骤名称\",\n    \"newStep\":\"新步骤\",\n    \"addStep\":\"新增步骤\",\n    \"editStep\":\"修改步骤\",\n    \"approvalStepName\":\"审批步骤名称\",\n    \"topLevel\":\"顶级\",\n    \"selectTree\":\"选择树\",\n    \"lowerLevel\":\"下级\",\n    \"selected\":\"已选择\",\n    \"selected2\": \"已选\",\n    \"time\":\"时间\",\n    \"beginTime\":\"开始时间\",\n    \"endTime\":\"结束时间\",\n    \"selectOrganization\":\"选择组织\",\n    \"selectPerson\":\"选择人员\",\n    \"changeOne\":\"换一张\",\n    \"lamp\":{\n      \"red\":\"红灯\",\n      \"yellow\":\"黄灯\",\n      \"green\":\"绿灯\"\n    },\n    \"controlPointKey\":\"管控要点\",\n    \"physicalTruth\":\"实际情况\",\n    \"lightingCondition\":\"亮灯情况\",\n    \"judgeStandard\":\"判定标准\",\n    \"userName\":\"用户名\",\n    \"password\":\"密码\",\n    \"verificationCode\":\"验证码\",\n    \"approvalComment\":\"审批意见\",\n    \"cancelComment\":\"作废意见\",\n    \"counterSignPerson\":\"意见征询人员\",\n    \"counterSignComment\":\"意见征询意见\",\n    \"counterSignMode\":\"意见征询方式\",\n    \"counterSignObject\":\"意见征询对象\",\n    \"forwardSignature\":\"向前意见征询\",\n    \"backwardSignature\":\"向后意见征询\",\n    \"handoverPerson\":\"委托人员\",\n    \"handoverComment\":\"委托意见\",\n    \"handoverObject\":\"委托对象\",\n    \"noticePerson\":\"通知人员\",\n    \"noticeObject\":\"通知对象\",\n    \"refuseComment\":\"拒绝原因\",\n    \"startPerson\":\"发起人\",\n    \"rejectComment\":\"退回意见\",\n    \"rejectStep\":\"退回步骤\",\n    \"rejectType\":\"退回类型\",\n    \"rejectDirect\":\"直接提交给退回人\",\n    \"rejectNew\":\"需重新逐级审批\",\n    \"emptyApprovalPerson\":\"空审批人\",\n    \"retentionTime\":{\n      \"stop\":\"停留\",\n      \"day\":\"天\",\n      \"hour\":\"时\",\n      \"minute\":\"分钟\"\n    },\n    \"process\": \"流程\",\n    \"company\": \"所属公司\",\n    \"processType\": \"流程类别\",\n    \"category\":\"类别\",\n    \"myStart\":\"我发起\",\n    \"myApproval\":\"我审批\",\n    \"ccToMe\":\"抄送我\",\n    \"startTime\":\"发起时间\",\n    \"class\": \"分类\",\n    \"top\":\"置顶\",\n    \"cancelTop\":\"取消置顶\",\n    \"surplus\":\"剩余\",\n    \"overtime\":\"超时\",\n    \"mailbox\": \"邮箱\",\n    \"post\":\"岗位\",\n    \"sendMessage\":\"发消息\",\n    \"Authorization\":\"授权\",\n    \"Authorized\":\"被授权\",\n    \"Transfer\":\"委托\",\n    \"Urge\":\"催办\",\n    \"ExtraInsert\":\"意见征询\",\n    \"ExtraAppend\":\"意见征询\",\n    \"ProcessDeduction\":\"流程推演\",\n    \"reminder\":\"温馨提示\",\n    \"info\":  \"信息\"\n  },\n  \"placeholders\":{\n    \"todoCenterSearch\":\"输入流程编号/流程主题/发起人\",\n    \"todoCenterSearch2\":\"输入流程编号/流程主题\",\n    \"draftCenterSearch\":\"输入流程主题/发起人\",\n    \"processStartSearch\":\"流程名称\",\n    \"searchKw\":\"请输入搜索关键词\",\n    \"instanceRelationSearch\":\"流程名称/流程编号/申请人\"\n  },\n  \"buttons\":{\n    \"selectAll\":\"全选\",\n    \"unselectAll\":\"取消全选\",\n    \"cancel\":\"取消\",\n    \"agree\":\"同意\",\n    \"saveDraft\":\"保存草稿\",\n    \"save\":\"保存\",\n    \"notice\":\"抄送\",\n    \"void\":\"作废\",\n    \"reStart\":\"重新发起\",\n    \"start\":\"发起\",\n    \"edit\":\"编辑\",\n    \"delete\":\"删除\",\n    \"preview\":\"预览\",\n    \"selectProcess\":\"选择流程\",\n    \"ok\":\"确定\",\n    \"confirm\":\"确认\",\n    \"filter\":\"筛选\",\n    \"download\":\"下载\",\n    \"selectAttachment\":\"选择附件\",\n    \"addStep\":\"添加步骤\",\n    \"remove\":\"移除\",\n    \"selectTime\":\"选择时间\",\n    \"login\":\"登录\",\n    \"reject\":\"退回\",\n    \"handover\":\"委托\",\n    \"counterSign\":\"意见征询\",\n    \"refuse\":\"拒绝\",\n    \"urging\":\"催办\",\n    \"withdraw\":\"撤回\",\n    \"submit\":\"提交\",\n    \"withdrawCounterSign\":\"撤回意见征询\",\n    \"close\":\"关闭\",\n    \"batchAgree\": \"批量同意原文意见\",\n    \"continue-start\":\"继续发起\",\n    \"receive\":\"receive\"\n  },\n  \"tips\":{\n    \"isAllowMobileStart\":\"此流程不允许在移动端发起，请至电脑端操作！\",\n    \"batchAgreeSuccess\":\"批量同意原文意见成功\",\n    \"startSuccess\":\"发起成功\",\n    \"hasBranch\":\"依据表单数据未找到符合的流程，请检查是否有必填项未填，或联系管理员。\",\n    \"requiredNotInput\":\"必填项未输入\",\n    \"uploadFormatError\":\"上传附件格式错误\",\n    \"uploading\":\"上传中...\",\n    \"uploadFail\":\"上传失败\",\n    \"areYouSureWithdraw\":\"请确认是否撤回\",\n    \"areYouSureUrge\":\"请确认是否催办\",\n    \"areYouSureWithdrawCounterSign\":\"确定要撤回意见征询?\",\n    \"urgingSuccess\":\"催办成功\",\n    \"approvalSuccess\":\"审批成功\",\n    \"cancelSuccess\":\"作废成功\",\n    \"counterSignSuccess\":\"意见征询成功\",\n    \"handoverSuccess\":\"委托成功\",\n    \"noticeSuccess\":\"抄送成功\",\n    \"refuseSuccess\":\"拒绝成功\",\n    \"rejectSuccess\":\"退回成功\",\n    \"withdrawSuccess\":\"撤回成功\",\n    \"alreadyStart\":\"该流程已成功发起。\",\n    \"saveDraftSuccess\":\"保存成功\",\n    \"tripartiteTodoMobileUrl\":\"三方待办移动端Url为空,请从pc端打开\",\n    \"start-steps-unhealthy\": \"节点处理人不完善，无法发起\",\n     \"start-steps-repeat\": \"流程步骤名称存在重复\",\n     \"matching-process-fail\": \"匹配流程失败\",\n     \"deleteSuccess\":\"删除成功\",\n     \"areYouSureDelete\":\"请确认是否删除?\",\n     \"rejectInfo\":\"提示：退回类型分为\'需重新逐级审批\'及\'直接提交给驳回人\'。\",\n      \"rejectDirect\":\"直接提交给退回人：申请人重新发起单据后，直接到退回人开始审批，不可对审批步骤做任何修改。\",\n     \"rejectNew\":\"需重新逐级审批退回：申请人重新发起单据后，需从头开始审批，可以重新添加相关审批人。\",\n     \"isAllowMobileRestart\":\"此流程不允许在移动端重新发起、作废，请至对应业务系统操作！\",\n     \"setTopSuccess\":\"置顶成功\",\n     \"cancelTopSuccess\":\"取消置顶成功\",\n     \"agreeConfirm\":\"请确认是否进行批量审批\",\n     \"handoveInfo\": \"提示：将当前审批{0}给选择的人，由被{0}人进行审批处理。\",\n      \"handoveInfoHandover\": \"被{0}人待办列表中可以看到当前流程。\",\n      \"handoveInfoUser\": \"当前审批人待办列表中不再显示当前流程\",\n      \"process-approver-limited\":\"流程审批人数不能大于\",\n      \"node-approver-limited\":\"步骤审批人数不能大于\",\n      \"sign-limit\":\"意见征询次数已达上限\",\n      \"handover-limit\":\"委托次数已达上限\",\n      \"delay-limit\":\"延时次数已达上限\",\n      \"delay-success\": \"延时成功\",\n      \"receive-success\": \"收到成功\",\n      \"node-approver-limited-message\":\"此节点审批人不能超过{0}人,请联系管理员调整!\",\n       \"process-approver-limited-message\":\"请注意,您发起的该流程总办理人员超过{0}人,请确认流程发起的有效性,慎重发起!\",\n        \"skippedWhenEmptyResolver\": \"空审批人自动跳过\",\n            \"skippedWhenSameApprover\": \"相同审批人自动跳过\",\n             \"rejectConfirm\":\"您确认要退回该任务吗？\",\n             \"deleteDraftSuccess\":\"删除草稿成功\"\n  }\n}','d819a08ba55f626cd75975c868982268','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(20,'medusa.service.integration.ocelot.appsettings.json','integrationcenter','{\n  \"Nacos\": {\n     \"ServerAddresses\": [ \"http://*************:8848\" ],\n     \"DefaultTimeOut\": 15000,\n     \"Namespace\": \"shuiwu\",\n     \"ListenInterval\": 1000,\n     \"GroupId\": \"integrationcenter\",\n     \"DataId\": \"medusa.service.integration.ocelot.appsettings.json\",\n     \"LanguageGroupId\": \"i18n\",\n     \"LanguageIds\": [\"en.json\", \"zh.json\"],\n     \"ServiceName\": \"OcelotApiGateway\"\n  },\n    \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Information\",\n      \"Microsoft\": \"Warning\",\n      \"Microsoft.Hosting.Lifetime\": \"Information\"\n    }\n  },\n  \"Persistence\": {\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Port=3306;Database=boost_standard;uid=bpm;Pwd=****************;\"\n    },\n    \"Integration\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Port=3306;Database=interfacecenter_dev;uid=bpm;Pwd=****************;\"\n    }\n  },\n  \"CollectSetting\": {\n    \"MongoConnectionString\": \"mongodb://localhost:27017\",\n    \"MongoDatabase\": \"DataExchange\"\n  },\n  \"OcelotSettings\": {\n    \"JobUri\": \"/integration-ocelot\"\n  },\n \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"600\",\n    \"IsHttps\": false\n  },\n  \"RabbitMQ\": {\n    \"HostName\": \"*************\",\n    \"Port\": \"5672\",\n    \"UserName\": \"guest\",\n    \"Password\": \"guest\",\n    \"DataTransQueue\": \"integration:data-trans\"\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000\n  },\n  \"OcelotConfigFolder\": \"./OcelotConfig\",\n  \"Ids4Setting\": {\n    \"IP\": \"*************\",\n    \"Port\": 32065,\n    \"IdentityScheme\": \"Bearer\"\n  },\n  \"NacosOcelotData\": {\n    \"DataId\": \"medusa.service.integration.ocelot.routes.json\",\n    \"Group\": \"integrationcenter\"\n  },\n  \"Ids4Mapping\": {\n    \"Beisen\": {\n      \"client_id\": \"app_key\",\n      \"client_secret\": \"app_secret\"\n    }\n  }\n}','16ad1ee2c6fd54b70f4f52708c9d02d7','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(21,'sqlhangfire.appsettings.json','cronjob','{\n  \"Logging\": {\n    \"IncludeScopes\": false,\n    \"LogLevel\": {\n      \"Default\": \"Trace\",\n      \"Microsoft\": \"Warning\",\n      \"Microsoft.Hosting.Lifetime\": \"Information\"\n    }\n  },\n  \"Hangfire\": {\n    \"HangfireSettings\": {\n      \"ServerName\": \"SqlserverHangfire\",\n      \"StartUpPath\": \"/job\",\n      \"ReadOnlyPath\": \"\",\n      \"JobQueues\": [ \"default\", \"apis\", \"recurring\", \"endpoint_instance_start\", \"endpoint_activity_turning\", \"endpoint_activity_completed\", \"endpoint_instance_end\", \"tripartite_system_instance_start\", \"tripartite_system_activity_turning\", \"tripartite_system_activity_completed\", \"tripartite_system_instance_end\"],\n      \"WorkerCount\": 50,\n      \"DisplayStorageConnectionString\": false,\n      \"HttpAuthInfo\": {\n        \"SslRedirect\": false,\n        \"RequireSsl\": false,\n        \"LoginCaseSensitive\": true,\n        \"IsOpenLogin\": true,\n        \"Users\": [\n          {\n            \"Login\": \"bpm\",\n            \"PasswordClear\": \"rPFwdOQHXnl5mzCW\"\n          },\n          {\n            \"Login\": \"guest\",\n            \"PasswordClear\": \"U0ymicSJzqeC41XZ\"\n          }\n        ]\n      },\n      \"ConnectionString\": \"server=*************;Port=3306;Database=hangfire;Integrated Security=False;User ID=sa;Password=****************;\"\n    },\n    \"HttpJobOptions\": {\n      \"Lang\": \"zh\",\n      \"DefaultTimeZone\": \"Asia/Shanghai\",\n      \"CurrentDomain\": \"//\",\n      \"EnableDingTalk\": true,\n      \"DefaultRecurringQueueName\": \"recurring\",\n      \"GlobalSettingJsonFilePath\": \"\",\n      \"Proxy\": \"\",\n      \"JobExpirationTimeoutDay\": 7,\n      \"GlobalHttpTimeOut\": 5000,\n      \"MailOption\": {\n        \"Server\": \"\",\n        \"Port\": 0,\n        \"User\": \"\",\n        \"Password\": \"\",\n        \"UseSsl\": false,\n        \"AlertMailList\": []\n      },\n      \"DingTalkOption\": {\n        \"Token\": \"\",\n        \"AtPhones\": \"\",\n        \"IsAtAll\": false\n      },\n      \"GatewayOption\": {\n        \"Enable\": false,\n        \"Host\": \"http://*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n      },\n      \"ApiRequestOption\": {\n          \"Host\": \"*************:6380\",\n          \"DB\": 8,\n          \"Password\": \"87htd2NxIQ0YguE9\",\n          \"Timeout\": \"30\",\n          \"IsHttps\": false\n      }      \n    }\n  }\n}','6d25fc1055f0eb7de177a69362856d28','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(22,'medusa.service.modeling.entrance.appsetting.json','modeling','{\n     \"Logging\": {\n        \"LogLevel\": {\n            \"Default\": \"Debug\",\n            \"System\": \"Information\",\n            \"Microsoft\": \"Information\"\n        }\n    },\n    \"Persistence\": {\n        \"DbType\": \"Mysql\",\n        \"ConnectionString\": \"server=*************;Port=3306;Database=LowCode;uid=bpm;Pwd=*****************"\n    },\n    \"Boost\": {\n        \"DbType\": \"Mysql\",\n        \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\n    },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"60\"\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": \"30\",\n        \"IsHttps\": false\n    },\n    \"EncryptKeys\": {\n        \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n    },\n    \"PlatformSettings\": {\n        \"ProductLicense\": \"/platform/v1/product-license/{id}\"\n    },\n    \"ProcessSettings\": {\n        \"BusinessObject\": \"/process/v1/manage/processes/{id}/business-object\",\n        \"StartProcess\": \"/process/endpoints/business-data\",\n        \"RecallProcess\": \"/process/endpoints/{procinstno}/recall\",\n        \"CancelProcess\": \"/process/endpoints/{procinstno}/cancel\",\n        \"StartUrl\": \"https://bpm.chinahho.cn:2005/customer/start?bsid={bsid}&btid={btid}&boid={boid}\",\n        \"ReStartUrl\": \"https://bpm.chinahho.cn:2005/customer/start/{procinstno}?bsid={bsid}&btid={btid}&boid={boid}\"\n    },\n      \"Redis\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 0,\n        \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"Message\": {\n        \"Url\": \"http://*************:32008/v1/messages/Wf_EOPNotifyMessage\",\n        \"SystemCode\": \"crm\"\n    },\n      \"RabbitMQ\": {\n        \"HostName\": \"*************\",\n        \"Port\": 5672,\n        \"UserName\": \"bpm\",\n        \"Password\": \"1nsc72AZBxNFLtw4\",\n        \"InterfaceQueue\": \"modeling.connect\"\n    },\n     \"BusinessOrg\": {\n        \"SET\": \"F6C33F44-11FE-44EE-9971-35D860EF78C8.0_7A6C2B1F-B76D-4C87-A132-E91C296B82D5.30\",\n        \"3C\": \"F6C33F44-11FE-44EE-9971-35D860EF78C8.0_C58FD84E-A710-4243-A5E5-BE555ECAD385.30\",\n        \"Mopro\": \"F6C33F44-11FE-44EE-9971-35D860EF78C8.0_3E81322A-2109-431B-93EB-860C524A6727.30\",\n        \"PV\": \"F6C33F44-11FE-44EE-9971-35D860EF78C8.0_25E17CC8-7A08-410B-8575-91BE5BFB6799.30\"\n    }\n}','8e5fe8043ad900edb881daf6715e5cee','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(23,'mobile.config.json','mobile','{\n  \"urlKey\": \"mobile\",\n  \"port\": 20082,\n  \"uploadLimit\": \"100mb\",\n  \"apiGateway\": {\n    \"uri\": \"http://*************:32000\",\n    \"appKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"appSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n  },\n  \"localServices\": [\n    {\n      \"enable\": false,\n      \"name\": \"form\",\n      \"uri\": \"http://localhost:5053\"\n    },\n    {\n      \"enable\": false,\n      \"name\": \"platform\",\n      \"uri\": \"http://localhost:5051\"\n    },\n    {\n      \"enable\": false,\n      \"name\": \"process\",\n      \"uri\": \"http://localhost:5052\"\n    }\n  ],\n  \"adminUri\": \"\",\n  \"sso\": {\n    \"type\": \"movitech\",\n    \"movitech\": {\n      \"pc\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/mobile/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      },\n      \"h5\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/mobile/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      },\n      \"wechat\": {\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\n        \"loginUri\": \"/shuiwuaccount/login\",\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\n        \"userStateUri\": \"/connect/userinfo\",\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/mobile/auth/sso-callback\",\n        \"localNetwork\":\"http://*************:32014\"\n      }\n    }\n  },\n  \"impersonatePath\": \"/auth/impersonate\",\n  \"saiwuSetting\":{\n    \"home\":\"https://bpm.chinahho.cn:2005/mobile/todo\"\n  },\n  \"noNeedAuthPaths\": {\n    \"state\": \"/platform/v1/userExts\",\n    \"captcha\": \"/platform/v1/captcha\"\n  },\n  \"session\": {\n    \"maxAge\": 3600,\n    \"redis\": {\n      \"host\": \"*************\",\n      \"port\": 6380,\n      \"db\": 0,\n      \"password\": \"87htd2NxIQ0YguE9\"\n    }\n  },\n  \"rabbitMQ\": {\n    \"UserName\": \"bpm\",\n    \"Password\": \"1nsc72AZBxNFLtw4\",\n    \"host\": \"*************\",\n    \"exchange\": \"boards.refresh\"\n  },\n  \"idocv\": {\n    \"view\": \"http://************/view/url?url=\"\n  },\n  \"IsParalle\": true,\n  \"mobilesatisfactionurl\":\"http://*************:32003/business-for-m/swcrm/modeling-view/682ed002-e8d6-48f7-b879-d6317fb2f0f4\",\n  \"expireminutes\":1440000\n}\n','b89769cc29cfe118c8d588396f4e605c','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(25,'medusa.service.document.entrance.appsetting.json','platform','{\n    \"Logging\": {\n        \"LogLevel\": {\n            \"Default\": \"Debug\",\n            \"System\": \"Information\",\n            \"Microsoft\": \"Information\"\n        }\n    },\n    \"AllowedHosts\": \"*\",\n    \"PlatformSettings\": {\n        \"ProductLicense\": \"/platform/v1/product-license/{id}\"\n    },\n    \"DocumentRootPath\": \"/var/www/boost/medusa.service.document/upload\",\n    \"LogSettings\": {\n        \"QueryDbType\": \"MySql\",\n        \"MinimumLevel\": 3,\n        \"DBName\": \"LogCenter\",\n        \"LogAddress\": \"server=*************;Database=logcenter;uid=bpm;Pwd=*****************"\n    },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"30\"\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": \"30\",\n        \"IsHttps\": false\n    },\n    \"IDocView\": {\n        \"UploadUrl\": \"http://************\",\n        \"ServerUrl\": \"http://************\",\n        \"Token\": \"testtoken\",\n        \"RootPath\": \"E:\\\\idocv\"\n    },\n    \"KKFile\": {\n        \"UploadUrl\": \"http://*************:8012/kkFileView\",\n        \"ServerUrl\": \"http://*************:8012/kkFileView\",\n        \"DownloadUrl\": \"http://*************:32057/v1/document-services/download/KKFile?FilePath=\"\n    },\n    \"Ftp\": {\n        \"Host\": \"*************\",\n        \"UserName\": \"ftpadmin\",\n        \"Password\": \"admin123\",\n        \"Port\": \"21\",\n        \"BaseDirectory\": \"/home/<USER>"\n    }    \n}','0972fec6c22fb9efa4e83b7d7808a3c6','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(26,'mysqlhangfire.appsettings.json','cronjob','{\n    \"Logging\": {\n        \"IncludeScopes\": false,\n        \"LogLevel\": {\n            \"Default\": \"Trace\",\n            \"Microsoft\": \"Warning\",\n            \"Microsoft.Hosting.Lifetime\": \"Information\"\n        }\n    },\n    \"Hangfire\": {\n        \"HangfireSettings\": {\n            \"ServerName\": \"MysqlHangfire\",\n            \"TablePrefix\": \"hangfire\",\n            \"StartUpPath\": \"/job\",\n            \"ReadOnlyPath\": \"\",\n      \"JobQueues\": [ \"default\", \"apis\", \"recurring\", \"endpoint_instance_start\", \"endpoint_activity_turning\", \"endpoint_activity_completed\", \"endpoint_instance_end\", \"tripartite_system_instance_start\", \"tripartite_system_activity_turning\", \"tripartite_system_activity_completed\", \"tripartite_system_instance_end\", \"todomessage\", \"othermessage\"],\n            \"WorkerCount\": 40,\n            \"DisplayStorageConnectionString\": false,\n            \"HttpAuthInfo\": {\n                \"SslRedirect\": false,\n                \"RequireSsl\": false,\n                \"LoginCaseSensitive\": true,\n                \"IsOpenLogin\": true,\n                \"Users\": [\n                    {\n                        \"Login\": \"bpm\",\n                        \"PasswordClear\": \"rPFwdOQHXnl5mzCW\"\n                    },\n                    {\n                        \"Login\": \"guest\",\n                        \"PasswordClear\": \"U0ymicSJzqeC41XZ\"\n                    }\n                ]\n            },\n            \"ConnectionString\": \"server=*************;Port=3306;Database=hangfire;uid=bpm;Pwd=****************;charset=utf8;SslMode=none;Allow User Variables=True\"\n        },\n        \"HttpJobOptions\": {\n            \"Lang\": \"zh\",\n            \"DefaultTimeZone\": \"Asia/Shanghai\",\n            \"CurrentDomain\": \"//\",\n            \"EnableDingTalk\": true,\n            \"DefaultRecurringQueueName\": \"recurring\",\n            \"GlobalSettingJsonFilePath\": \"\",\n            \"Proxy\": \"\",\n            \"JobExpirationTimeoutDay\": 7,\n            \"GlobalHttpTimeOut\": 5000,\n            \"MailOption\": {\n                \"Server\": \"\",\n                \"Port\": 0,\n                \"User\": \"\",\n                \"Password\": \"\",\n                \"UseSsl\": false,\n                \"AlertMailList\": []\n            },\n            \"DingTalkOption\": {\n                \"Token\": \"\",\n                \"AtPhones\": \"\",\n                \"IsAtAll\": false\n            },\n            \"GatewayOption\": {\n                \"Enable\": false,\n                \"Host\": \"*************\",\n                \"Port\": \"32000\",\n                \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n                \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n            },\n            \"ApiRequestOption\": {\n                \"Host\": \"*************:6380\",\n                \"DB\": 8,\n                \"Password\": \"87htd2NxIQ0YguE9\",\n                \"Timeout\": \"30\",\n                \"IsHttps\": false\n            },\n            \"OcelotAuthOption\": {\n                \"Host\": \"http://*************:32065/connect/Token\",\n                \"UserName\": \"Admin\",\n                \"Password\": \"12345678\",\n                \"GrantType\": \"client_credentials\",\n                \"UrlPrefix\": \"integration-ocelot\"\n            },\n            \"RabbitMQOption\": []            \n        }\n    }\n}\n','511cc7d79d79e0a093a5454643a50e84','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(27,'medusa.service.identity.appsetting.json','identity','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Information\",\n      \"Microsoft\": \"Warning\",\n      \"Microsoft.Hosting.Lifetime\": \"Information\"\n    }\n  },\n  \"UrlKey\": \"sso\",\n  \"RequestScheme\": \"http\",\n  \"AuthorityUrl\": \"\",\n  \"SSOType\": \"account\",\n  \"EnableLog\": true,\n  \"LdapSettings\": {\n    \"Enable\": true,\n    \"UseSSL\": false,\n    \"ServerName\": \"movit-tech.com\",\n    \"ServerPort\": 389\n  },\n  \"DdSettings\": {\n    \"Enable\": true,\n    \"ApiUrl\": \"https://oapi.dingtalk.com\",\n    \"AppKey\": \"\",\n    \"AppSecret\": \"zVg9dJvcMEkkhN8XyK4WcW_kph_iXevekRFJOToG_P6wWd4qqukIF-RJmoeUo4R_\"\n  },\n  \"Persistence\": {\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"SaiWuSettings\": {\n    \"ClientId\": \"bpm\",\n    \"ClienSecret\": \"bpm\",\n    \"AuthUrl\": \"http://***********:1011\",\n    \"Redirect_uri\": \"http://***************:32014/saiwuaccount/sso/callback\"\n  },\n  \"ShuiWuSettings\": {\n    \"ClientId\": \"bpm\",\n    \"ClienSecret\": \"bpm\",\n    \"AuthUrl\": \"https://portal.chinahho.cn:2006\",\n    \"InternalAuthUrl\": \"https://portal.chinahho.cn:2006\",\n    \"Redirect_uri\": \"https://bpm.chinahho.cn:2005/identity/shuiwuaccount/sso/callback\"\n  }\n}\n','787b943fbcb565c1942a51305bad91e6','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(28,'medusa.service.integration.appsettings.json','integrationcenter','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Information\",\n      \"Microsoft\": \"Warning\",\n      \"Microsoft.Hosting.Lifetime\": \"Information\"\n    }\n  },\n  \"Persistence\": {\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Port=3306;Database=boost_standard;uid=bpm;Pwd=*****************"\n    },\n    \"Integration\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Port=3306;Database=interfacecenter_dev;uid=bpm;Pwd=****************;\"\n    }\n  },\n  \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"600\",\n    \"IsHttps\": false\n  },\n  \"AuthSettings\": {\n    \"CreateClient\": \"/integration-auth/GatewayAuth/create-client\",\n    \"UpdateClientPassword\": \"/integration-auth/GatewayAuth/update-client-password\",\n    \"DeleteClient\": \"/integration-auth/GatewayAuth/delete-client\"\n  },\n  \"OcelotSettings\": {\n    \"Host\": \"http://*************:32064\",\n    \"JobUri\": \"/integration-ocelot\",\n    \"ReloadConfig\": \"/integration-ocelot/v1/reload-config\"\n  },\n  \"CollectSetting\": {\n    \"MongoConnectionString\": \"mongodb://localhost:27017\",\n    \"MongoDatabase\": \"DataExchange\"\n  },\n  \"IntegrationSettings\": {\n    \"Host\": \"http://*************:32063\"\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000\n  },\n  \"RabbitMQ\": {\n    \"HostName\": \"*************\",\n    \"Port\": \"5672\",\n    \"UserName\": \"guest\",\n    \"Password\": \"guest\",\n    \"DataTransQueue\": \"integration:data-trans\"\n  },\n  \"BeisenHr\": {\n    \"Token\": {\n      \"url\": \"https://openapi.italent.cn/token\",\n      \"type\": \"Bearer\",\n      \"app_key\": \"BCA8B516164D4357831E9074602D24A6\",\n      \"app_secret\": \"9E5B320AA747404C91061BA4883541E15A56B82C9ACC4924B4ADC8A4C8407199\",\n      \"grant_type\": \"client_credentials\"\n    }\n  }\n}\n','2daaef1138d0b5c0fb11d6892c5509e5','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(30,'medusa.engine.evententrance.adaptersettings.json','engine','{\r\n    \"Persistence\": {\r\n        \"Boost\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\r\n        },\r\n        \"Process\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\r\n        }\r\n    },\r\n    \"TodoExpireTime\": 36,\r\n    \"CategroryLevel\": 2,\r\n    \"AutoCancelTime\": 48,\r\n    \"AutoApprovalTime\": 36\r\n}','7873dfd8a70e057dab78a210e01a519f','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(31,'todo-center.config.json','todocenter','{\r\n  \"urlKey\": \"todo-center\",\r\n  \"port\": 20080,\r\n  \"uploadLimit\": \"200mb\",\r\n  \"apiGateway\": {\r\n    \"uri\": \"http://*************:32000\",\r\n    \"appKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\r\n    \"appSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\r\n  },\r\n  \"localServices\": [],\r\n  \"adminUri\": \"\",\r\n\"sso\": {\r\n    \"type\": \"movitech\",\r\n    \"movitech\": {\r\n      \"pc\": {\r\n        \"redirectUri\": \"https://bpm.chinahho.cn:2005/identity\",\r\n        \"loginUri\": \"/shuiwuaccount/login\",\r\n        \"logoutUri\":  \"/shuiwuaccount/logout\",\r\n        \"userStateUri\": \"/connect/userinfo\",\r\n        \"callbackUri\": \"https://bpm.chinahho.cn:2005/todo-center/auth/sso-callback\",\r\n        \"localNetwork\":\"http://*************:32014\"\r\n      }\r\n    }\r\n  },\r\n  \"impersonatePath\": \"/todo-center/auth/impersonate\",\r\n  \"noNeedAuthPaths\": {\r\n    \"state\": \"/platform/v1/userExts\",\r\n    \"captcha\": \"/platform/v1/captcha\"\r\n  },\r\n  \"session\": {\r\n    \"maxAge\": 3600,\r\n    \"redis\": {\r\n      \"host\": \"*************\",\r\n      \"port\": 6380,\r\n      \"db\": 0,\r\n      \"password\": \"87htd2NxIQ0YguE9\"\r\n    }\r\n  },\r\n  \"rabbitMQ\": {\r\n    \"UserName\": \"bpm\",\r\n    \"Password\": \"1nsc72AZBxNFLtw4\",\r\n    \"host\": \"*************\",\r\n    \"exchange\": \"boards.refresh\"\r\n  },\r\n  \"idocv\":{\r\n    \"view\":\"http://************/view/url?url=\"\r\n  },\r\n   \"formDesginConfig\": {\r\n    \"processClientViewUrl\": \"https://bpm.chinahho.cn:2005/customer/done/{procinstNo}\",\r\n    \"fileUploadType\": \"local\",\r\n    \"baiduak\": \"QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz\",\r\n    \"logoGotoLink\": \"https://portal.chinahho.cn:2006/portal-web\"\r\n  },\r\n\"mobilesatisfactionurl\":\"\"\r\n}','727ff7e3066fdd67793cd4a095a2f6ad','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(32,'medusa.service.business.entrance.appsetting.json','business','{\r\n    \"Logging\": {\r\n        \"LogLevel\": {\r\n            \"Default\": \"Debug\",\r\n            \"System\": \"Information\",\r\n            \"Microsoft\": \"Information\"\r\n        }\r\n    },\r\n    \"Persistence\": {\r\n        \"Process\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\r\n        }\r\n    },\r\n    \"LogSettings\": {\r\n        \"MongoDBContext\": \"mongodb://*************:27017\"\r\n    },\r\n    \"EngineSettings\": {\r\n        \"NoticeApi\": \"/engine/v1/notice\"\r\n    },\r\n    \"ApiGateway\": {\r\n        \"Host\": \"*************\",\r\n        \"Port\": \"32000\",\r\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\r\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\r\n        \"Timeout\": \"30\"\r\n    },\r\n    \"ApiRequest\": {\r\n        \"Host\": \"*************:6380\",\r\n        \"DB\": 8,\r\n        \"Password\": \"87htd2NxIQ0YguE9\",\r\n        \"Timeout\": 600,\r\n        \"IsHttps\": false\r\n    },\r\n    \"BPM\": {\r\n        \"TokenTimeout\": 10080\r\n    },\r\n    \"EventBusSettings\": {\r\n    \"Redis\": {\r\n      \"Host\": \"*************:6380,abortConnect=false,connectTimeout=60000\",\r\n      \"DB\": 1,\r\n      \"CacheDb\":7,\r\n      \"Password\": \"87htd2NxIQ0YguE9\"\r\n    }\r\n  },\r\n    \"WebServiceSettings\": {\r\n        \"Identification\": \"\",\r\n        \"ZBAPI_PO_GETDETAIL\": \"\",\r\n        \"ZMM_PO_UPDATEPRICE\": \"\"\r\n    },\r\n    \"SystemType\": \"\"\r\n}','30712de499ee42f09a95ea08610cd765','2023-09-08 08:41:36','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(88,'medusa.engine.evententrance.appsetting.json','engine','{\r\n  \"Logging\": {\r\n    \"LogLevel\": {\r\n      \"Default\": \"Debug\"\r\n    }\r\n  },\r\n  \"AllowedHosts\": \"*\",\r\n  \"Persistence\": {\r\n    \"Engine\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM-Engine;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"EngineEventBus\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM_EngineEventBus;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"EngineCache\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=BPM-EngineCache;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"LogCenter\": {\r\n      \"DbType\": \"Mysql\",\r\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"Boost\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\r\n    },\r\n    \"Process\": {\r\n            \"DbType\": \"Mysql\",\r\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\r\n    }\r\n  },\r\n  \"EventBusSettings\": {\r\n    \"RabbitMQ\": {\r\n      \"ExchangeName\": \"bpm4.engine\",\r\n      \"HostName\": \"*************\",\r\n      \"UserName\": \"bpm\",\r\n      \"Password\": \"1nsc72AZBxNFLtw4\",\r\n      \"IsSubscribeDebugMessage\": false\r\n    },\r\n    \"Redis\": {\r\n      \"Host\": \"*************:6380\",\r\n      \"DB\": 1,\r\n      \"CacheDb\":7,\r\n      \"Password\": \"87htd2NxIQ0YguE9\"\r\n    },\r\n    \"EngineRedis\": {\r\n      \"Host\": \"*************:6379\",\r\n      \"DB\": 2,\r\n      \"Password\": \"7Qx0YFWyUDsbjzBv\"\r\n    },\r\n    \"Metadata\": {\r\n      \"GroupName\": \"bpm4.engine\",\r\n      \"KeyPrefix\": \"engine\",\r\n      \"KeyExpireHour\": null,\r\n      \"TablePrefix\": \"\",\r\n      \"ExpiresDays\": 7,\r\n      \"FailedRetryCount\": 1,\r\n      \"FailedRetryInterval\": 3600,\r\n      \"SucceedMessageExpiredAfter\": 86400,\r\n      \"LockExpiredInterval\": 3600,\r\n      \"ConsumerThreadCount\": 10\r\n    },\r\n    \"IsEnableAsyncOnExecuteEndpoint\": true,\r\n    \"MergeEvent\": true,\r\n    \"LogEventTime\": true,\r\n    \"DelayTime\": 1\r\n  },\r\n  \"PlatformSettings\": {\r\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\"\r\n  },\r\n    \"TodoPushSettings\": {\r\n        \"PushType\": \"mq\",\r\n        \"PushCenterSiteUrl\": \"https://bpm.chinahho.cn:2005/customer\",\r\n        \"PushCenterMobileSiteUrl\": \"https://bpm.chinahho.cn:2005/mobile\",\r\n        \"PushSystemCode\": \"BPM\",\r\n        \"RabbitMQ\": {\r\n            \"VirtualHost\": \"/\",\r\n            \"ExchangeName\": \"\",\r\n            \"HostName\": \"*************\",\r\n            \"Port\": 5672,\r\n            \"UserName\": \"bpm\",\r\n            \"Password\": \"1nsc72AZBxNFLtw4\"\r\n        },\r\n        \"MyStart\": {\r\n            \"Method\": \"POST\",\r\n            \"Url\": \"/todo-centre/v1/myStart/batchInsert\",\r\n            \"Queue\": \"othermessage\"\r\n        },\r\n        \"UpdateStartStatus\": {\r\n            \"Method\": \"PUT\",\r\n            \"Url\": \"/todo-centre/v1/myStart/batchUpdateStepAndBusinessStatus\",\r\n            \"Queue\": \"othermessage\"\r\n        },\r\n        \"MyTodo\": {\r\n            \"Method\": \"POST\",\r\n            \"Url\": \"/todo-centre/v1/task/batchInsert\",\r\n            \"Queue\": \"todomessage\"\r\n        },\r\n        \"MyDone\": {\r\n            \"Method\": \"POST\",\r\n            \"Url\": \"/todo-centre/v1/archivedTask/batchInsert\",\r\n            \"Queue\": \"othermessage\",\r\n            \"exchange\": \"todo.fanout\",\r\n            \"FanoutQueues\": [ \"portalmessage_lyle\" ]\r\n        },\r\n        \"MyCC\": {\r\n            \"Method\": \"POST\",\r\n            \"Url\": \"/todo-centre/v1/carboncopy/batchInsert\",\r\n            \"Queue\": \"othermessage\"\r\n        },\r\n        \"AddDraft\": {\r\n            \"Method\": \"POST\",\r\n            \"Url\": \"/todo-centre/v1/draft/batchInsert\",\r\n            \"Queue\": \"othermessage\"\r\n        },\r\n        \"DeleteDraft\": {\r\n            \"Method\": \"Delete\",\r\n            \"Url\": \"/todo-centre/v1/draft/batchDelete\",\r\n            \"Queue\": \"othermessage\"\r\n        },\r\n         \"UpdateTodo\": {\r\n            \"Method\": \"PUT\",\r\n            \"Url\": \"/todo-centre/v1/task/batchUpdate\",\r\n            \"Queue\": \"todomessage\"\r\n        }\r\n    },\r\n  \"LogSettings\": {\r\n    \"QueryDbType\": \"MySql\",\r\n    \"MinimumLevel\": 3,\r\n    \"DBName\": \"LogCenter\",\r\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\r\n  },\r\n  \"ApiGateway\": {\r\n    \"Host\": \"*************\",\r\n    \"Port\": \"32000\",\r\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\r\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\r\n    \"Timeout\": \"30\"\r\n  },\r\n  \"ApiRequest\": {\r\n    \"Host\": \"*************:6380\",\r\n    \"DB\": 8,\r\n    \"Password\": \"87htd2NxIQ0YguE9\",\r\n    \"Timeout\": \"30\",\r\n    \"IsHttps\": false\r\n  },\r\n  \"CronJob\": {\r\n    \"Host\": \"http://*************\",\r\n    \"Port\": \"32004\",\r\n    \"Uri\": \"/job\",\r\n    \"BasicUserName\": \"bpm\",\r\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\r\n    \"DefaultTimeOut\": \"180000\",\r\n    \"DefaultRetryDelaysInSeconds\": \"60,3600,21600\"\r\n  },\r\n  \"AutoJobSettings\": {\r\n      \"RejectAutoCancel\": {\r\n          \"Method\": \"PUT\",\r\n          \"Url\": \"/engine/v1/reject-auto-instance-cancel\",\r\n          \"Comment\": \"\"\r\n      },\r\n      \"AutoApproval\": {\r\n          \"Method\": \"PUT\",\r\n          \"Url\": \"/engine/v1/timeout-auto-tasks\",\r\n          \"Comment\": \"超出设定办理时间，系统已自动办理通过\"\r\n      },\r\n      \"AutoApprovalMessage\": {\r\n          \"Method\": \"POST\",\r\n          \"Url\": \"/platform/v1/messages/Wf_TaskAutoApprovalMessage\",\r\n          \"Comment\": \"\"\r\n      }\r\n  }\r\n}','6abe15b2732f5f7aeea0fe89fe94a0c4','2023-09-12 06:09:35','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(130,'medusa.service.integration.ocelot.routes.json','integrationcenter','{\"Routes\":[{\"ApiId\":\"19586f45-d0ed-4c97-b7fb-ee63c3dcd893\",\"DownstreamPathTemplate\":\"/todo-centre/v1/SFW/PushLeave\",\"UpstreamPathTemplate\":\"/PushLeave\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"9EA94h8zoK+aiP3Qafqj+sFO7LwYoiU4DJur6lDIESwdC0k363rDRqWeIi4fVTp7jKc60CGEpy2OPPn6qLvBIw==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"4b224771-8bea-4acc-a18e-4a207da0125e\",\"DownstreamPathTemplate\":\"/process/v1/instanceSendRecvRecord/addListByInstance\",\"UpstreamPathTemplate\":\"/bpm/process/v1/instanceSendRecvRecord/addListByInstance\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"IkRqbJAeE74jGnFeQU3pc3jVvgHpZgqNVRufq01nfB7uoCKpi7jmOVocMLy0CajdWqxtbH5dvOR+r4bicyTIEg==\",\"request-module\":\"Transparent\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1\",\"DownstreamPathTemplate\":\"/biz-logic/v1/instances/end\",\"UpstreamPathTemplate\":\"/end\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"3WvfFUIUmaxJBrJhc2m52Hrn6UgxXGBSdMmcS0VtzOu/2sJ/uTcNRnNFXxVWuGIvObvqDPjiiPDqRPmHCAuk4Q==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"701c80a7-473a-4dd4-8449-3fe71e476714\",\"DownstreamPathTemplate\":\"/process/v2/processes/structuredstorage\",\"UpstreamPathTemplate\":\"/structuredstorage\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"qyD1loa5vXgymL8+QdMHc9q0RuH1UYl9B3qSH/DqV1sVqXVqs9yIVA+wb61uVIz3PVxz0IVy5kAvdLvMZYjg6w==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"7448fd78-311b-422e-a80d-48508b0a6c43\",\"DownstreamPathTemplate\":\"/process/v1/document-no/cancel-number\",\"UpstreamPathTemplate\":\"/cancelNumber\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"BO+tWksWcKWOjum1OrAH6LXVHw3t5/vZqlipglyS+ujMqZ1ZHsR0swvpZO0dXR4y9w2k2daQZ+tm/0U3UsKAQA==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"8208b1ec-9498-4b89-b568-58891e4ea19e\",\"DownstreamPathTemplate\":\"/process/v1/hr/user-annual-leave/{user-id}\",\"UpstreamPathTemplate\":\"/user-annual-leave\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Get\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"q4eyfdmo8C30xwHSJbvrCSeJDIZ61CnOxLNn9XcTW113bRdq2HRkOMchBUwp6YTnTfL+ttO8ddSxYIWA1vOqeg==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb\",\"DownstreamPathTemplate\":\"/modeling/v1/common/business-object-data/integration-center\",\"UpstreamPathTemplate\":\"/GetPostJob\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"/KE83TWpe4xDtUa0zsxa0RIQBGcDzK9GXxvQ1FGfj8m30NHxzHH9lbFOe9zFo3z55dtHo0qrhQFYqsQ0j20t/A==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"aed8cc16-8e06-4b9a-87ac-4afed36cbfdc\",\"DownstreamPathTemplate\":\"/process/v1/document-no/generate-number\",\"UpstreamPathTemplate\":\"/generate-number\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"usmbSGj1fUWzUmS+R6VVVPNWq8OLPcujCfd03z5E32kicRRZ3w0cmuLygSEip+KPVcMY/lURgVvoXQrSfIP66w==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"b42d7073-f79c-459b-831d-715bc65fd83e\",\"DownstreamPathTemplate\":\"/todo-centre/v1/SFW/CallBackBS\",\"UpstreamPathTemplate\":\"/CallBackBS\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"L251+2zO4aQFOzZjTSptaTFQ7WwyYmRntmbjAZkOJOoRZpmTzLSK27uH3gP5TwyzSSG88ThaO0+V6tjxLgcqfw==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"cbd670df-1478-42d2-a7e4-f4b90dd88c35\",\"DownstreamPathTemplate\":\"/todo-centre/v1/SFW/PushNews\",\"UpstreamPathTemplate\":\"/PushNews\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"br1V9c4+XkSNQunjv6iYwBY2HBlRbFCCWE8eScSqnHoCj/0X1GVwpsVJp3Dy72H6cj1ZN7Y2Q6qrob2b049BEA==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"cd49b8d1-8535-4579-a4bc-76768a29e959\",\"DownstreamPathTemplate\":\"/process/v1/webOffice/wrapheader/update-bookmark\",\"UpstreamPathTemplate\":\"/update-bookmark\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"U5WOe6aCuTQ1V17zOgZF3qEuKAuR/wG1oUa0UHmy9LCDStfI2ReAcqeYADQixBjteomSBtsjVFMzB3cuxexHlA==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null},{\"ApiId\":\"f2465026-b9ca-40b9-86d1-c4396337f46f\",\"DownstreamPathTemplate\":\"/process/v1/webOffice/wrapheader\",\"UpstreamPathTemplate\":\"/wrapheader\",\"UpstreamHttpMethod\":[\"Post\"],\"DownstreamHttpMethod\":\"Post\",\"AddHeadersToRequest\":{},\"UpstreamHeaderTransform\":{\"api-token\":\"iXGpqmcO7ZirVUWvTv8xP8SDZ+cmLIfSEXXToJK6pC9gnP1qhMcAkKA6y3Mimyk9ROMTBv68aXO+49webklgTg==\",\"request-module\":\"Mapping\",\"api-category\":\"internal\"},\"DownstreamHeaderTransform\":{},\"AddClaimsToRequest\":{},\"RouteClaimsRequirement\":{},\"AddQueriesToRequest\":{},\"ChangeDownstreamPathTemplate\":{},\"RequestIdKey\":null,\"FileCacheOptions\":{\"TtlSeconds\":0,\"Region\":null},\"RouteIsCaseSensitive\":false,\"ServiceName\":null,\"ServiceNamespace\":null,\"DownstreamScheme\":\"http\",\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"RateLimitOptions\":{\"ClientWhitelist\":[],\"EnableRateLimiting\":false,\"Period\":null,\"PeriodTimespan\":0.0,\"Limit\":0},\"AuthenticationOptions\":{\"AuthenticationProviderKey\":\"ApiIds4\",\"AllowedScopes\":[\"Identity.bpm.Scope\",\"AdminScope\"]},\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHostAndPorts\":[{\"Host\":\"127.0.0.1\",\"Port\":80}],\"UpstreamHost\":null,\"Key\":null,\"DelegatingHandlers\":[\"OcelotHttpsDistributor\"],\"Priority\":1,\"Timeout\":0,\"DangerousAcceptAnyServerCertificateValidator\":false,\"SecurityOptions\":{\"IPAllowedList\":[],\"IPBlockedList\":[]},\"DownstreamHttpVersion\":null}],\"DynamicRoutes\":[],\"Aggregates\":[],\"GlobalConfiguration\":{\"RequestIdKey\":null,\"ServiceDiscoveryProvider\":{\"Scheme\":\"Http\",\"Host\":\"*************\",\"Port\":8848,\"Type\":\"Nacos\",\"Token\":null,\"ConfigurationKey\":null,\"PollingInterval\":0,\"Namespace\":null},\"RateLimitOptions\":{\"ClientIdHeader\":\"ClientId\",\"QuotaExceededMessage\":null,\"RateLimitCounterPrefix\":\"ocelot\",\"DisableRateLimitHeaders\":false,\"HttpStatusCode\":429},\"QoSOptions\":{\"ExceptionsAllowedBeforeBreaking\":0,\"DurationOfBreak\":0,\"TimeoutValue\":0},\"BaseUrl\":null,\"LoadBalancerOptions\":{\"Type\":null,\"Key\":null,\"Expiry\":0},\"DownstreamScheme\":null,\"HttpHandlerOptions\":{\"AllowAutoRedirect\":false,\"UseCookieContainer\":false,\"UseTracing\":false,\"UseProxy\":true,\"MaxConnectionsPerServer\":**********},\"DownstreamHttpVersion\":null}}','3c7b54c4bd35a4437fcc3615ee125c62','2023-10-27 08:25:55','2025-04-19 15:03:19',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL),(156,'medusa.service.sync.appsetting.json','sync','{\r\n	\"Persistence\": {\r\n		\"Boost\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=*************;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom2\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom3\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		}\r\n	},\r\n	\"Logging\": {\r\n		\"LogLevel\": {\r\n			\"Default\": \"Warning\"\r\n		}\r\n	},\r\n	\"LogSettings\": {\r\n		\"MongoDBContext\": \"mongodb://172.20.138.48:27017\"\r\n	},\r\n	\"SystemName\": \"ShuiWu\",\r\n	\"SystemSettings\": {\r\n		\"LianTou\": {},\r\n		\"SaiWu\": {},\r\n		\"MeiTuan\": {},\r\n		\"ShuiWu\": {\r\n			\"GetTokenUrl\": \"https://portal.chinahho.cn:2006/token/signLogin\",\r\n			\"TokenClientId\": \"bpm\",\r\n			\"TokenCode\": \"xiajingjun\",\r\n			\"GetOrganizationsUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/office-list\",\r\n			\"GetPositionsUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/post-list\",\r\n			\"GetUsersUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/user-list\",\r\n			\"DefaultDomainId\": \"f7d23c73-78cb-4387-b16d-0a76871bc806\",\r\n			\"DefaultDomainName\": \"中国水务\",\r\n			\"DefaultDomainLevelId\": \"de5f3287-4c58-4a64-b16e-6493c61e85ea\",\r\n			\"DefaultDomainLevelName\": \"部门\",\r\n			\"DefaultDomainLevelCode\": \"Deapartment\",\r\n			\"DefaultWeight\": \"100\",\r\n			\"DefaultPassword\": \"wGii546mdvsW3JYqds+0cJxK/NU=,123456\",\r\n	        \"RefreshCacheUrl\": \"http://*************:32008/v1/manage\",\r\n            \"ProcessMapCacheUrl\": \"http://*************:32009/v1/process-map/setProcessMap_Cache\"\r\n		}\r\n	}\r\n}','4ad7109f0dac5fb3a2bb59067af6a8f3','2023-11-09 06:31:01','2025-07-23 03:20:08',NULL,'*************','','shuiwu','','','','json',''),(535,'mysqlhangfire.todocentre1.appsettings.json','cronjob','{\n    \"Logging\": {\n        \"IncludeScopes\": false,\n        \"LogLevel\": {\n            \"Default\": \"Trace\",\n            \"Microsoft\": \"Warning\",\n            \"Microsoft.Hosting.Lifetime\": \"Information\"\n        }\n    },\n    \"Hangfire\": {\n        \"HangfireSettings\": {\n            \"ServerName\": \"MysqlHangfire\",\n            \"TablePrefix\": \"hangfire\",\n            \"StartUpPath\": \"/job\",\n            \"ReadOnlyPath\": \"\",\n      \"JobQueues\": [ \"default\", \"apis\", \"recurring\", \"endpoint_instance_start\", \"endpoint_activity_turning\", \"endpoint_activity_completed\", \"endpoint_instance_end\", \"tripartite_system_instance_start\", \"tripartite_system_activity_turning\", \"tripartite_system_activity_completed\", \"tripartite_system_instance_end\", \"todomessage\", \"othermessage\"],\n            \"WorkerCount\": 40,\n            \"DisplayStorageConnectionString\": false,\n            \"HttpAuthInfo\": {\n                \"SslRedirect\": false,\n                \"RequireSsl\": false,\n                \"LoginCaseSensitive\": true,\n                \"IsOpenLogin\": true,\n                \"Users\": [\n                    {\n                        \"Login\": \"bpm\",\n                        \"PasswordClear\": \"rPFwdOQHXnl5mzCW\"\n                    },\n                    {\n                        \"Login\": \"guest\",\n                        \"PasswordClear\": \"U0ymicSJzqeC41XZ\"\n                    }\n                ]\n            },\n            \"ConnectionString\": \"server=*************;Port=3306;Database=hangfire-todocentre1;uid=bpm;Pwd=****************;charset=utf8;SslMode=none;Allow User Variables=True\"\n        },\n        \"HttpJobOptions\": {\n            \"Lang\": \"zh\",\n            \"DefaultTimeZone\": \"Asia/Shanghai\",\n            \"CurrentDomain\": \"//\",\n            \"EnableDingTalk\": true,\n            \"DefaultRecurringQueueName\": \"recurring\",\n            \"GlobalSettingJsonFilePath\": \"\",\n            \"Proxy\": \"\",\n            \"JobExpirationTimeoutDay\": 7,\n            \"GlobalHttpTimeOut\": 5000,\n            \"MailOption\": {\n                \"Server\": \"\",\n                \"Port\": 0,\n                \"User\": \"\",\n                \"Password\": \"\",\n                \"UseSsl\": false,\n                \"AlertMailList\": []\n            },\n            \"DingTalkOption\": {\n                \"Token\": \"\",\n                \"AtPhones\": \"\",\n                \"IsAtAll\": false\n            },\n            \"GatewayOption\": {\n                \"Enable\": false,\n                \"Host\": \"*************\",\n                \"Port\": \"32000\",\n                \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n                \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\"\n            },\n            \"ApiRequestOption\": {\n                \"Host\": \"*************:6380\",\n                \"DB\": 8,\n                \"Password\": \"87htd2NxIQ0YguE9\",\n                \"Timeout\": \"30\",\n                \"IsHttps\": false\n            },\n            \"OcelotAuthOption\": {\n                \"Host\": \"http://*************:32065/connect/Token\",\n                \"UserName\": \"Admin\",\n                \"Password\": \"12345678\",\n                \"GrantType\": \"client_credentials\",\n                \"UrlPrefix\": \"integration-ocelot\"\n            },\n            \"RabbitMQOption\": [\n                {\n                    \"HostName\": \"*************\",\n                    \"Port\": 5672,\n                    \"UserName\": \"bpm\",\n                    \"Password\": \"1nsc72AZBxNFLtw4\",\n                    \"VirtualHost\": \"/\",\n                    \"Queues\": [\n                        {\n                            \"Name\": \"todomessage\",\n                            \"NumOfThreads\": 3,\n                            \"UrlReplaces\": [\n                                {\n                                    \"OldStr\": \"/todo-centre/\",\n                                    \"NewStr\": \"/todo-centre-dataprocess/\"\n                                }\n                            ]\n                        },\n                        {\n                            \"Name\": \"othermessage\",\n                            \"NumOfThreads\": 3,\n                            \"UrlReplaces\": [\n                                {\n                                    \"OldStr\": \"/todo-centre/\",\n                                    \"NewStr\": \"/todo-centre-dataprocess/\"\n                                }\n                            ]\n                        },\n                        {\n                            \"Name\": \"portalmessage\",\n                            \"NumOfThreads\": 3,\n                            \"UrlReplaces\": [\n                                {\n                                    \"OldStr\": \"/todo-centre/\",\n                                    \"NewStr\": \"/todo-centre-dataprocess/\"\n                                }\n                            ]\n                        },\n                        {\n                            \"Name\": \"bpm-job-queue\",\n                            \"NumOfThreads\": 3,\n                            \"UrlReplaces\": [\n                                {\n                                    \"OldStr\": \"/todo-centre/\",\n                                    \"NewStr\": \"/todo-centre-dataprocess/\"\n                                }\n                            ]\n                        }]\n                }\n            ]            \n        }\n    }\n}\n','9dfc23db48a6c519dfd28622126bc6aa','2024-10-26 01:56:53','2024-10-26 01:56:53',NULL,'*************','','shuiwu',NULL,NULL,NULL,'json',NULL);
/*!40000 ALTER TABLE `config_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_aggr`
--

DROP TABLE IF EXISTS `config_info_aggr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_aggr` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfoaggr_datagrouptenantdatum` (`data_id`,`group_id`,`tenant_id`,`datum_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='增加租户字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_aggr`
--

LOCK TABLES `config_info_aggr` WRITE;
/*!40000 ALTER TABLE `config_info_aggr` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_aggr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_beta`
--

DROP TABLE IF EXISTS `config_info_beta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_beta` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfobeta_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info_beta';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_beta`
--

LOCK TABLES `config_info_beta` WRITE;
/*!40000 ALTER TABLE `config_info_beta` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_beta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_info_tag`
--

DROP TABLE IF EXISTS `config_info_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_info_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfotag_datagrouptenanttag` (`data_id`,`group_id`,`tenant_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info_tag';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_info_tag`
--

LOCK TABLES `config_info_tag` WRITE;
/*!40000 ALTER TABLE `config_info_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_info_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_tags_relation`
--

DROP TABLE IF EXISTS `config_tags_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_tags_relation` (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`),
  UNIQUE KEY `uk_configtagrelation_configidtag` (`id`,`tag_name`,`tag_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_tag_relation';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_tags_relation`
--

LOCK TABLES `config_tags_relation` WRITE;
/*!40000 ALTER TABLE `config_tags_relation` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_tags_relation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `group_capacity`
--

DROP TABLE IF EXISTS `group_capacity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='集群、各Group容量信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `group_capacity`
--

LOCK TABLES `group_capacity` WRITE;
/*!40000 ALTER TABLE `group_capacity` DISABLE KEYS */;
/*!40000 ALTER TABLE `group_capacity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `his_config_info`
--

DROP TABLE IF EXISTS `his_config_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `his_config_info` (
  `id` bigint unsigned NOT NULL,
  `nid` bigint unsigned NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin,
  `src_ip` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`nid`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_gmt_modified` (`gmt_modified`),
  KEY `idx_did` (`data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=566 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='多租户改造';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `his_config_info`
--

LOCK TABLES `his_config_info` WRITE;
/*!40000 ALTER TABLE `his_config_info` DISABLE KEYS */;
INSERT INTO `his_config_info` VALUES (7,562,'medusa.service.process.entrance.appsetting.json','process','','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Error\"\n    }\n  },\n  \"AllowedHosts\": \"*\",\n  \"InitDatabase\": false,\n  \"FormDataPeriod\": 2,\n  \"ViewTokenExpireHours\": 99999,\n  \"LimitStartInstantsNumber\": 99999,\n  \"LimitApprovInstantsNumnber\": 99999,\n  \"CurrentRuntimeEnvironment\": \"PRD\",\n  \"Persistence\": {\n    \"Process\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\n    },\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\n    },\n    \"EventBus\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM_EngineEventBus;uid=bpm;Pwd=*****************"\n    },\n    \"Engine\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Engine;uid=bpm;Pwd=*****************"\n    },\n    \"EngineCache\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-EngineCache;uid=bpm;Pwd=*****************"\n    },\n    \"Todo\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-ToDoCenter;uid=bpm;Pwd=*****************"\n    },\n    \"Report\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Report;uid=bpm;Pwd=*****************"\n    },\n    \"LogCenter\": {\n        \"DbType\": \"Mysql\",\n        \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    },\n     \"StructuredStorage\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=bpm-structuredstorage;uid=bpm;Pwd=*****************"\n    }  \n  },\n  \"LogSettings\": {\n    \"QueryDbType\": \"MySql\",\n    \"MinimumLevel\": 3,\n    \"DBName\": \"LogCenter\",\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n    \"OperationLog\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"PlatformSettings\": {\n    \"UserRoles\": \"/platform/v1/users/{id}/roles\",\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\",\n    \"UserDataAuthorities\": \"/platform/v1/manage/data-authoritys-by-user\",\n    \"UserDataAuthoritiesGroupByRole\": \"/platform/v1/manage/data-authoritys-by-user-groupby-role\",\n    \"CurrentUserAuthorityOrganization\": \"/platform/v1/manage/data-authoritys-organization\",\n    \"UsersImpersonateUrl\": \"/platform/v1/users/{account}/impersonate-url\"\n  },\n  \"FormSettings\": {\n    \"Apis\": {\n      \"ProcessFormRelation\": \"/form/v1/manage/processes/{id}/forms\",\n      \"FormBatchPublish\": \"/form/v1/manage/forms/batch-publish\",\n      \"FormState\": \"/form/v1/manage/form-states\",\n      \"Form\": \"/form/v1/manage/form\",\n      \"FormDesigns\": \"/form/v1/manage/forms\",\n      \"FormSchema\": \"/form/v1/templates/{id}/form-schema\",\n      \"FormEnginePublish\": \"/form/v1/engine-endpoints/instance-start\",\n      \"FormVersions\": \"/form/v1/manage/form-versions\"\n    }\n  },\n  \"ProcessSettings\": {\n    \"BpmnPageUrl\": \"http://*************:32002/process/bpmn-view\",\n    \"ExcelProcess\": {\n      \"NodeEndFlag\": \"\",\n      \"ABRoleSuffix\": \"B\",\n      \"CustomFlag\":\"&\"\n    },\n    \"AuthorizedBusinessTypes\": \"/process/v1/tree-business-types-authority?params=%7B%7D\",\n    \"ProcessWebUrl\": \"https://bpm.chinahho.cn:2005/customer/start\",\n    \"ProcessMobileUrl\": \"https://bpm.chinahho.cn:2005/mobile/start\"\n  },\n  \"EngineSettings\": {\n    \"ProcessMainParamsApi\": \"/engine/v1/process-instance-main-params\",\n    \"InstanceJson\": \"/engine/v1/instances/{id}/json\",\n    \"InstanceWholeJson\": \"/engine/v1/instances/{id}/whole-json\",\n    \"StartApi\": \"/engine/v1/start\",\n    \"OperateApi\": \"/engine/v1/tasks\",\n    \"BatchOperateApi\": \"/engine/v1/batch-tasks\",\n    \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n    \"TasksStateApi\": \"/engine/v1/tasks/status\",\n    \"UserInstacesApi\": \"/engine/v1/user-instances\",\n    \"UserInstaceGroupsApi\": \"/engine/v1/user-instance-groups\",\n    \"UserErrorInstacesApi\": \"/engine/v1/user-error-instances\",\n    \"UserErrorInstaceGroupsApi\": \"/engine/v1/user-error-instance-groups\",\n    \"UserStateOfInstaceApi\": \"/engine/v1/instances/{instance-number}/user-state\",\n    \"InstanceBaseInfoApi\": \"/engine/v1/instances/{instance-number}/info\",\n    \"InstanceRelationsApi\": \"/engine/v1/instances/{instance-number}/relations\",\n    \"InstanceAttachmentsApi\": \"/engine/v1/instances/{instance-number}/attachments\",\n    \"InstanceRecordsApi\": \"/engine/v1/instances/{instance-number}/records\",\n    \"InstanceFormParamsApi\": \"/engine/v1/instances/{instance-number}/form-params\",\n    \"RelationInstancesApi\": \"/engine/v1/relation-instances\",\n    \"InstanceStepsApi\": \"/engine/v1/instances/{instance-number}/steps\",\n    \"InstanceRejectStepsApi\": \"/engine/v1/instances/{instance-number}/reject-steps/{task-id}/task\",\n    \"NoticeApi\": \"/engine/v1/notice\",\n    \"UserMenuNumbersApi\": \"/engine/v1/menu-numbers\",\n    \"MaintainenceInstancesApi\": \"/engine/v1/data-authority-maintenence-instances\",\n    \"OperateNodeApi\": \"/engine/v1/instances/{number}/nodes\",\n    \"GetInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"SaveInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"InstanceRecallApi\": \"/engine/v1/instance-recall\",\n    \"InstanceResumeApi\": \"/engine/v1/resume\",\n    \"InstanceLinearActivitiesApi\": \"/engine/v1/instances/{instance-number}/line-activities\",\n    \"AnalysisInstanceApi\": \"/engine/v1/analysis-instances\",\n    \"AnalysisNodeApi\": \"/engine/v1/analysis-nodes\",\n    \"AnalysisProcessEfficiencyApi\": \"/engine/v1/analysis-processes/{id}/efficiency\",\n    \"InstanceCancelApi\": \"/engine/v1/instance-cancel\",\n    \"InstanceBatchCancelApi\": \"/engine/v1/batch-instance-cancel\",\n    \"InstanceBatchNoticeApi\": \"/engine/v1/batch-notice\",\n    \"AddCommentsApi\": \"/engine/v1/instances/{number}/comment\",\n    \"ProcessStepsApi\": \"/engine/v1/process-steps\",\n    \"TaskStepInfoApi\": \"/engine/v1/tasks/{id}/step-info\",\n    \"TaskFixedByUserApi\": \"/engine/v1/tasks/fixed-by-user/{user-id}\",\n    \"TaskFixedBatchReplaceApi\": \"/engine/v1/tasks/fixed-batch-replace\",\n    \"RoleTaskFixedBatchReplaceApi\": \"/engine/v1/role-tasks/fixed-batch-replace\",\n    \"InstanceStatus\": \"/engine/v1/instances/instance-status\",\n    \"TaskByUsuerApi\": \"/engine/v1/tasks/tasks-by-user/{user-id}\",\n    \"BatchApproveApi\": \"/engine/v1/activity-resolvers/batch-approve\",\n    \"InstanceAuthIdApi\": \"/engine/v1/instances/{id}/auth-id\",\n    \"InstanceCustomTopic\": \"/engine/v1/instances/{id}/custom-topic\",\n    \"InstanceStatusApi\": \"/engine/v1/instances/{id}/instance-status\",\n    \"RecallTaskApi\": \"/engine/v1/tasks/{id}/recall-task\",\n    \"RecallExtraTaskApi\": \"/engine/v1/tasks/{id}/recall-extra-task\",\n    \"InstancePars\": \"/engine/v1/instances/{id}/instance-pars\",\n    \"ParallelStatusApi\": \"/engine/v1/tasks/{id}/parallel-status\",\n    \"CheckIsCanStartApi\": \"/engine/v1/instances/check-is-can-start?bsid={bsid}&btid={btid}&boid={boid}\",\n    \"ParallelInfoApi\": \"/engine/v1/tasks/{id}/parallel-info\",\n    \"CheckIsOwnerUserApi\": \"/engine/v1/instances/check-is-owner-user?number={number}&userId={userId}\",\n    \"TaskRecallState\": \"/engine/v1/tasks/{id}/recall-state\",\n    \"TaskDelayState\": \"/engine/v1/tasks/{id}/delay-state\",\n    \"DelayTaskApi\": \"/engine/v1/tasks/{id}/delay-task\",\n    \"UpdateInstanceCallBackStatusApi\":\"/engine/v1/instances/{instanceId}/instance-Call-Back-Status\",\n    \"InstanceBatchDelApi\": \"/engine/v1/batch-del\",\n    \"MachineNodeTaskApi\": \"/engine/v1/machine-node-task/{id}\"\n  },\n  \"ApiGateway\": {\n    \"Host\": \"*************\",\n    \"Port\": \"32000\",\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n    \"Timeout\": \"30\"\n  },\n  \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"30\",\n    \"IsHttps\": false\n  },\n  \"EncryptKeys\": {\n    \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n  },\n  \"TodoCentreSettings\": {\n    \"UserToDoTasksApi\": \"/todo-centre/v1/tasks\",\n    \"UserToDoTaskGroupsApi\": \"/todo-centre/v1/task-groups\",\n    \"UserDoneTasksApi\": \"/todo-centre/v1/archived-tasks\",\n    \"UserDoneTaskGroupsApi\": \"/todo-centre/v1/archived-task-groups\",\n    \"UserMenuNumbersApi\": \"/todo-centre/v1/tasks/menu-numbers\",\n    \"UserDockingTasksApi\": \"/todo-centre/v1/docking-tasks\",\n    \"UserDockingTasksUrgingApi\": \"/todo-centre/v1/docking-tasks/{id}/urging\",\n    \"RelationInstancesApi\": \"/todo-centre/v1/tasks/relation-instances\",\n    \"UserInstacesApi\": \"/todo-centre/v1/tasks/user-instances\",\n    \"UserInstaceGroupsApi\": \"/todo-centre/v1/tasks/user-instance-groups\",\n    \"UpdateTaskProcesingApi\": \"/todo-centre/v1/taskProcesing/{id}\",\n    \"BatchUpdateTaskProcesingApi\": \"/todo-centre/v1/batchTaskProcesing\",\n    \"BatchUpdateTaskProcesingByInstanceNumberApi\": \"/todo-centre/v1/taskProcesingByInstanceNumber/{id}\",\n    \"GlobalRelationApi\": \"/todo-centre/v1/global/relation\",\n    \"BatchDelApi\": \"/todo-centre/v1/global/batchDelete\",\n    \"TaskUpdateStatus\": \"/todo-centre/v1/task/updateStatus\"\n  },\n  \"TodoPushSettings\": {\n      \"PushType\": \"\",\n      \"PushCenterSiteUrl\": \"https://bpm.chinahho.cn:2005/customer\",\n      \"PushCenterMobileSiteUrl\": \"https://bpm.chinahho.cn:2005/mobile\",\n      \"PushSystemCode\": \"BPM\",\n      \"RabbitMQ\": {\n          \"VirtualHost\": \"/\",\n          \"ExchangeName\": \"\",\n          \"HostName\": \"*************\",\n          \"Port\": 5672,\n          \"UserName\": \"bpm\",\n          \"Password\": \"1nsc72AZBxNFLtw4\"\n      },\n      \"MyStart\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/myStart/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"AddDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"DeleteDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchDelete\",\n          \"Queue\": \"othermessage\"\n      },\n      \"UpdateTodo\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/task/batchUpdate\",\n          \"Queue\": \"todomessage\"\n      }\n  },\n  \"FileCentreSettings\": {\n    \"ServerType\": \"local\",\n        \"Upload\": \"http://*************:32057/v1/document-services/upload/{serverType}\",\n        \"Download\": \"/customer/api/file-centre/v1/document-services/download/{serverType}\",\n        \"DownloadGW\": \"http://*************:32057/v1/document-services/download/{serverType}\",\n        \"PreviewServerType\": \"wps\",\n        \"Preview\": \"/api/file-centre/v1/document-services/view/{serverType}\",\n        \"DownloadList\": \"http://*************:32000/api/file-centre/v1/document-services/downloads/{serverType}\"\n  },\n  \"Transport\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\"\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000,\n    \"DefaultRetryDelaysInSeconds\": \"60,3600,21600\",\n    \"QueueName\": \"bpm-job-queue\"\n  },\n  \"BPM\": {\n    \"Url\": \"https://bpm.chinahho.cn:2005\",\n    \"ImpersonatePath\": \"/platform/auth/impersonate?token\",\n    \"IndependentPath\": \"/customer/done/{id}\",\n    \"StartPath\": \"/customer/start?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"ReStartPath\": \"/customer/start/{instance-number}?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"TokenTimeout\": 10080\n  },\n  \"AttachmentsPath\": \"/var/www/uploads\",\n  \"EventBusSettings\": {\n    \"Redis\": {\n      \"Host\": \"*************:6380,abortConnect=false,connectTimeout=60000\",\n      \"DB\": 1,\n      \"CacheDb\":7,\n      \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"RabbitMQ\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\", \n      \"InterfaceExchange\": \"bpm4.engine.fanout\",\n      \"IsPublishDebugMessage\":true\n    }\n  },\n  \"IsEnableToDoCenter\": true,\n    \"WebOffice\": {\n    \"ExternalDomain\": \"https://wpszt.chinahho.cn:12006\",\n    \"InternalDomain\": \"http://*************\",\n    \"GetLinkApi\": \"/open/api/preview/v1/files/{file_id}/link\",\n    \"GetEditLinkApi\": \"/open/api/edit/v1/files/{file_id}/link\",\n    \"GetWrapheaderApi\": \"/open/api/cps/sync/v1/wrapheader\",\n    \"GetContentOperateApi\": \"/open/api/cps/sync/v1/content/operate\",\n    \"GetDownloadApi\": \"/open/api/cps/v1/download/{download_id}\",\n    \"AccessKey\": \"JFVYGLQPWEUSUKPP\",\n    \"SecretKey\": \"SKbgbxempheppfmq\",\n      \"FileTypeW\": \"doc;docx\",\n    \"FileTypeS\": \"xls;xlsx\",\n    \"FileTypeP\": \"ppt;pptx\",\n    \"FileTypeF\": \"pdf\",\n    \"FileTypeX\": \"png;jpg;jpeg;gif;bmp;svg;zip;rar;tar;7z;gz\",\n    \"EditRoleCode\":  \"GWGLY\"\n  },\n  \"ContractLockSettings\": {\n    \"ContractLockApi\": \"http://*************:9182\",\n    \"Accesstoken\": \"UYAIS4vIIB\",\n    \"Secret\": \"SmbMDoM1BTifTtxTNoJ8tEEUyomzIn\",\n    \"ReceiverName\": \"测试账号\",\n    \"Contact\": \"13328033090\",\n    \"CategoryId\": \"2916235861576192065\",\n    \"TenantType\": \"COMPANY\",\n    \"TenantName\": \"中国水务投资集团有限公司\",\n    \"SealId\": 2906084454476009538,\n    \"SealReplaceUrl\": {\n      \"Before\": \"http://127.0.0.1:9181\",\n      \"After\": \"http://*************:12003\"\n    },\n    \"OpenTestUser\": \"0\"\n  },\n  \"FddTaskSettings\": {\n    \"FddAppID\": \"80001107\",\n    \"FddAppSecret\": \"FFJ2FVUUQOXWFGK4GKPPRRFF74CS4KTF\",\n    \"FddOpenCorpId\": \"3b66c3fa92d84d4e9ba84b58766ba003\",\n    \"FddServerUrl\": \"https://uat-api.fadada.com/api/v5/\",\n    \"FddActorName\": \"盟拓软件（苏州）有限公司\",\n    \"FddCorpMembers\": \"1780890618366214144\",\n    \"FddIsOnline\": \"0\"\n  },\n  \"IsEnableCancelNumber\": false,\n  \"matrixAccessToken\": {\n    \"enable\": true,\n    \"tokenExpire\": \"100\",\n    \"serverExpire\": \"100\",\n    \"whiteList\" : {\n      \"^/endpoints/business-data$\": [\"Post\"],\n      \"^/endpoints/[^/]+/cancel$\": [\"Put\"],\n      \"^/endpoints/[^/]+/recall$\": [\"Put\"],\n      \"^/endpoints/[^/]+/urging$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/done$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/reject$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/notice$\": [\"Post\"],\n      \"^/endpoints/tasks/[^/]+/extra-task$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/handover$\": [\"Put\"],\n      \"^/endpoints/process-steps$\": [\"Post\"],\n      \"^/v1/instances/[^/]+/steps$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/info$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/user/info$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/rename$\": [\"Put\"],\n      \"^/v1/webOffice/v1/3rd/file/history$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/version/[^/]+$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/online$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/save$\": [\"Post\"],\n      \"^/v1/contractlock/contractcallback$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack2$\": [\"Post\"],\n      \"^/v1/documents/upload$\": [\"Post\"],\n      \"^/v1/documents/upload/gw$\": [\"Post\"],\n      \"^/v1/documents/upload/gwmb$\": [\"Post\"],\n      \"^/v1/hr/import-annual-leave$\": [\"Post\"]\n    }\n  }\n}','67f341da3b50f42b0f72a75580f371a9','2010-05-05 00:00:00','2025-07-11 15:31:17',NULL,'*************','U','shuiwu'),(156,563,'medusa.service.sync.appsetting.json','sync','','{\r\n	\"Persistence\": {\r\n		\"Boost\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=*************;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom2\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		},\r\n		\"SourceFrom3\": {\r\n			\"DbType\": \"MySql\",\r\n			\"ConnectionString\": \"server=xxx;uid=bpm;pwd=****************;database=Boost\"\r\n		}\r\n	},\r\n	\"Logging\": {\r\n		\"LogLevel\": {\r\n			\"Default\": \"Warning\"\r\n		}\r\n	},\r\n	\"LogSettings\": {\r\n		\"MongoDBContext\": \"mongodb://172.20.138.48:27017\"\r\n	},\r\n	\"SystemName\": \"ShuiWu\",\r\n	\"SystemSettings\": {\r\n		\"LianTou\": {},\r\n		\"SaiWu\": {},\r\n		\"MeiTuan\": {},\r\n		\"ShuiWu\": {\r\n			\"GetTokenUrl\": \"https://portal.chinahho.cn:2006/token/signLogin\",\r\n			\"TokenClientId\": \"bpm\",\r\n			\"TokenCode\": \"xiajingjun\",\r\n			\"GetOrganizationsUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/office-list\",\r\n			\"GetPositionsUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/post-list\",\r\n			\"GetUsersUrl\": \"https://portal.chinahho.cn:2006/sync/open-api/user-list\",\r\n			\"DefaultDomainId\": \"f7d23c73-78cb-4387-b16d-0a76871bc806\",\r\n			\"DefaultDomainName\": \"中国水务\",\r\n			\"DefaultDomainLevelId\": \"de5f3287-4c58-4a64-b16e-6493c61e85ea\",\r\n			\"DefaultDomainLevelName\": \"部门\",\r\n			\"DefaultDomainLevelCode\": \"Deapartment\",\r\n			\"DefaultWeight\": \"100\",\r\n			\"DefaultPassword\": \"wGii546mdvsW3JYqds+0cJxK/NU=,123456\"\r\n		}\r\n	}\r\n}','16cc82b8755df1c86454bca801a378ce','2010-05-05 00:00:00','2025-07-23 03:20:08',NULL,'*************','U','shuiwu'),(1,564,'medusa.service.platform.entrance.appsetting.json','platform','','{\n    \"Logging\": {\n        \"LogLevel\": {\n            \"Default\": \"Debug\",\n            \"System\": \"Information\",\n            \"Microsoft\": \"Information\"\n        }\n    },\n    \"InitDatabase\": false,\n    \"LdapSettings\": {\n        \"Enable\": false,\n        \"UseSSL\": false,\n        \"ServerName\": \"movit-tech.com\",\n        \"ServerPort\": 389\n    },\n    \"Persistence\": {\n        \"Boost\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"     \n        },\n        \"Process\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\n        },         \n        \"CronJob\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=hangfire;uid=bpm;Pwd=*****************" \n        },\n        \"Log\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n        },\n        \"LowCode\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LowCode;uid=bpm;Pwd=*****************"\n        }          \n    },\n    \"WebsocketExchange\": \"boards.refresh\",\n    \"Redis\": {\n        \"Host\": \"*************:6380,syncTimeout=5000,ConnectTimeout=5000\",\n        \"Db\": 1,\n        \"CacheDb\":7,\n        \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"GatewayService\": {\n        \"Db\": 8,\n        \"Key\": \"_routes\"\n    },\n    \"AttachmentsPath\": \"/var/www/uploads\",\n    \"EngineSettings\": {\n        \"StartApi\": \"/engine/v1/start\",\n        \"OperateApi\": \"/engine/v1/tasks\",\n        \"BatchOperateApi\": \"/engine/v1/batch-tasks\",\n        \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n        \"UserInstacesApi\": \"/engine/v1/user-instances\",\n        \"UserInstaceGroupsApi\": \"/engine/v1/user-instance-groups\",\n        \"UserErrorInstacesApi\": \"/engine/v1/user-error-instances\",\n        \"UserErrorInstaceGroupsApi\": \"/engine/v1/user-error-instance-groups\",\n        \"UserStateOfInstaceApi\": \"/engine/v1/instances/{instance-number}/user-state\",\n        \"InstanceBaseInfoApi\": \"/engine/v1/instances/{instance-number}/info\",\n        \"InstanceRelationsApi\": \"/engine/v1/instances/{instance-number}/relations\",\n        \"InstanceAttachmentsApi\": \"/engine/v1/instances/{instance-number}/attachments\",\n        \"InstanceRecordsApi\": \"/engine/v1/instances/{instance-number}/records\",\n        \"InstanceFormParamsApi\": \"/engine/v1/instances/{instance-number}/form-params\",\n        \"RelationInstancesApi\": \"/engine/v1/relation-instances\",\n        \"InstanceStepsApi\": \"/engine/v1/instances/{instance-number}/steps\",\n        \"InstanceRejectStepsApi\": \"/engine/v1/instances/{instance-number}/activities\",\n        \"NoticeApi\": \"/engine/v1/notice\",\n        \"UserMenuNumbersApi\": \"/engine/v1/menu-numbers\",\n        \"MaintainenceInstancesApi\": \"/engine/v1/maintenence-instances\",\n        \"OperateNodeApi\": \"/engine/v1/instances/{number}/nodes\",\n        \"GetInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n        \"SaveInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n        \"InstanceRecallApi\": \"/engine/v1/instance-recall\",\n        \"InstanceResumeApi\": \"/engine/v1/resume\",\n        \"InstanceLinearActivitiesApi\": \"/engine/v1/instances/{instance-number}/line-activities\",\n        \"AnalysisInstanceApi\": \"/engine/v1/analysis-instances\",\n        \"AnalysisNodeApi\": \"/engine/v1/analysis-nodes\",\n        \"AnalysisProcessEfficiencyApi\": \"/engine/v1/analysis-processes/{id}/efficiency\",\n        \"InstanceCancelApi\": \"/engine/v1/instance-cancel\",\n        \"AddCommentsApi\": \"/engine/v1/instances/{number}/comment\",\n        \"ProcessStepsApi\": \"/engine/v1/process-steps\"\n    },\n    \"TodoCentreSettings\": {\n        \"UserToDoTasksApi\": \"/todo-centre/v1/tasks\",\n        \"UserToDoTaskGroupsApi\": \"/todo-centre/v1/task-groups\",\n        \"UserDoneTasksApi\": \"/todo-centre/v1/archived-tasks\",\n        \"UserDoneTaskGroupsApi\": \"/todo-centre/v1/archived-task-groups\",\n        \"UserMenuNumbersApi\": \"/todo-centre/v1/tasks/menu-numbers\",\n        \"UserDockingTasksApi\": \"/todo-centre/v1/docking-tasks\",\n        \"UserDockingTasksUrgingApi\": \"/todo-centre/v1/docking-tasks/{id}/urging\"\n    },\n    \"ProcessSettings\": {\n        \"AgentUsersApi\": \"/process/v1/agent-users\",\n        \"ApiTablesApi\": \"/process/v1/manage/api-tables\",\n        \"UpdateAuthMemberOrgpathApi\": \"/process/v1/manage/process-auth/update-auth-path\"\n    },\n    \"LogSettings\": {\n        \"QueryDbType\": \"MySql\",\n        \"MinimumLevel\": 3,\n        \"DBName\": \"LogCenter\",\n        \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n        \"Logger\": {\n            \"DotNetCore.CAP\": \"DotNetCore.CAP\",\n            \"MT.Enterprise.Core\": \"MT.Enterprise.Core\",\n            \"Medusa.Engine\": \"Medusa.Engine\",\n            \"Medusa.Service.Platform\": \"Medusa.Service.Platform\",\n            \"Medusa.Service.Form\": \"Medusa.Service.Form\",\n            \"Medusa.Service.Process\": \"Medusa.Service.Process\",\n            \"Medusa.Service.ToDo_Center\": \"Medusa.Service.ToDo_Center\",\n            \"Medusa.Service.Biz_Logic\": \"Medusa.Service.Biz_Logic\",\n            \"Other\": \"Other\"\n        },\n        \"OperationLog\": {\n            \"DbType\": \"Mysql\",\n            \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n        }\n    },\n    \"EncryptKeys\": {\n        \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n    },\n    \"ApiGateway\": {\n        \"Host\": \"*************\",\n        \"Port\": \"32000\",\n        \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n        \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n        \"Timeout\": \"30\"\n    },\n    \"ApiRequest\": {\n        \"Host\": \"*************:6380\",\n        \"DB\": 8,\n        \"Password\": \"87htd2NxIQ0YguE9\",\n        \"Timeout\": 600,\n        \"IsHttps\": false\n    },    \n    \"BPM\": {\n        \"Url\": \"https://bpm.chinahho.cn:2005/todo-center\",\n        \"ImpersonatePath\": \"/auth/impersonate?movitech_token\",\n        \"IndependentPath\": \"/independent/done/{id}\",\n        \"TokenTimeout\": 10080,\n        \"MobileUrl\": \"https://bpm.chinahho.cn:2005/mobile\"\n    },\n    \"MDM\": {\n        \"RefreshCacheApi\": \"/platform/v1/manage/refresh-org-cache\",\n        \"RootOrgCode\": \"Movitech\",\n        \"RootOrgName\": \"盟拓软件(苏州)有限公司\",\n        \"AppKey\": \"de\",\n        \"UserApi\": \"http://*************/api/inner/datapub/rest/api/v1/hcm/user\",\n        \"OrgApi\": \"http://*************/api/inner/datapub/rest/api/v1/hcm/org\"\n    },\n    \"MobileSettings\": {\n        \"ListUrl\": \"http://WXAPPROVETEST.NIPPONPAINT.COM.CN/WeiXin/Index2018.aspx\"\n    },\n    \"IDocV\": {\n        \"IDocVUploadUri\": \"http://************/doc/upload\",\n        \"IDocVViewUri\": \"http://************/view\",\n        \"IDocVToken\": \"testtoken\"\n    },\n    \"CronJob\": {\n        \"Host\": \"http://*************\",\n        \"Port\": \"32004\",\n        \"Uri\": \"/job\",\n        \"BasicUserName\": \"bpm\",\n        \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n        \"DefaultTimeOut\": 180000\n    },\n    \"MessageSetting\": {\n        \"Email\": {\n            \"Host\": \"smtp.movit-tech.com\",\n            \"User\": \"<EMAIL>\",\n            \"Password\": \"\",\n            \"From\": \"<EMAIL>\",\n            \"Port\": \"25\",\n            \"TryTimes\": \"3\",\n            \"AllowedEmail\":\"<EMAIL>;<EMAIL>\"\n        },\n        \"ShortMessage\": {\n            \"Host\": \"https://api.wy17173.com:8443/sms/api/sendMessage\",\n            \"UserName\": \"910020\",\n            \"Password\": \"kTouD3mvz3I1\"\n        },\n        \"WeChat\": {\n            \n        },\n        \"DingTalk\": {\n            \"ApiUrl\": \"https://oapi.dingtalk.com\",\n            \"AppKey\": \"\",\n            \"AppSecret\": \"zVg9dJvcMEkkhN8XyK4WcW_kph_iXevekRFJOToG_P6wWd4qqukIF-RJmoeUo4R_\",\n            \"Agent_Id\": \"1641511600\",\n            \"ToUsersTbField\": \"WorkNumber\",\n            \"AllowedUserIds\": \"74f63559-7190-42a8-a71e-b6f16be08e86;0b42f88e-3fd4-4c0b-8174-36afbc25bcdc\",\n            \"TryTimes\": 3\n        },\n        \"Portal\": {\n            \"Host\": \"http://*************\",\n            \"TryTimes\": \"2\",\n            \"BizType\": \"bpm\",\n            \"BizKey\": \"bpm\",\n            \"Types\": \"pc\"\n        }\n    },\n    \"matrixAccessToken\": {\n        \"enable\": true,\n        \"tokenExpire\": \"100\",\n        \"serverExpire\": \"100\",\n        \"whiteList\" : {\n            \"^/v1/impersonate$\": [\"Post\"],\n            \"^/v1/impersonate-out$\": [\"Post\"],\n            \"^/v1/customer/menus$\": [\"Get\"],\n            \"^/v1/mobile/menus$\": [\"Get\"],\n            \"^/v1/messages/[^/]+$\": [\"Post\"],\n            \"^/v1/product-license/[^/]+$\": [\"Get\"],\n            \"^/v1/users/[^/]+/mobile-impersonate-url$\": [\"Get\"]\n        }\n    },\n    \"FeishuSettings\": {\n        \"AppId\": \"cli_a077a343d1f8500d\",\n        \"AppSecret\": \"\",\n        \"TenantTokenUrl\": \"https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal\",\n        \"BatchGetIdUrl\": \"https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id\"\n    }   \n}','519c2580fd6aaeb6b28e0e3660ea7a06','2010-05-05 00:00:00','2025-07-23 03:25:35',NULL,'*************','U','shuiwu'),(7,565,'medusa.service.process.entrance.appsetting.json','process','','{\n  \"Logging\": {\n    \"LogLevel\": {\n      \"Default\": \"Error\"\n    }\n  },\n  \"AllowedHosts\": \"*\",\n  \"InitDatabase\": false,\n  \"FormDataPeriod\": 30,\n  \"ViewTokenExpireHours\": 99999,\n  \"LimitStartInstantsNumber\": 99999,\n  \"LimitApprovInstantsNumnber\": 99999,\n  \"CurrentRuntimeEnvironment\": \"PRD\",\n  \"Persistence\": {\n    \"Process\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Process;uid=bpm;Pwd=*****************"\n    },\n    \"Boost\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=Boost;uid=bpm;Pwd=*****************"\n    },\n    \"EventBus\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM_EngineEventBus;uid=bpm;Pwd=*****************"\n    },\n    \"Engine\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Engine;uid=bpm;Pwd=*****************"\n    },\n    \"EngineCache\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-EngineCache;uid=bpm;Pwd=*****************"\n    },\n    \"Todo\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-ToDoCenter;uid=bpm;Pwd=*****************"\n    },\n    \"Report\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=BPM-Report;uid=bpm;Pwd=*****************"\n    },\n    \"LogCenter\": {\n        \"DbType\": \"Mysql\",\n        \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    },\n     \"StructuredStorage\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=bpm-structuredstorage;uid=bpm;Pwd=*****************"\n    }  \n  },\n  \"LogSettings\": {\n    \"QueryDbType\": \"MySql\",\n    \"MinimumLevel\": 3,\n    \"DBName\": \"LogCenter\",\n    \"LogAddress\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************",\n    \"OperationLog\": {\n      \"DbType\": \"Mysql\",\n      \"ConnectionString\": \"server=*************;Database=LogCenter;uid=bpm;Pwd=*****************"\n    }\n  },\n  \"PlatformSettings\": {\n    \"UserRoles\": \"/platform/v1/users/{id}/roles\",\n    \"ProductLicense\": \"/platform/v1/product-license/{id}\",\n    \"UserDataAuthorities\": \"/platform/v1/manage/data-authoritys-by-user\",\n    \"UserDataAuthoritiesGroupByRole\": \"/platform/v1/manage/data-authoritys-by-user-groupby-role\",\n    \"CurrentUserAuthorityOrganization\": \"/platform/v1/manage/data-authoritys-organization\",\n    \"UsersImpersonateUrl\": \"/platform/v1/users/{account}/impersonate-url\"\n  },\n  \"FormSettings\": {\n    \"Apis\": {\n      \"ProcessFormRelation\": \"/form/v1/manage/processes/{id}/forms\",\n      \"FormBatchPublish\": \"/form/v1/manage/forms/batch-publish\",\n      \"FormState\": \"/form/v1/manage/form-states\",\n      \"Form\": \"/form/v1/manage/form\",\n      \"FormDesigns\": \"/form/v1/manage/forms\",\n      \"FormSchema\": \"/form/v1/templates/{id}/form-schema\",\n      \"FormEnginePublish\": \"/form/v1/engine-endpoints/instance-start\",\n      \"FormVersions\": \"/form/v1/manage/form-versions\"\n    }\n  },\n  \"ProcessSettings\": {\n    \"BpmnPageUrl\": \"http://*************:32002/process/bpmn-view\",\n    \"ExcelProcess\": {\n      \"NodeEndFlag\": \"\",\n      \"ABRoleSuffix\": \"B\",\n      \"CustomFlag\":\"&\"\n    },\n    \"AuthorizedBusinessTypes\": \"/process/v1/tree-business-types-authority?params=%7B%7D\",\n    \"ProcessWebUrl\": \"https://bpm.chinahho.cn:2005/customer/start\",\n    \"ProcessMobileUrl\": \"https://bpm.chinahho.cn:2005/mobile/start\"\n  },\n  \"EngineSettings\": {\n    \"ProcessMainParamsApi\": \"/engine/v1/process-instance-main-params\",\n    \"InstanceJson\": \"/engine/v1/instances/{id}/json\",\n    \"InstanceWholeJson\": \"/engine/v1/instances/{id}/whole-json\",\n    \"StartApi\": \"/engine/v1/start\",\n    \"OperateApi\": \"/engine/v1/tasks\",\n    \"BatchOperateApi\": \"/engine/v1/batch-tasks\",\n    \"TaskState\": \"/engine/v1/tasks/{id}/state\",\n    \"TasksStateApi\": \"/engine/v1/tasks/status\",\n    \"UserInstacesApi\": \"/engine/v1/user-instances\",\n    \"UserInstaceGroupsApi\": \"/engine/v1/user-instance-groups\",\n    \"UserErrorInstacesApi\": \"/engine/v1/user-error-instances\",\n    \"UserErrorInstaceGroupsApi\": \"/engine/v1/user-error-instance-groups\",\n    \"UserStateOfInstaceApi\": \"/engine/v1/instances/{instance-number}/user-state\",\n    \"InstanceBaseInfoApi\": \"/engine/v1/instances/{instance-number}/info\",\n    \"InstanceRelationsApi\": \"/engine/v1/instances/{instance-number}/relations\",\n    \"InstanceAttachmentsApi\": \"/engine/v1/instances/{instance-number}/attachments\",\n    \"InstanceRecordsApi\": \"/engine/v1/instances/{instance-number}/records\",\n    \"InstanceFormParamsApi\": \"/engine/v1/instances/{instance-number}/form-params\",\n    \"RelationInstancesApi\": \"/engine/v1/relation-instances\",\n    \"InstanceStepsApi\": \"/engine/v1/instances/{instance-number}/steps\",\n    \"InstanceRejectStepsApi\": \"/engine/v1/instances/{instance-number}/reject-steps/{task-id}/task\",\n    \"NoticeApi\": \"/engine/v1/notice\",\n    \"UserMenuNumbersApi\": \"/engine/v1/menu-numbers\",\n    \"MaintainenceInstancesApi\": \"/engine/v1/data-authority-maintenence-instances\",\n    \"OperateNodeApi\": \"/engine/v1/instances/{number}/nodes\",\n    \"GetInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"SaveInstanceDataApi\": \"/engine/v1/instances/{number}/data\",\n    \"InstanceRecallApi\": \"/engine/v1/instance-recall\",\n    \"InstanceResumeApi\": \"/engine/v1/resume\",\n    \"InstanceLinearActivitiesApi\": \"/engine/v1/instances/{instance-number}/line-activities\",\n    \"AnalysisInstanceApi\": \"/engine/v1/analysis-instances\",\n    \"AnalysisNodeApi\": \"/engine/v1/analysis-nodes\",\n    \"AnalysisProcessEfficiencyApi\": \"/engine/v1/analysis-processes/{id}/efficiency\",\n    \"InstanceCancelApi\": \"/engine/v1/instance-cancel\",\n    \"InstanceBatchCancelApi\": \"/engine/v1/batch-instance-cancel\",\n    \"InstanceBatchNoticeApi\": \"/engine/v1/batch-notice\",\n    \"AddCommentsApi\": \"/engine/v1/instances/{number}/comment\",\n    \"ProcessStepsApi\": \"/engine/v1/process-steps\",\n    \"TaskStepInfoApi\": \"/engine/v1/tasks/{id}/step-info\",\n    \"TaskFixedByUserApi\": \"/engine/v1/tasks/fixed-by-user/{user-id}\",\n    \"TaskFixedBatchReplaceApi\": \"/engine/v1/tasks/fixed-batch-replace\",\n    \"RoleTaskFixedBatchReplaceApi\": \"/engine/v1/role-tasks/fixed-batch-replace\",\n    \"InstanceStatus\": \"/engine/v1/instances/instance-status\",\n    \"TaskByUsuerApi\": \"/engine/v1/tasks/tasks-by-user/{user-id}\",\n    \"BatchApproveApi\": \"/engine/v1/activity-resolvers/batch-approve\",\n    \"InstanceAuthIdApi\": \"/engine/v1/instances/{id}/auth-id\",\n    \"InstanceCustomTopic\": \"/engine/v1/instances/{id}/custom-topic\",\n    \"InstanceStatusApi\": \"/engine/v1/instances/{id}/instance-status\",\n    \"RecallTaskApi\": \"/engine/v1/tasks/{id}/recall-task\",\n    \"RecallExtraTaskApi\": \"/engine/v1/tasks/{id}/recall-extra-task\",\n    \"InstancePars\": \"/engine/v1/instances/{id}/instance-pars\",\n    \"ParallelStatusApi\": \"/engine/v1/tasks/{id}/parallel-status\",\n    \"CheckIsCanStartApi\": \"/engine/v1/instances/check-is-can-start?bsid={bsid}&btid={btid}&boid={boid}\",\n    \"ParallelInfoApi\": \"/engine/v1/tasks/{id}/parallel-info\",\n    \"CheckIsOwnerUserApi\": \"/engine/v1/instances/check-is-owner-user?number={number}&userId={userId}\",\n    \"TaskRecallState\": \"/engine/v1/tasks/{id}/recall-state\",\n    \"TaskDelayState\": \"/engine/v1/tasks/{id}/delay-state\",\n    \"DelayTaskApi\": \"/engine/v1/tasks/{id}/delay-task\",\n    \"UpdateInstanceCallBackStatusApi\":\"/engine/v1/instances/{instanceId}/instance-Call-Back-Status\",\n    \"InstanceBatchDelApi\": \"/engine/v1/batch-del\",\n    \"MachineNodeTaskApi\": \"/engine/v1/machine-node-task/{id}\"\n  },\n  \"ApiGateway\": {\n    \"Host\": \"*************\",\n    \"Port\": \"32000\",\n    \"AppKey\": \"799e6e124ad95e09b055ae8c8cc53d7f\",\n    \"AppSecret\": \"a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0\",\n    \"Timeout\": \"30\"\n  },\n  \"ApiRequest\": {\n    \"Host\": \"*************:6380\",\n    \"DB\": 8,\n    \"Password\": \"87htd2NxIQ0YguE9\",\n    \"Timeout\": \"30\",\n    \"IsHttps\": false\n  },\n  \"EncryptKeys\": {\n    \"BPM\": \"58e13310360446198c1c596f32ad86c6\"\n  },\n  \"TodoCentreSettings\": {\n    \"UserToDoTasksApi\": \"/todo-centre/v1/tasks\",\n    \"UserToDoTaskGroupsApi\": \"/todo-centre/v1/task-groups\",\n    \"UserDoneTasksApi\": \"/todo-centre/v1/archived-tasks\",\n    \"UserDoneTaskGroupsApi\": \"/todo-centre/v1/archived-task-groups\",\n    \"UserMenuNumbersApi\": \"/todo-centre/v1/tasks/menu-numbers\",\n    \"UserDockingTasksApi\": \"/todo-centre/v1/docking-tasks\",\n    \"UserDockingTasksUrgingApi\": \"/todo-centre/v1/docking-tasks/{id}/urging\",\n    \"RelationInstancesApi\": \"/todo-centre/v1/tasks/relation-instances\",\n    \"UserInstacesApi\": \"/todo-centre/v1/tasks/user-instances\",\n    \"UserInstaceGroupsApi\": \"/todo-centre/v1/tasks/user-instance-groups\",\n    \"UpdateTaskProcesingApi\": \"/todo-centre/v1/taskProcesing/{id}\",\n    \"BatchUpdateTaskProcesingApi\": \"/todo-centre/v1/batchTaskProcesing\",\n    \"BatchUpdateTaskProcesingByInstanceNumberApi\": \"/todo-centre/v1/taskProcesingByInstanceNumber/{id}\",\n    \"GlobalRelationApi\": \"/todo-centre/v1/global/relation\",\n    \"BatchDelApi\": \"/todo-centre/v1/global/batchDelete\",\n    \"TaskUpdateStatus\": \"/todo-centre/v1/task/updateStatus\"\n  },\n  \"TodoPushSettings\": {\n      \"PushType\": \"\",\n      \"PushCenterSiteUrl\": \"https://bpm.chinahho.cn:2005/customer\",\n      \"PushCenterMobileSiteUrl\": \"https://bpm.chinahho.cn:2005/mobile\",\n      \"PushSystemCode\": \"BPM\",\n      \"RabbitMQ\": {\n          \"VirtualHost\": \"/\",\n          \"ExchangeName\": \"\",\n          \"HostName\": \"*************\",\n          \"Port\": 5672,\n          \"UserName\": \"bpm\",\n          \"Password\": \"1nsc72AZBxNFLtw4\"\n      },\n      \"MyStart\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/myStart/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"AddDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchInsert\",\n          \"Queue\": \"othermessage\"\n      },\n      \"DeleteDraft\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/draft/batchDelete\",\n          \"Queue\": \"othermessage\"\n      },\n      \"UpdateTodo\": {\n          \"Method\": \"POST\",\n          \"Url\": \"/todo-centre/v1/task/batchUpdate\",\n          \"Queue\": \"todomessage\"\n      }\n  },\n  \"FileCentreSettings\": {\n    \"ServerType\": \"local\",\n        \"Upload\": \"http://*************:32057/v1/document-services/upload/{serverType}\",\n        \"Download\": \"/customer/api/file-centre/v1/document-services/download/{serverType}\",\n        \"DownloadGW\": \"http://*************:32057/v1/document-services/download/{serverType}\",\n        \"PreviewServerType\": \"wps\",\n        \"Preview\": \"/api/file-centre/v1/document-services/view/{serverType}\",\n        \"DownloadList\": \"http://*************:32000/api/file-centre/v1/document-services/downloads/{serverType}\"\n  },\n  \"Transport\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\"\n  },\n  \"CronJob\": {\n    \"Host\": \"http://*************\",\n    \"Port\": \"32004\",\n    \"Uri\": \"/job\",\n    \"BasicUserName\": \"bpm\",\n    \"BasicPassword\": \"rPFwdOQHXnl5mzCW\",\n    \"DefaultTimeOut\": 180000,\n    \"DefaultRetryDelaysInSeconds\": \"60,3600,21600\",\n    \"QueueName\": \"bpm-job-queue\"\n  },\n  \"BPM\": {\n    \"Url\": \"https://bpm.chinahho.cn:2005\",\n    \"ImpersonatePath\": \"/platform/auth/impersonate?token\",\n    \"IndependentPath\": \"/customer/done/{id}\",\n    \"StartPath\": \"/customer/start?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"ReStartPath\": \"/customer/start/{instance-number}?bsid={bsid}&btid={btid}&boid={boid}&simulate=1\",\n    \"TokenTimeout\": 10080\n  },\n  \"AttachmentsPath\": \"/var/www/uploads\",\n  \"EventBusSettings\": {\n    \"Redis\": {\n      \"Host\": \"*************:6380,abortConnect=false,connectTimeout=60000\",\n      \"DB\": 1,\n      \"CacheDb\":7,\n      \"Password\": \"87htd2NxIQ0YguE9\"\n    },\n    \"RabbitMQ\": {\n      \"HostName\": \"*************\",\n      \"UserName\": \"bpm\",\n      \"Password\": \"1nsc72AZBxNFLtw4\", \n      \"InterfaceExchange\": \"bpm4.engine.fanout\",\n      \"IsPublishDebugMessage\":true\n    }\n  },\n  \"IsEnableToDoCenter\": true,\n    \"WebOffice\": {\n    \"ExternalDomain\": \"https://wpszt.chinahho.cn:12006\",\n    \"InternalDomain\": \"http://*************\",\n    \"GetLinkApi\": \"/open/api/preview/v1/files/{file_id}/link\",\n    \"GetEditLinkApi\": \"/open/api/edit/v1/files/{file_id}/link\",\n    \"GetWrapheaderApi\": \"/open/api/cps/sync/v1/wrapheader\",\n    \"GetContentOperateApi\": \"/open/api/cps/sync/v1/content/operate\",\n    \"GetDownloadApi\": \"/open/api/cps/v1/download/{download_id}\",\n    \"AccessKey\": \"JFVYGLQPWEUSUKPP\",\n    \"SecretKey\": \"SKbgbxempheppfmq\",\n      \"FileTypeW\": \"doc;docx\",\n    \"FileTypeS\": \"xls;xlsx\",\n    \"FileTypeP\": \"ppt;pptx\",\n    \"FileTypeF\": \"pdf\",\n    \"FileTypeX\": \"png;jpg;jpeg;gif;bmp;svg;zip;rar;tar;7z;gz\",\n    \"EditRoleCode\":  \"GWGLY\"\n  },\n  \"ContractLockSettings\": {\n    \"ContractLockApi\": \"http://*************:9182\",\n    \"Accesstoken\": \"UYAIS4vIIB\",\n    \"Secret\": \"SmbMDoM1BTifTtxTNoJ8tEEUyomzIn\",\n    \"ReceiverName\": \"测试账号\",\n    \"Contact\": \"13328033090\",\n    \"CategoryId\": \"2916235861576192065\",\n    \"TenantType\": \"COMPANY\",\n    \"TenantName\": \"中国水务投资集团有限公司\",\n    \"SealId\": 2906084454476009538,\n    \"SealReplaceUrl\": {\n      \"Before\": \"http://127.0.0.1:9181\",\n      \"After\": \"http://*************:12003\"\n    },\n    \"OpenTestUser\": \"0\"\n  },\n  \"FddTaskSettings\": {\n    \"FddAppID\": \"80001107\",\n    \"FddAppSecret\": \"FFJ2FVUUQOXWFGK4GKPPRRFF74CS4KTF\",\n    \"FddOpenCorpId\": \"3b66c3fa92d84d4e9ba84b58766ba003\",\n    \"FddServerUrl\": \"https://uat-api.fadada.com/api/v5/\",\n    \"FddActorName\": \"盟拓软件（苏州）有限公司\",\n    \"FddCorpMembers\": \"1780890618366214144\",\n    \"FddIsOnline\": \"0\"\n  },\n  \"IsEnableCancelNumber\": false,\n  \"matrixAccessToken\": {\n    \"enable\": true,\n    \"tokenExpire\": \"100\",\n    \"serverExpire\": \"100\",\n    \"whiteList\" : {\n      \"^/endpoints/business-data$\": [\"Post\"],\n      \"^/endpoints/[^/]+/cancel$\": [\"Put\"],\n      \"^/endpoints/[^/]+/recall$\": [\"Put\"],\n      \"^/endpoints/[^/]+/urging$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/done$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/reject$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/notice$\": [\"Post\"],\n      \"^/endpoints/tasks/[^/]+/extra-task$\": [\"Put\"],\n      \"^/endpoints/tasks/[^/]+/handover$\": [\"Put\"],\n      \"^/endpoints/process-steps$\": [\"Post\"],\n      \"^/v1/instances/[^/]+/steps$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/info$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/user/info$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/rename$\": [\"Put\"],\n      \"^/v1/webOffice/v1/3rd/file/history$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/version/[^/]+$\": [\"Get\"],\n      \"^/v1/webOffice/v1/3rd/file/online$\": [\"Post\"],\n      \"^/v1/webOffice/v1/3rd/file/save$\": [\"Post\"],\n      \"^/v1/contractlock/contractcallback$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack$\": [\"Post\"],\n      \"^/v1/ContractLock/FddTaskFinishCallBack2$\": [\"Post\"],\n      \"^/v1/documents/upload$\": [\"Post\"],\n      \"^/v1/documents/upload/gw$\": [\"Post\"],\n      \"^/v1/documents/upload/gwmb$\": [\"Post\"],\n      \"^/v1/hr/import-annual-leave$\": [\"Post\"]\n    }\n  }\n}','d235bd0f1498680e594712e59dea3893','2010-05-05 00:00:00','2025-07-23 03:27:21',NULL,'*************','U','shuiwu');
/*!40000 ALTER TABLE `his_config_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `resource` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `action` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE KEY `uk_role_permission` (`role`,`resource`,`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES ('shuiwu_rw','shuiwu:*:*','rw');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  UNIQUE KEY `uk_username_role` (`username`,`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES ('bpm','shuiwu_rw'),('guest','shuiwu_rw'),('nacos','ROLE_ADMIN');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenant_capacity`
--

DROP TABLE IF EXISTS `tenant_capacity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT '2010-05-05 00:00:00' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='租户容量信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant_capacity`
--

LOCK TABLES `tenant_capacity` WRITE;
/*!40000 ALTER TABLE `tenant_capacity` DISABLE KEYS */;
/*!40000 ALTER TABLE `tenant_capacity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenant_info`
--

DROP TABLE IF EXISTS `tenant_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_info_kptenantid` (`kp`,`tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='tenant_info';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant_info`
--

LOCK TABLES `tenant_info` WRITE;
/*!40000 ALTER TABLE `tenant_info` DISABLE KEYS */;
INSERT INTO `tenant_info` VALUES (1,'1','shuiwu','shuiwu','shuiwu','nacos',1694162438253,1694162438253);
/*!40000 ALTER TABLE `tenant_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('bpm','$2a$10$/GZkQgC/TrdtMNI/yW5wtOuK65hXxmDEs8X/enRB8xs2zv5I18csy',1),('guest','$2a$10$ZY4GReU3PkNDQTsxYKmtL.ufIMXwChlMfsxCPcY6AIPOghy2cTzDq',1),('nacos','$2a$10$utCSn3rZsr6uQx0jUd32a.f9ljFCkg.5XaJ2zFCg9MiH5IxJJ3GXC',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-05 17:13:32
