#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL批量导入工具测试脚本
"""

import os
import tempfile
import unittest
from sql_batch_importer import SQLBatchImporter, DatabaseConfig


class TestSQLBatchImporter(unittest.TestCase):
    """SQL批量导入工具测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时SQLite数据库用于测试
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        self.config = DatabaseConfig(
            db_type='sqlite',
            database=self.temp_db.name
        )
        
        self.importer = SQLBatchImporter(self.config, 'ERROR')  # 减少日志输出
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'importer'):
            self.importer.disconnect()
        
        # 删除临时数据库文件
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_database_connection(self):
        """测试数据库连接"""
        self.assertTrue(self.importer.connect())
        self.assertIsNotNone(self.importer.connection)
        self.assertIsNotNone(self.importer.cursor)
    
    def test_sql_statement_splitting(self):
        """测试SQL语句分割"""
        sql_content = """
        CREATE TABLE test1 (id INTEGER PRIMARY KEY);
        INSERT INTO test1 (id) VALUES (1);
        INSERT INTO test1 (id) VALUES (2);
        """
        
        statements = self.importer.split_sql_statements(sql_content)
        self.assertEqual(len(statements), 3)
        self.assertIn('CREATE TABLE test1', statements[0])
        self.assertIn('INSERT INTO test1 (id) VALUES (1)', statements[1])
        self.assertIn('INSERT INTO test1 (id) VALUES (2)', statements[2])
    
    def test_sql_statement_with_strings(self):
        """测试包含字符串的SQL语句分割"""
        sql_content = """
        CREATE TABLE test2 (name TEXT);
        INSERT INTO test2 (name) VALUES ('Hello; World');
        INSERT INTO test2 (name) VALUES ("Another; Test");
        """
        
        statements = self.importer.split_sql_statements(sql_content)
        self.assertEqual(len(statements), 3)
        self.assertIn("'Hello; World'", statements[1])
        self.assertIn('"Another; Test"', statements[2])
    
    def test_single_sql_file_import(self):
        """测试单个SQL文件导入"""
        # 创建临时SQL文件
        sql_content = """
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT UNIQUE
        );
        
        INSERT INTO users (name, email) VALUES ('张三', '<EMAIL>');
        INSERT INTO users (name, email) VALUES ('李四', '<EMAIL>');
        """
        
        temp_sql = tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False, encoding='utf-8')
        temp_sql.write(sql_content)
        temp_sql.close()
        
        try:
            # 连接数据库并导入
            self.assertTrue(self.importer.connect())
            result = self.importer.import_sql_file(temp_sql.name)
            
            # 验证结果
            self.assertTrue(result['success'])
            self.assertEqual(result['total_statements'], 3)
            self.assertEqual(result['executed_statements'], 3)
            self.assertEqual(result['failed_statements'], 0)
            
            # 验证数据是否正确插入
            self.importer.cursor.execute("SELECT COUNT(*) FROM users")
            count = self.importer.cursor.fetchone()[0]
            self.assertEqual(count, 2)
            
        finally:
            os.unlink(temp_sql.name)
    
    def test_sql_file_with_errors(self):
        """测试包含错误的SQL文件"""
        sql_content = """
        CREATE TABLE products (id INTEGER PRIMARY KEY);
        INSERT INTO products (id) VALUES (1);
        INSERT INTO nonexistent_table (id) VALUES (2);  -- 这会失败
        INSERT INTO products (id) VALUES (3);
        """
        
        temp_sql = tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False, encoding='utf-8')
        temp_sql.write(sql_content)
        temp_sql.close()
        
        try:
            self.assertTrue(self.importer.connect())
            result = self.importer.import_sql_file(temp_sql.name, continue_on_error=True)
            
            # 应该有错误，但继续执行
            self.assertFalse(result['success'])  # 因为有错误，整体失败
            self.assertEqual(result['total_statements'], 4)
            self.assertGreater(result['failed_statements'], 0)
            self.assertGreater(len(result['errors']), 0)
            
        finally:
            os.unlink(temp_sql.name)
    
    def test_multiple_files_import(self):
        """测试多文件导入"""
        # 创建多个临时SQL文件
        files = []
        
        # 文件1: 创建表
        sql1 = """
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL
        );
        """
        
        # 文件2: 插入数据
        sql2 = """
        INSERT INTO categories (name) VALUES ('电子产品');
        INSERT INTO categories (name) VALUES ('图书');
        """
        
        for i, content in enumerate([sql1, sql2], 1):
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=f'_test{i}.sql', delete=False, encoding='utf-8')
            temp_file.write(content)
            temp_file.close()
            files.append(temp_file.name)
        
        try:
            self.assertTrue(self.importer.connect())
            
            # 按顺序导入文件
            file_patterns = files
            results = self.importer.import_multiple_files(file_patterns)
            
            # 验证结果
            self.assertEqual(results['total_files'], 2)
            self.assertEqual(results['successful_files'], 2)
            self.assertEqual(results['failed_files'], 0)
            
            # 验证数据
            self.importer.cursor.execute("SELECT COUNT(*) FROM categories")
            count = self.importer.cursor.fetchone()[0]
            self.assertEqual(count, 2)
            
        finally:
            for file_path in files:
                if os.path.exists(file_path):
                    os.unlink(file_path)


def create_test_sql_files():
    """创建一些测试用的SQL文件"""
    test_files = {
        'test_create_tables.sql': """
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
        """,
        
        'test_insert_data.sql': """
-- 插入测试用户
INSERT INTO users (username, email) VALUES ('admin', '<EMAIL>');
INSERT INTO users (username, email) VALUES ('user1', '<EMAIL>');
INSERT INTO users (username, email) VALUES ('user2', '<EMAIL>');

-- 插入测试产品
INSERT INTO products (name, price, category_id) VALUES ('笔记本电脑', 5999.99, 1);
INSERT INTO products (name, price, category_id) VALUES ('无线鼠标', 99.99, 1);
INSERT INTO products (name, price, category_id) VALUES ('编程书籍', 79.99, 2);
        """
    }
    
    for filename, content in test_files.items():
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建测试文件: {filename}")


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'create-test-files':
        create_test_sql_files()
        print("\n测试文件创建完成！")
        print("您可以使用以下命令测试导入工具:")
        print("python sql_batch_importer.py test_*.sql")
    else:
        # 运行单元测试
        unittest.main()
