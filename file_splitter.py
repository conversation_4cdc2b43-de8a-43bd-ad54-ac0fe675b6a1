#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件分割工具
将超大SQL文件分割成小文件，然后分别处理
"""

import os
import sys
import time


def split_large_sql_file(file_path, max_size_mb=50):
    """将大SQL文件分割成小文件"""
    
    file_size = os.path.getsize(file_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"文件: {file_path}")
    print(f"大小: {file_size_mb:.1f}MB")
    
    if file_size_mb <= max_size_mb:
        print(f"文件小于{max_size_mb}MB，无需分割")
        return [file_path]
    
    print(f"开始分割文件，目标大小: {max_size_mb}MB")
    
    base_name = os.path.splitext(file_path)[0]
    split_files = []
    
    try:
        # 尝试不同编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                print(f"尝试编码: {encoding}")
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                print(f"✓ 成功使用编码: {encoding}")
                break
            except UnicodeDecodeError:
                print(f"✗ 编码 {encoding} 失败")
                continue
        
        if not content:
            print("所有编码都失败")
            return []
        
        print(f"文件读取完成，开始分析SQL语句...")
        
        # 简单的SQL语句分割
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        
        for i, char in enumerate(content):
            if i % 1000000 == 0:  # 每100万字符显示一次进度
                progress = (i / len(content)) * 100
                print(f"分析进度: {progress:.1f}%")
            
            if not in_string:
                if char in ("'", '"'):
                    in_string = True
                    string_char = char
                elif char == ';':
                    if current_statement.strip():
                        statements.append(current_statement.strip())
                    current_statement = ""
                    continue
            else:
                if char == string_char:
                    if i + 1 < len(content) and content[i + 1] == string_char:
                        current_statement += char + content[i + 1]
                        i += 1
                        continue
                    else:
                        in_string = False
                        string_char = None
            
            current_statement += char
        
        # 添加最后一个语句
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        print(f"✓ 分析完成，找到 {len(statements)} 个SQL语句")
        
        # 计算每个文件应该包含多少语句
        max_size_bytes = max_size_mb * 1024 * 1024
        current_file_size = 0
        current_file_statements = []
        file_index = 1
        
        for i, statement in enumerate(statements):
            statement_size = len(statement.encode(used_encoding))
            
            # 如果添加这个语句会超过大小限制，先保存当前文件
            if current_file_size + statement_size > max_size_bytes and current_file_statements:
                # 保存当前文件
                split_file_path = f"{base_name}_part{file_index:03d}.sql"
                
                with open(split_file_path, 'w', encoding=used_encoding) as f:
                    f.write(';\n'.join(current_file_statements) + ';')
                
                split_files.append(split_file_path)
                actual_size = os.path.getsize(split_file_path) / (1024 * 1024)
                print(f"创建分割文件: {split_file_path} ({actual_size:.1f}MB, {len(current_file_statements)} 个语句)")
                
                # 重置
                current_file_statements = []
                current_file_size = 0
                file_index += 1
            
            # 添加当前语句
            current_file_statements.append(statement)
            current_file_size += statement_size
        
        # 保存最后一个文件
        if current_file_statements:
            split_file_path = f"{base_name}_part{file_index:03d}.sql"
            
            with open(split_file_path, 'w', encoding=used_encoding) as f:
                f.write(';\n'.join(current_file_statements) + ';')
            
            split_files.append(split_file_path)
            actual_size = os.path.getsize(split_file_path) / (1024 * 1024)
            print(f"创建分割文件: {split_file_path} ({actual_size:.1f}MB, {len(current_file_statements)} 个语句)")
        
        print(f"\n✓ 分割完成！")
        print(f"原文件: {file_path} ({file_size_mb:.1f}MB)")
        print(f"分割为: {len(split_files)} 个文件")
        
        total_size = sum(os.path.getsize(f) for f in split_files) / (1024 * 1024)
        print(f"总大小: {total_size:.1f}MB")
        
        return split_files
        
    except Exception as e:
        print(f"分割文件时出错: {e}")
        return []


def process_split_files_with_creator(split_files):
    """使用数据库创建工具处理分割后的文件"""
    if not split_files:
        return
    
    print(f"\n开始处理 {len(split_files)} 个分割文件...")
    
    # 导入数据库创建工具
    try:
        from sql_database_creator import SQLDatabaseCreator, load_config_from_file
    except ImportError:
        print("无法导入数据库创建工具")
        return
    
    # 加载配置
    try:
        config = load_config_from_file('db_config.ini')
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    # 获取原始数据库名（从第一个分割文件推断）
    first_file = split_files[0]
    base_name = first_file.replace('_part001.sql', '').replace('.sql', '')
    database_name = os.path.basename(base_name).replace('-', '_')
    
    print(f"目标数据库: {database_name}")
    
    creator = SQLDatabaseCreator(config, 'INFO')
    
    try:
        # 连接服务器
        if not creator.connect_to_server():
            print("无法连接到数据库服务器")
            return
        
        # 创建数据库
        print(f"创建数据库: {database_name}")
        if not creator.create_database(database_name, drop_if_exists=True):
            print("创建数据库失败")
            return
        
        # 连接到数据库
        if not creator.connect_to_database(database_name):
            print("连接数据库失败")
            return
        
        # 逐个处理分割文件
        total_statements = 0
        total_success = 0
        total_errors = 0
        
        for i, split_file in enumerate(split_files, 1):
            print(f"\n处理分割文件 {i}/{len(split_files)}: {split_file}")
            
            # 读取文件
            try:
                with open(split_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 分割语句
                statements = [s.strip() for s in content.split(';') if s.strip()]
                total_statements += len(statements)
                
                print(f"  包含 {len(statements)} 个SQL语句")
                
                # 执行语句
                success_count = 0
                error_count = 0
                
                for j, statement in enumerate(statements, 1):
                    try:
                        creator.cursor.execute(statement)
                        success_count += 1
                        
                        # 每10个语句提交一次
                        if j % 10 == 0:
                            creator.connection.commit()
                        
                        # 显示进度
                        if j % 50 == 0 or j == len(statements):
                            progress = (j / len(statements)) * 100
                            print(f"    进度: {j}/{len(statements)} ({progress:.1f}%)")
                    
                    except Exception as e:
                        error_count += 1
                        print(f"    语句 {j} 执行失败: {str(e)[:100]}...")
                
                # 提交剩余的语句
                creator.connection.commit()
                
                total_success += success_count
                total_errors += error_count
                
                print(f"  ✓ 文件处理完成: 成功 {success_count}, 失败 {error_count}")
                
            except Exception as e:
                print(f"  ✗ 处理文件 {split_file} 时出错: {e}")
        
        print(f"\n" + "=" * 60)
        print(f"所有分割文件处理完成!")
        print(f"数据库: {database_name}")
        print(f"总语句数: {total_statements}")
        print(f"成功执行: {total_success}")
        print(f"执行失败: {total_errors}")
        print(f"成功率: {(total_success/total_statements*100):.1f}%")
        print(f"=" * 60)
        
    finally:
        creator.disconnect()


def main():
    """主函数"""
    print("大文件分割和处理工具")
    print("=" * 50)
    
    # 查找大文件
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql') and not '_part' in f]
    large_files = []
    
    for sql_file in sql_files:
        size_mb = os.path.getsize(sql_file) / (1024 * 1024)
        if size_mb > 50:  # 大于50MB
            large_files.append((sql_file, size_mb))
    
    if not large_files:
        print("没有找到大于50MB的SQL文件")
        return
    
    large_files.sort(key=lambda x: x[1], reverse=True)
    
    print(f"找到 {len(large_files)} 个大文件:")
    for i, (file, size) in enumerate(large_files, 1):
        print(f"{i:2d}. {file} ({size:.1f}MB)")
    
    try:
        choice = int(input("\n请选择要处理的文件编号: ")) - 1
        if 0 <= choice < len(large_files):
            file_path, size = large_files[choice]
            
            print(f"\n选择的文件: {file_path} ({size:.1f}MB)")
            
            # 询问分割大小
            max_size = input(f"请输入分割后每个文件的最大大小(MB，默认50): ").strip()
            if not max_size:
                max_size = 50
            else:
                max_size = int(max_size)
            
            # 分割文件
            split_files = split_large_sql_file(file_path, max_size)
            
            if split_files:
                # 询问是否立即处理
                process_now = input(f"\n是否立即处理分割后的文件? (y/n): ").strip().lower()
                if process_now == 'y':
                    process_split_files_with_creator(split_files)
                else:
                    print(f"\n分割文件已保存，您可以稍后使用以下命令处理:")
                    print(f"python sql_database_creator.py {' '.join(split_files)}")
        else:
            print("无效选择")
    
    except (ValueError, KeyboardInterrupt):
        print("\n程序被中断")


if __name__ == '__main__':
    main()
