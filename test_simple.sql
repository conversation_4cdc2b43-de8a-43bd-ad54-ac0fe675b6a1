-- 简单测试数据库
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (username, email) VALUES 
('admin', '<EMAIL>'),
('user1', '<EMAIL>'),
('user2', '<EMAIL>');

INSERT INTO products (name, price, description) VALUES 
('笔记本电脑', 5999.99, '高性能笔记本电脑'),
('无线鼠标', 99.99, '蓝牙无线鼠标'),
('机械键盘', 299.99, '青轴机械键盘');
