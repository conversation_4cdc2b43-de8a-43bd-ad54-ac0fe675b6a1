-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: interfacecenter_dev
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `apibase`
--

DROP TABLE IF EXISTS `apibase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apibase` (
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `ApiName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API名称',
  `RequestMethod` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求方法',
  `RequestPath` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '请求路径',
  `IsSecurityCert` bit(1) DEFAULT NULL COMMENT '是否加密',
  `Description` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '描述',
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API主键Id',
  `ApiVersionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API版本Id',
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人Id',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人Id',
  `ModifyDate` datetime(3) DEFAULT NULL COMMENT '修改时间',
  `CategoryId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属分类',
  PRIMARY KEY (`ApiBaseId`) USING BTREE,
  KEY `IDX_ApiBase_ApiId` (`ApiId`) USING BTREE,
  KEY `IDX_ApiBase_ApiVersionId` (`ApiVersionId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API基本信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apibase`
--

LOCK TABLES `apibase` WRITE;
/*!40000 ALTER TABLE `apibase` DISABLE KEYS */;
INSERT INTO `apibase` VALUES ('28580b9b-1f33-4117-984b-bc6bf32d6676','获取水务岗位/职务信息','Post','/GetPostJob',NULL,'获取水务岗位/职务信息','a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb','66fc0f77-1f17-427b-a9cb-25c117e00867','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-26 03:15:49.000','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-30 03:05:40.892','ac88def4-d37a-4413-984f-eff12c565260'),('3d1bb54c-3e44-4f2f-9410-eadd50bb003c','更新字号','Post','/update-bookmark',NULL,'更新字号','cd49b8d1-8535-4579-a4bc-76768a29e959','8b2de592-94f3-4b13-b8a8-11ac40e40770','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-03-27 17:58:30.000','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-23 12:18:52.125','ac88def4-d37a-4413-984f-eff12c565260'),('86b15014-49d7-4beb-9edd-3dacabb2c600','推送请假至EOPX','Post','/PushLeave',NULL,'推送请假至EOPX','19586f45-d0ed-4c97-b7fb-ee63c3dcd893','5c5b9da3-eb70-43f3-bf2b-672dc43ff94c','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-12-22 14:00:17.564',NULL,NULL,NULL,'ac88def4-d37a-4413-984f-eff12c565260'),('8adbff29-3a78-4dc8-bd20-d45ca0364c83','流程结构化存储','Post','/structuredstorage',NULL,'流程结构化存储','701c80a7-473a-4dd4-8449-3fe71e476714','da798bf4-0a75-411f-b1da-a4050aa4d5d6','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-26 23:31:56.000','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-26 23:32:16.963','ac88def4-d37a-4413-984f-eff12c565260'),('9af5e62f-fbaa-42b1-8dfa-2b3661012f59','电子文件送达机器人','Post','/bpm/process/v1/instanceSendRecvRecord/addListByInstance',NULL,'电子文件送达机器人','4b224771-8bea-4acc-a18e-4a207da0125e','e11d2a32-cccc-43d6-9ee0-75457337214f','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-07-12 16:39:51.977',NULL,NULL,NULL,'ac88def4-d37a-4413-984f-eff12c565260'),('a4244630-f12b-40ec-93a8-9336efe2f320','生成编号','Post','/generate-number',NULL,'生成编号','aed8cc16-8e06-4b9a-87ac-4afed36cbfdc','d1cbd53a-e2d4-4279-8492-5e7e77c54a27','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-30 10:05:41.193',NULL,NULL,NULL,'ac88def4-d37a-4413-984f-eff12c565260'),('a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','备案前回调业务系统','Post','/CallBackBS',NULL,'备案前回调业务系统','b42d7073-f79c-459b-831d-715bc65fd83e','456a4ff0-ff36-4e1e-99c6-2bc3836643e7','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-26 10:10:17.000','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-05-10 17:38:46.345','ac88def4-d37a-4413-984f-eff12c565260'),('a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db','作废公文编号','Post','/cancelNumber',NULL,'作废公文编号','7448fd78-311b-422e-a80d-48508b0a6c43','01e7cb50-5f68-47eb-b5c2-f87ba335f244','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2024-01-08 15:40:27.037',NULL,NULL,NULL,'ac88def4-d37a-4413-984f-eff12c565260'),('b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1','推送新闻至EOPX','Post','/PushNews',NULL,'推送新闻至EOPX','cbd670df-1478-42d2-a7e4-f4b90dd88c35','ea8217f5-33b9-449c-ab04-5fb17351c649','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-06-24 16:57:52.000','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-09-24 17:01:11.286','ac88def4-d37a-4413-984f-eff12c565260'),('cb7316bd-c7a1-4a3a-9c65-466482dd7925','设置公文套红','Post','/wrapheader',NULL,'公文套红','f2465026-b9ca-40b9-86d1-c4396337f46f','f78650e5-2c1d-4e0a-9fd6-8e89056c7cb2','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-11-22 04:28:48.000','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-12-13 02:37:59.111','ac88def4-d37a-4413-984f-eff12c565260'),('d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9','获取用户年假信息','Post','/user-annual-leave',NULL,'获取用户年假信息','8208b1ec-9498-4b89-b568-58891e4ea19e','1f3f31b3-1fb5-4f17-acff-44212dd519c9','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-12-04 00:47:38.000','姚磊','1c7153cb-48bc-4134-b724-f4debfabe9ab','2025-04-19 23:03:10.247','ac88def4-d37a-4413-984f-eff12c565260'),('e64177a8-23e7-4bc6-926c-eb307d9bed1a','销假','Post','/end',NULL,'销假','4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1','178c2287-cdb9-4aa5-b6d2-59ae39d6c2dc','姚磊','1c7153cb-48bc-4134-b724-f4debfabe9ab','2025-03-18 15:34:19.637',NULL,NULL,NULL,'ac88def4-d37a-4413-984f-eff12c565260');
/*!40000 ALTER TABLE `apibase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apicalllog`
--

DROP TABLE IF EXISTS `apicalllog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apicalllog` (
  `ApiCallLogId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `Addtime` datetime(3) DEFAULT NULL COMMENT '添加时间',
  `Url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '访问路径',
  `Parame` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '访问参数',
  `State` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态',
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API主键Id',
  `ApiName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT 'API名称',
  PRIMARY KEY (`ApiCallLogId`) USING BTREE,
  KEY `IDX_ApiCallLog_ApiId` (`ApiId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API调试日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apicalllog`
--

LOCK TABLES `apicalllog` WRITE;
/*!40000 ALTER TABLE `apicalllog` DISABLE KEYS */;
/*!40000 ALTER TABLE `apicalllog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apicategory`
--

DROP TABLE IF EXISTS `apicategory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apicategory` (
  `CategoryId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `CategoryCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分类编号',
  `CategoryType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分类类型',
  `CategoryName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分类名称',
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人Id',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人Id',
  `ModifyDate` datetime(3) DEFAULT NULL COMMENT '修改时间',
  `Status` int DEFAULT '0' COMMENT '状态 1 有效 0 无效',
  PRIMARY KEY (`CategoryId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API分类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apicategory`
--

LOCK TABLES `apicategory` WRITE;
/*!40000 ALTER TABLE `apicategory` DISABLE KEYS */;
INSERT INTO `apicategory` VALUES ('77937ef6-2bfb-41ed-b430-506da37eb12b','COMMON','自定义','通用','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-26 02:48:28.514',NULL,NULL,NULL,0),('ac88def4-d37a-4413-984f-eff12c565260','BpmCommon','自定义','BPM通用','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-26 02:49:27.798',NULL,NULL,NULL,1),('d318e2de-6580-4e68-897d-b60ff7125765','GangWei','自定义','水务岗位职务','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-25 04:30:19.528',NULL,NULL,NULL,0);
/*!40000 ALTER TABLE `apicategory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apidownstreamparams`
--

DROP TABLE IF EXISTS `apidownstreamparams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apidownstreamparams` (
  `ParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ParamName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数名称',
  `ParamLocation` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数位置',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `DataSource` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据来源',
  `DefaultValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认值',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息主键ID',
  `DataSourceType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据来源类型',
  `UpperParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级参数ID',
  PRIMARY KEY (`ParamId`) USING BTREE,
  KEY `IDX_ApiDownstreamParams_ApiBaseId` (`ApiBaseId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API下游接口参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apidownstreamparams`
--

LOCK TABLES `apidownstreamparams` WRITE;
/*!40000 ALTER TABLE `apidownstreamparams` DISABLE KEYS */;
INSERT INTO `apidownstreamparams` VALUES ('0340955a-12b7-397b-c999-633331de9a1f','where','Body','object',NULL,NULL,NULL,NULL,4,'28580b9b-1f33-4117-984b-bc6bf32d6676',NULL,'4b3ea389-cf27-55ad-99b0-05e966532383'),('0926b4c3-c4b1-db27-3cc2-a75017b0416a','number','Body','string',NULL,NULL,'78c0f9d3-6aa2-224c-470b-537a7f35042e',NULL,0,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1','RequestBody','731dbe14-55f7-ed16-cfca-a05b559a08c5'),('0aa94c78-dbaf-1fb1-a509-0aef4d61366e','instanceData','Body','object',NULL,NULL,'8dc0ae28-5d99-07b1-7794-e564c36ba118',NULL,2,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c','RequestBody','1a730c09-2fdb-9164-1be3-20e8d66de6b6'),('0b040ab1-ec88-fec3-4acf-17335561d5f4','businessData','Body','array',NULL,NULL,'83a22e30-16df-4dfb-3f3a-40759e0e5d1e',NULL,2,'e64177a8-23e7-4bc6-926c-eb307d9bed1a','RequestBody','0ff8d46a-344c-9cdf-1b50-c4531759d1d5'),('0ff8d46a-344c-9cdf-1b50-c4531759d1d5','root','Body','object',NULL,NULL,NULL,NULL,0,'e64177a8-23e7-4bc6-926c-eb307d9bed1a',NULL,NULL),('119c1a29-23a3-8fb7-ba4f-0d1d4f7bf3c2','page','Body','number',NULL,NULL,'9d79d8af-7d31-1451-c4ab-784e1cc903d9',NULL,2,'28580b9b-1f33-4117-984b-bc6bf32d6676','RequestBody','4b3ea389-cf27-55ad-99b0-05e966532383'),('15e045ef-5a44-cd5c-a384-2b3f756b8474','root','Body','object',NULL,NULL,NULL,NULL,0,'a4244630-f12b-40ec-93a8-9336efe2f320',NULL,NULL),('1a730c09-2fdb-9164-1be3-20e8d66de6b6','root','Body','object',NULL,NULL,NULL,NULL,0,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c',NULL,NULL),('20cd61cf-d633-090c-ab66-de5579a196bf','activities','Body','array',NULL,NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074',NULL,1,'cb7316bd-c7a1-4a3a-9c65-466482dd7925','RequestBody','593ed231-cc8a-0995-ea2e-f905c00a2539'),('26f08699-ad39-fbec-4ed2-e43e51c3c7a0','root','Body','object',NULL,NULL,NULL,NULL,0,'8adbff29-3a78-4dc8-bd20-d45ca0364c83',NULL,NULL),('2ac65e52-aead-38b2-dbf0-f9a431de1a5d','CreateDate','Body','string',NULL,NULL,'a4d80eed-da69-46b8-85ac-dce6f1fe4a3b',NULL,3,'28580b9b-1f33-4117-984b-bc6bf32d6676','RequestBody','0340955a-12b7-397b-c999-633331de9a1f'),('33ef8dc7-9f6b-e94c-9082-cd08df23dcc6','PostName','Body','string',NULL,NULL,'9c80c1ae-135f-e240-1f3d-2bb6138b3a05',NULL,0,'28580b9b-1f33-4117-984b-bc6bf32d6676','RequestBody','0340955a-12b7-397b-c999-633331de9a1f'),('3de4a3b7-bf71-09c9-4f25-a303865814c3','root','Body','object',NULL,NULL,NULL,NULL,0,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',NULL,NULL),('400baa31-ba71-7e0f-7fcb-b0fd6d00771a','number','Body','string',NULL,NULL,'f04de2dc-40a8-80f6-a7b0-e1c743026a04',NULL,0,'8adbff29-3a78-4dc8-bd20-d45ca0364c83','RequestBody','26f08699-ad39-fbec-4ed2-e43e51c3c7a0'),('40132c95-4e79-a44d-6f9a-77a19806cfe3','sartTime','Body','string',NULL,NULL,'25965293-02ae-e7ce-8996-37b25caf1b9d',NULL,2,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db','RequestBody','3de4a3b7-bf71-09c9-4f25-a303865814c3'),('433ad315-6349-e159-b960-2309f5fa807d','JobName','Body','string',NULL,NULL,'377bdc0e-b8ff-d605-a894-965a780d09ff',NULL,2,'28580b9b-1f33-4117-984b-bc6bf32d6676','RequestBody','0340955a-12b7-397b-c999-633331de9a1f'),('4b3ea389-cf27-55ad-99b0-05e966532383','root','Body','object',NULL,NULL,NULL,NULL,0,'28580b9b-1f33-4117-984b-bc6bf32d6676',NULL,NULL),('58b1f378-7b09-e6b1-8dbe-7a3ea210fe6d','number','Body','string',NULL,NULL,'96945f55-bb7c-2ff9-fa94-f88aaf976646',NULL,0,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c','RequestBody','1a730c09-2fdb-9164-1be3-20e8d66de6b6'),('593ed231-cc8a-0995-ea2e-f905c00a2539','root','Body','object',NULL,NULL,NULL,NULL,0,'cb7316bd-c7a1-4a3a-9c65-466482dd7925',NULL,NULL),('5dfa7394-f6ae-514a-3ea5-51c32c5c220d','root','Body','object',NULL,NULL,NULL,NULL,0,'86b15014-49d7-4beb-9edd-3dacabb2c600',NULL,NULL),('5e926d05-71c3-e4ea-3fe7-31c544ee10ea','BSID','Body','string',NULL,NULL,'e2fc4a31-4cfc-3f52-42da-47cc45ff934d',NULL,1,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('641cdd45-bc8a-455f-0c41-821766ac871a','activities','Body','array',NULL,NULL,'acf9ec0d-8908-ad0f-5a19-26deeec91aed',NULL,2,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1','RequestBody','731dbe14-55f7-ed16-cfca-a05b559a08c5'),('64a2b29a-ad56-538f-0308-1893a8b2844b','BTID','Body','string',NULL,NULL,'1debcee5-e048-7f5c-5040-98aa147b1eb5',NULL,2,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('6603e05f-8b92-c10d-2a2f-c2760ce089b6','processId','Body','string',NULL,NULL,'ae91f7a6-170c-f258-410f-89f7e18d2dcc',NULL,1,'a4244630-f12b-40ec-93a8-9336efe2f320','RequestBody','15e045ef-5a44-cd5c-a384-2b3f756b8474'),('685ef597-e0cf-338d-c03f-f6d1ab8a90ea','PostName_czf','Body','string',NULL,NULL,'Like',NULL,1,'28580b9b-1f33-4117-984b-bc6bf32d6676','FixedValue','0340955a-12b7-397b-c999-633331de9a1f'),('6a5eb3df-49ed-363c-a96f-dd69302522a2','Num','Body','string',NULL,NULL,'c0458cdd-c754-5b75-b20f-8d0bf0296f96',NULL,0,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c','RequestBody','0aa94c78-dbaf-1fb1-a509-0aef4d61366e'),('6dcf7147-3612-ec55-4592-26abfddacd4a','pageSize','Body','number',NULL,NULL,'63225fbc-3cf0-9320-c02c-7381d97750b8',NULL,3,'28580b9b-1f33-4117-984b-bc6bf32d6676','RequestBody','4b3ea389-cf27-55ad-99b0-05e966532383'),('6f17e320-ed48-caac-1272-eb076bd0508c','InstanceExtension','Body','string',NULL,NULL,'04f513dc-1635-e9ed-8259-90c47007e164',NULL,5,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('711fb61a-9f76-db90-60d2-6b7b58324972','number','Body','string',NULL,NULL,'1f2a7169-cbe7-d4af-a03b-402b3ab26690',NULL,0,'cb7316bd-c7a1-4a3a-9c65-466482dd7925','RequestBody','593ed231-cc8a-0995-ea2e-f905c00a2539'),('731dbe14-55f7-ed16-cfca-a05b559a08c5','root','Body','object',NULL,NULL,NULL,NULL,0,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',NULL,NULL),('75b847a2-0b54-bd6f-9d80-6e10776d2da4','CreateDate','Body','string',NULL,NULL,'Asc',NULL,0,'28580b9b-1f33-4117-984b-bc6bf32d6676','FixedValue','c6121ddd-fe19-f54a-914c-390161464d62'),('82bcd33e-5865-90bb-01ef-45f3a3de5e63','processid','Body','string',NULL,NULL,'91227e25-6bae-6f5a-6951-ec51ed8c6d88',NULL,1,'86b15014-49d7-4beb-9edd-3dacabb2c600','RequestBody','5dfa7394-f6ae-514a-3ea5-51c32c5c220d'),('85b9f2b3-83ee-150f-fd33-b6d5adcd4025','user-id','Path','string',NULL,NULL,'58dc2f20-a56d-b919-369a-944437b2862a',NULL,0,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9','RequestBody',NULL),('87e3a9d0-243f-20b2-fe7d-1a06942bf412','bookMarks','Body','string',NULL,NULL,'Num',NULL,1,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c','FixedValue','1a730c09-2fdb-9164-1be3-20e8d66de6b6'),('89585afe-8f84-3f72-534f-7341a7a11e74','status','Body','string',NULL,NULL,'b144b8ef-c9e7-3bc2-1e98-5efe555c781b',NULL,0,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db','RequestBody','3de4a3b7-bf71-09c9-4f25-a303865814c3'),('92e8bd53-18ae-5a19-f0f8-826aaed11064','number','Body','string',NULL,NULL,'cd89f34b-498f-8ebd-60c6-cb591177c3b5',NULL,0,'86b15014-49d7-4beb-9edd-3dacabb2c600','RequestBody','5dfa7394-f6ae-514a-3ea5-51c32c5c220d'),('9fdf3e57-88d7-9df6-5144-53ab88847783','BusinessData','Body','string',NULL,NULL,'1c8db09f-429d-a517-fa1b-e09cb61e8dce',NULL,4,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('b17eabe0-2744-8ba8-3198-fabcd7f12620','BTID','Body','string',NULL,NULL,'dac05cd7-78e2-67fa-3e12-463adfc85ce1',NULL,1,'e64177a8-23e7-4bc6-926c-eb307d9bed1a','RequestBody','0ff8d46a-344c-9cdf-1b50-c4531759d1d5'),('b2bf7223-ed7e-0ef2-0e48-4421d2a88262','root','Body','object',NULL,NULL,NULL,NULL,0,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',NULL,NULL),('c152c860-7a34-6fbb-cc44-a4edf378844b','EndType','Body','number',NULL,NULL,'1',NULL,4,'e64177a8-23e7-4bc6-926c-eb307d9bed1a','FixedValue','0ff8d46a-344c-9cdf-1b50-c4531759d1d5'),('c6121ddd-fe19-f54a-914c-390161464d62','orderby','Body','object',NULL,NULL,NULL,NULL,5,'28580b9b-1f33-4117-984b-bc6bf32d6676',NULL,'4b3ea389-cf27-55ad-99b0-05e966532383'),('cb00841f-cb75-f489-37fe-d7334b421202','businessData','Body','object',NULL,NULL,'37a1e9eb-1092-4fc2-84c0-6997025e90ea',NULL,3,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db','RequestBody','3de4a3b7-bf71-09c9-4f25-a303865814c3'),('cd2ecc9c-8997-178e-fe15-bd4e56b32b7b','Number','Body','string',NULL,NULL,'f46409b8-a0c1-f41c-9f67-608defe45d49',NULL,0,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('d56b7312-9d6d-e81a-6ddb-135f26ae48ae','BOID','Body','string',NULL,NULL,'ba8a59e5-fa1e-8ca3-2285-4059c4d5a21b',NULL,3,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9','RequestBody','b2bf7223-ed7e-0ef2-0e48-4421d2a88262'),('df1c5921-ffc9-ce70-102c-c594c50cd14d','processid','Body','string',NULL,NULL,'fd1768fd-4930-a877-6829-cf772cea18cd',NULL,1,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1','RequestBody','731dbe14-55f7-ed16-cfca-a05b559a08c5'),('e2115bf9-8235-9fc4-fd76-d17cdefe6584','name','Body','string',NULL,NULL,'PostJob',NULL,0,'28580b9b-1f33-4117-984b-bc6bf32d6676','FixedValue','4b3ea389-cf27-55ad-99b0-05e966532383'),('e3b9dc66-018c-e7e7-aa97-92d773271846','InstanceNumber','Body','string',NULL,NULL,'11cfc9d4-2a3e-dc73-c493-cbbb152c4567',NULL,0,'e64177a8-23e7-4bc6-926c-eb307d9bed1a','RequestBody','0ff8d46a-344c-9cdf-1b50-c4531759d1d5'),('e8b9b39c-95ec-6f1f-b572-252c94d5dd5a','isAll','Body','boolean',NULL,NULL,'false',NULL,1,'28580b9b-1f33-4117-984b-bc6bf32d6676','FixedValue','4b3ea389-cf27-55ad-99b0-05e966532383'),('ed02f589-ae09-3574-0c6b-61665430a45d','processId','Body','string',NULL,NULL,'78686cd6-a8ad-4cd8-9cd3-a87ee16b2a8b',NULL,1,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db','RequestBody','3de4a3b7-bf71-09c9-4f25-a303865814c3'),('f6f16c57-4dbe-fb56-f782-9fa08c345b3c','number','Body','string',NULL,NULL,'a24ae877-0bc0-1967-ec52-4492d2cea7f3',NULL,0,'a4244630-f12b-40ec-93a8-9336efe2f320','RequestBody','15e045ef-5a44-cd5c-a384-2b3f756b8474'),('f8a980fb-cb83-bc78-266b-489f2586dc56','Status','Body','string',NULL,NULL,'approved',NULL,3,'e64177a8-23e7-4bc6-926c-eb307d9bed1a','FixedValue','0ff8d46a-344c-9cdf-1b50-c4531759d1d5');
/*!40000 ALTER TABLE `apidownstreamparams` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroupitemparammapping`
--

DROP TABLE IF EXISTS `apigroupitemparammapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroupitemparammapping` (
  `ParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ParamName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数名称',
  `DataType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `MappingType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射类型 ParamSchema ResultSchema FixedValue',
  `ItemId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '如果是类型是ResultSchema则保存对应的ApiGroupItemId',
  `MappingSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段Id',
  `MappingSchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段名称',
  `MappingDataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段数据类型',
  `ParamValue` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数值如果是类型是FixedValue则存固定值，否则为NULL',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `ApiGroupItemId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'Api组合项主键Id',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`ParamId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合项参数映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroupitemparammapping`
--

LOCK TABLES `apigroupitemparammapping` WRITE;
/*!40000 ALTER TABLE `apigroupitemparammapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroupitemparammapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroupitems`
--

DROP TABLE IF EXISTS `apigroupitems`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroupitems` (
  `ApiGroupItemId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api主键Id',
  `RequestParamModule` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '入参请求模式',
  `RequestOrder` int DEFAULT NULL COMMENT '调用顺序',
  PRIMARY KEY (`ApiGroupItemId`) USING BTREE,
  KEY `IDX_ApiGroupItems_ApiBaseId` (`ApiBaseId`) USING BTREE,
  KEY `IDX_ApiGroupItems_ApiGroupId` (`ApiGroupId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合子项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroupitems`
--

LOCK TABLES `apigroupitems` WRITE;
/*!40000 ALTER TABLE `apigroupitems` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroupitems` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigrouppublishinfo`
--

DROP TABLE IF EXISTS `apigrouppublishinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigrouppublishinfo` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ApiGroupSchemaJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT 'Api组合请求和返回结构',
  `VersionNo` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最新版本号',
  `PublishDesc` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '发布说明',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`InnerId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigrouppublishinfo`
--

LOCK TABLES `apigrouppublishinfo` WRITE;
/*!40000 ALTER TABLE `apigrouppublishinfo` DISABLE KEYS */;
INSERT INTO `apigrouppublishinfo` VALUES ('9526a292-5e69-478c-9bfc-31ed9c3856d4','{\"paths\":{\"/user-group\":{\"Post\":{\"summary\":\"测试API组合调用\",\"parameters\":[{\"Name\":\"userId\",\"Description\":\"用户Id\",\"In\":\"body\",\"Required\":true,\"Schema\":{\"Type\":\"string\",\"Nullable\":false}}],\"responses\":{\"200\":{\"description\":\"OK\",\"schema\":{\"ErrorCode\":{\"type\":\"string\",\"description\":\"错误码\"},\"ErrorMessage\":{\"type\":\"string\",\"description\":\"错误信息\"},\"Data\":{\"type\":\"object\",\"description\":\"根节点\",\"item\":{\"user\":{\"type\":\"object\",\"description\":\"用户信息\"},\"param\":{\"type\":\"string\",\"description\":\"测试传入参数映射\"},\"fixed\":{\"type\":\"number\",\"description\":\"测试固定值\"},\"testobject\":{\"type\":\"object\",\"description\":\"testobject\",\"item\":{\"userinfo\":{\"type\":\"object\",\"description\":\"userinfo\"},\"userName\":{\"type\":\"string\",\"description\":\"userName\"}}},\"testarray\":{\"type\":\"array\",\"description\":\"testarray\"}}}}}}}}}}','20220621183134',NULL,'44f21bc0-789d-47ee-9b07-2fdeea5144bd');
/*!40000 ALTER TABLE `apigrouppublishinfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigrouprequestparams`
--

DROP TABLE IF EXISTS `apigrouprequestparams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigrouprequestparams` (
  `SchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `DefaultValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认值',
  `UpperSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级Id',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`SchemaId`) USING BTREE,
  KEY `IDX_ApiRequestBodySchema_ApiGroupId` (`ApiGroupId`) USING BTREE,
  KEY `IDX_ApiRequestBodySchema_UpperSchemaId` (`UpperSchemaId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合请求参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigrouprequestparams`
--

LOCK TABLES `apigrouprequestparams` WRITE;
/*!40000 ALTER TABLE `apigrouprequestparams` DISABLE KEYS */;
INSERT INTO `apigrouprequestparams` VALUES ('1aa9732a-6434-ca2f-5a7e-61ab7a66311f','userId','string',_binary '','用户Id',NULL,'b72bbadd-d9a8-f043-52ca-1c66cce2f5f6','44f21bc0-789d-47ee-9b07-2fdeea5144bd'),('b72bbadd-d9a8-f043-52ca-1c66cce2f5f6','root','object',NULL,'根节点',NULL,NULL,'44f21bc0-789d-47ee-9b07-2fdeea5144bd');
/*!40000 ALTER TABLE `apigrouprequestparams` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigrouprequestsample`
--

DROP TABLE IF EXISTS `apigrouprequestsample`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigrouprequestsample` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `RequestSampleType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求参数示例类型',
  `DataValue` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '示例值',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`InnerId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合请求参数示例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigrouprequestsample`
--

LOCK TABLES `apigrouprequestsample` WRITE;
/*!40000 ALTER TABLE `apigrouprequestsample` DISABLE KEYS */;
INSERT INTO `apigrouprequestsample` VALUES ('54454993-1f26-4a53-b4ae-c1e68bacccf0','json','{\n    \"userId\": \"string\"\n}','44f21bc0-789d-47ee-9b07-2fdeea5144bd');
/*!40000 ALTER TABLE `apigrouprequestsample` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroupresultmapping`
--

DROP TABLE IF EXISTS `apigroupresultmapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroupresultmapping` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段类型',
  `Description` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `UpperMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级Id',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `MappingType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射类型 ParamSchema ResultSchema FixedValue',
  `ItemId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '如果是类型是ResultSchema则保存对应的ApiGroupItemId',
  `MappingSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段Id',
  `MappingSchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段名称',
  `MappingDataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射的字段数据类型',
  `ParamValue` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数值如果是类型是FixedValue则存固定值，否则为NULL',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api分组主键Id',
  PRIMARY KEY (`MappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合请求结果字段映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroupresultmapping`
--

LOCK TABLES `apigroupresultmapping` WRITE;
/*!40000 ALTER TABLE `apigroupresultmapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroupresultmapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroupresultsample`
--

DROP TABLE IF EXISTS `apigroupresultsample`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroupresultsample` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ResultSampleType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '样例数据类型',
  `DataValue` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '样例值',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`InnerId`) USING BTREE,
  KEY `IDX_ApiResultSample_ApiBaseId` (`ApiGroupId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合结果样例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroupresultsample`
--

LOCK TABLES `apigroupresultsample` WRITE;
/*!40000 ALTER TABLE `apigroupresultsample` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroupresultsample` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroupresultschema`
--

DROP TABLE IF EXISTS `apigroupresultschema`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroupresultschema` (
  `SchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段类型',
  `Description` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字段描述',
  `UpperSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级Id',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api组合主键Id',
  PRIMARY KEY (`SchemaId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合请求结果结构';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroupresultschema`
--

LOCK TABLES `apigroupresultschema` WRITE;
/*!40000 ALTER TABLE `apigroupresultschema` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroupresultschema` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apigroups`
--

DROP TABLE IF EXISTS `apigroups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apigroups` (
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ApiGroupName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '组合名称',
  `RequestMethod` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求方式',
  `RequestPath` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求地址',
  `Description` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `CurrentVersionNo` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '当前版本号',
  `Status` int DEFAULT NULL COMMENT '状态 1 有效 0 无效',
  `PublishStatus` int DEFAULT NULL COMMENT '发布状态 1 已发布 0 已下线 -1 未发布',
  `CreatorId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建者Id',
  `CreatorName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建者名称',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `ModifierId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人Id',
  `ModifierName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人名称',
  `ModifyDate` datetime(3) DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`ApiGroupId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='Api组合';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apigroups`
--

LOCK TABLES `apigroups` WRITE;
/*!40000 ALTER TABLE `apigroups` DISABLE KEYS */;
/*!40000 ALTER TABLE `apigroups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apipublishhistory`
--

DROP TABLE IF EXISTS `apipublishhistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apipublishhistory` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `State` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态',
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API主键ID',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IDX_ApiPublishHistory_ApiId` (`ApiId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API发布记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apipublishhistory`
--

LOCK TABLES `apipublishhistory` WRITE;
/*!40000 ALTER TABLE `apipublishhistory` DISABLE KEYS */;
/*!40000 ALTER TABLE `apipublishhistory` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apipublishinfo`
--

DROP TABLE IF EXISTS `apipublishinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apipublishinfo` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `ApiSchemaJson` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT 'API结构描述JSON',
  `ApiVersionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API版本ID',
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API主键ID',
  PRIMARY KEY (`InnerId`) USING BTREE,
  KEY `IDX_ApiPublishInfo_ApiId` (`ApiId`) USING BTREE,
  KEY `IDX_ApiPublishInfo_ApiVersionId` (`ApiVersionId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API发布信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apipublishinfo`
--

LOCK TABLES `apipublishinfo` WRITE;
/*!40000 ALTER TABLE `apipublishinfo` DISABLE KEYS */;
INSERT INTO `apipublishinfo` VALUES ('09079f69-94bc-4440-997d-33da5b2f14ef','{\"ApiId\":\"aed8cc16-8e06-4b9a-87ac-4afed36cbfdc\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/generate-number\",\"RequestMethod\":\"Post\",\"Summary\":\"生成编号\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程实例号\\\"},\\\"ProcessId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程Id\\\"}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"ProcessId\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','d1cbd53a-e2d4-4279-8492-5e7e77c54a27','aed8cc16-8e06-4b9a-87ac-4afed36cbfdc'),('1fe286d3-c911-420a-b1c1-5d6ebfe52e03','{\"ApiId\":\"7448fd78-311b-422e-a80d-48508b0a6c43\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/cancelNumber\",\"RequestMethod\":\"Post\",\"Summary\":\"作废公文编号\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"ProcessId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"ProcessId\\\"},\\\"Status\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Status\\\"},\\\"StartTime\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"StartTime\\\"},\\\"BusinessData\\\":{}}\",\"RequestBodyDemo\":\"{    \\\"ProcessId\\\": \\\"string\\\",    \\\"Status\\\": \\\"string\\\",    \\\"StartTime\\\": \\\"string\\\",    \\\"BusinessData\\\": {}}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','01e7cb50-5f68-47eb-b5c2-f87ba335f244','7448fd78-311b-422e-a80d-48508b0a6c43'),('44272559-575c-4db5-9ecf-601057cc625f','{\"ApiId\":\"f2465026-b9ca-40b9-86d1-c4396337f46f\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/wrapheader\",\"RequestMethod\":\"Post\",\"Summary\":\"公文套红\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Number\\\"},\\\"Activities\\\":[{\\\"NodeId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"NodeId\\\"},\\\"Name\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Name\\\"},\\\"DetailStatus\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"DetailStatus\\\"},\\\"DetailStatusName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"DetailStatusName\\\"},\\\"Status\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Status\\\"},\\\"StatusName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"StatusName\\\"}}]}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"Activities\\\": [        {            \\\"NodeId\\\": \\\"string\\\",            \\\"Name\\\": \\\"string\\\",            \\\"DetailStatus\\\": \\\"string\\\",            \\\"DetailStatusName\\\": \\\"string\\\",            \\\"Status\\\": \\\"string\\\",            \\\"StatusName\\\": \\\"string\\\"        }    ]}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','f78650e5-2c1d-4e0a-9fd6-8e89056c7cb2','f2465026-b9ca-40b9-86d1-c4396337f46f'),('4738abb9-7ef6-4a38-bad5-e86e4df5ece1','{\"ApiId\":\"4b224771-8bea-4acc-a18e-4a207da0125e\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/bpm/process/v1/instanceSendRecvRecord/addListByInstance\",\"RequestMethod\":\"Post\",\"Summary\":\"电子文件送达机器人\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程单号\\\"},\\\"ActivityId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"节点Id\\\"},\\\"ProcessId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程Id\\\"}}\",\"RequestBodyDemo\":\"{    \\\"number\\\": \\\"string\\\",    \\\"ActivityId\\\": \\\"string\\\",    \\\"ProcessId\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{}}\"}}','e11d2a32-cccc-43d6-9ee0-75457337214f','4b224771-8bea-4acc-a18e-4a207da0125e'),('6d8a1771-aa6c-4833-9546-31214f9cf724','{\"ApiId\":\"cd49b8d1-8535-4579-a4bc-76768a29e959\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/update-bookmark\",\"RequestMethod\":\"Post\",\"Summary\":\"更新字号\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程实例号\\\"},\\\"InstanceData\\\":{\\\"Num\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"字号\\\"}}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"InstanceData\\\": {        \\\"Num\\\": \\\"string\\\"    }}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','8b2de592-94f3-4b13-b8a8-11ac40e40770','cd49b8d1-8535-4579-a4bc-76768a29e959'),('8686b74b-031e-4746-af12-79f30d78af9e','{\"ApiId\":\"b42d7073-f79c-459b-831d-715bc65fd83e\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/CallBackBS\",\"RequestMethod\":\"Post\",\"Summary\":\"备案前回调业务系统\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Number\\\"},\\\"BSID\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"BSID\\\"},\\\"BTID\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"BTID\\\"},\\\"BOID\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"BOID\\\"},\\\"BusinessData\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"BusinessData\\\"},\\\"InstanceExtension\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"InstanceExtension\\\"}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"BSID\\\": \\\"string\\\",    \\\"BTID\\\": \\\"string\\\",    \\\"BOID\\\": \\\"string\\\",    \\\"BusinessData\\\": \\\"string\\\",    \\\"InstanceExtension\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','456a4ff0-ff36-4e1e-99c6-2bc3836643e7','b42d7073-f79c-459b-831d-715bc65fd83e'),('93dac792-b7bb-4f35-9ff4-d4804021f9c4','{\"ApiId\":\"a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/GetPostJob\",\"RequestMethod\":\"Post\",\"Summary\":\"获取水务岗位/职务信息\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"page-index\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"page-index\\\"},\\\"page-size\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"page-size\\\"},\\\"PostName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"岗位全称\\\"},\\\"JobName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"职务\\\"},\\\"CreateDate\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"创建日期\\\"}}\",\"RequestBodyDemo\":\"{    \\\"page-index\\\": 0,    \\\"page-size\\\": 0,    \\\"PostName\\\": \\\"string\\\",    \\\"JobName\\\": \\\"string\\\",    \\\"CreateDate\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"total\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"total\\\"},\\\"items\\\":[{\\\"ID\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"主键\\\"},\\\"CreateDate\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"创建日期\\\"},\\\"CreateUserId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"创建用户Id\\\"},\\\"CreateUserOrgPathId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"创建用户组织路径Id\\\"},\\\"ModifyDate\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"修改日期\\\"},\\\"ModifyUserId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"修改用户Id\\\"},\\\"IsDelete\\\":{\\\"Type\\\":\\\"boolean\\\",\\\"Summary\\\":\\\"是否删除\\\"},\\\"ParentId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"上级ID\\\"},\\\"PostName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"岗位全称\\\"},\\\"PostShortName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"简称\\\"},\\\"PostNo\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"岗位编号\\\"},\\\"JobName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"职务\\\"}}]}}\"}}','66fc0f77-1f17-427b-a9cb-25c117e00867','a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb'),('9ca89b12-5762-442c-9c73-8112c6cc1822','{\"ApiId\":\"8208b1ec-9498-4b89-b568-58891e4ea19e\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/user-annual-leave\",\"RequestMethod\":\"Post\",\"Summary\":\"获取用户年假信息\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"userid\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"userid\\\"}}\",\"RequestBodyDemo\":\"{    \\\"userid\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"userId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"userId\\\"},\\\"workDate\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"workDate\\\"},\\\"annualLeave\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"annualLeave\\\"},\\\"used\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"used\\\"},\\\"unUsed\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"unUsed\\\"},\\\"carryOverAnnualLeave\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"carryOverAnnualLeave\\\"},\\\"frozenAnnualLeave\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"frozenAnnualLeave\\\"},\\\"userLoginId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"userLoginId\\\"},\\\"userName\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"userName\\\"},\\\"totallLeave\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"totallLeave\\\"}}}\"}}','1f3f31b3-1fb5-4f17-acff-44212dd519c9','8208b1ec-9498-4b89-b568-58891e4ea19e'),('a8694665-d050-468b-b886-a273b5e1b471','{\"ApiId\":\"4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/end\",\"RequestMethod\":\"Post\",\"Summary\":\"销假\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Number\\\"},\\\"BTID\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"BTID\\\"},\\\"BusinessData\\\":[],\\\"Status\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Status\\\"},\\\"EndType\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"EndType\\\"}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"BTID\\\": \\\"string\\\",    \\\"BusinessData\\\": [],    \\\"Status\\\": \\\"string\\\",    \\\"EndType\\\": 0}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{}}\"}}','178c2287-cdb9-4aa5-b6d2-59ae39d6c2dc','4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1'),('bc49d245-8aee-4d38-80c0-89f01a3ff0a3','{\"ApiId\":\"19586f45-d0ed-4c97-b7fb-ee63c3dcd893\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/PushLeave\",\"RequestMethod\":\"Post\",\"Summary\":\"推送请假至EOPX\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Number\\\"},\\\"ProcessId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"ProcessId\\\"}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"ProcessId\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','5c5b9da3-eb70-43f3-bf2b-672dc43ff94c','19586f45-d0ed-4c97-b7fb-ee63c3dcd893'),('f85db2bc-5030-40ec-8e4d-fa30d39b201d','{\"ApiId\":\"701c80a7-473a-4dd4-8449-3fe71e476714\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/structuredstorage\",\"RequestMethod\":\"Post\",\"Summary\":\"流程结构化存储\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"流程实例号\\\"}}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\"}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"number\\\",\\\"Summary\\\":\\\"code\\\"}}}\"}}','da798bf4-0a75-411f-b1da-a4050aa4d5d6','701c80a7-473a-4dd4-8449-3fe71e476714'),('fffcfa10-cfd5-4850-818c-feb45b2bd407','{\"ApiId\":\"cbd670df-1478-42d2-a7e4-f4b90dd88c35\",\"SystemCode\":\"integration-ocelot\",\"RequestPath\":\"/PushNews\",\"RequestMethod\":\"Post\",\"Summary\":\"推送新闻至EOPX\",\"RequestBodySchema\":{\"ContentType\":\"application/json\",\"RequestBodyType\":\"object\",\"RequestBody\":\"{\\\"Number\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"Number\\\"},\\\"ProcessId\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"ProcessId\\\"},\\\"Activities\\\":[]}\",\"RequestBodyDemo\":\"{    \\\"Number\\\": \\\"string\\\",    \\\"ProcessId\\\": \\\"string\\\",    \\\"Activities\\\": []}\"},\"ResponseSchema\":{\"ContentType\":\"application/json\",\"ResponseBodyType\":\"object\",\"ResponseBody\":\"{\\\"ErrorCode\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误码\\\"},\\\"ErrorMessage\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"错误信息\\\"},\\\"Data\\\":{\\\"code\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"code\\\"},\\\"message\\\":{\\\"Type\\\":\\\"string\\\",\\\"Summary\\\":\\\"message\\\"}}}\"}}','ea8217f5-33b9-449c-ab04-5fb17351c649','cbd670df-1478-42d2-a7e4-f4b90dd88c35');
/*!40000 ALTER TABLE `apipublishinfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apipublishversions`
--

DROP TABLE IF EXISTS `apipublishversions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apipublishversions` (
  `ApiVersionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `VersionNo` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '版本号',
  `Reason` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '发布原因',
  PRIMARY KEY (`ApiVersionId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API发布版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apipublishversions`
--

LOCK TABLES `apipublishversions` WRITE;
/*!40000 ALTER TABLE `apipublishversions` DISABLE KEYS */;
INSERT INTO `apipublishversions` VALUES ('01e7cb50-5f68-47eb-b5c2-f87ba335f244','20240108154027036',NULL),('178c2287-cdb9-4aa5-b6d2-59ae39d6c2dc','20250318153419637',NULL),('1f3f31b3-1fb5-4f17-acff-44212dd519c9','20231204004738008',NULL),('456a4ff0-ff36-4e1e-99c6-2bc3836643e7','20240426101017803',NULL),('5c5b9da3-eb70-43f3-bf2b-672dc43ff94c','20231222140017563',NULL),('66fc0f77-1f17-427b-a9cb-25c117e00867','20231026031549942',NULL),('8b2de592-94f3-4b13-b8a8-11ac40e40770','20240327175830619',NULL),('d1cbd53a-e2d4-4279-8492-5e7e77c54a27','20240430100541193',NULL),('da798bf4-0a75-411f-b1da-a4050aa4d5d6','20240426233156463',NULL),('e11d2a32-cccc-43d6-9ee0-75457337214f','20240712163951976',NULL),('ea8217f5-33b9-449c-ab04-5fb17351c649','20240624165752678',NULL),('f78650e5-2c1d-4e0a-9fd6-8e89056c7cb2','20231122042848873',NULL);
/*!40000 ALTER TABLE `apipublishversions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apirequestbodyschema`
--

DROP TABLE IF EXISTS `apirequestbodyschema`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apirequestbodyschema` (
  `SchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `DefaultValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认值',
  `UpperSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'Api基本信息主键Id',
  `Sequencing` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`SchemaId`) USING BTREE,
  KEY `IDX_ApiRequestBodySchema_ApiBaseId` (`ApiBaseId`) USING BTREE,
  KEY `IDX_ApiRequestBodySchema_UpperSchemaId` (`UpperSchemaId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API请求体结构';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apirequestbodyschema`
--

LOCK TABLES `apirequestbodyschema` WRITE;
/*!40000 ALTER TABLE `apirequestbodyschema` DISABLE KEYS */;
INSERT INTO `apirequestbodyschema` VALUES ('04f513dc-1635-e9ed-8259-90c47007e164','InstanceExtension','string',_binary '\0','InstanceExtension',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',6),('11cfc9d4-2a3e-dc73-c493-cbbb152c4567','Number','string',_binary '\0','Number',NULL,'611e59b8-66db-b28a-0d82-be16a55eb535','e64177a8-23e7-4bc6-926c-eb307d9bed1a',1),('1c8db09f-429d-a517-fa1b-e09cb61e8dce','BusinessData','string',_binary '\0','BusinessData',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',5),('1debcee5-e048-7f5c-5040-98aa147b1eb5','BTID','string',_binary '\0','BTID',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',3),('1df43339-b323-f597-4f19-0bbeb6e90e29','DetailStatusName','string',_binary '\0','DetailStatusName',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',4),('1f2a7169-cbe7-d4af-a03b-402b3ab26690','Number','string',_binary '\0','Number',NULL,'3c8c4ef2-2d4f-15d8-de33-9efe7ff58069','cb7316bd-c7a1-4a3a-9c65-466482dd7925',1),('22e8d063-4aae-2b87-421a-b8015c318aff','root','object',NULL,'根节点',NULL,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676',1),('25965293-02ae-e7ce-8996-37b25caf1b9d','StartTime','string',_binary '\0','StartTime',NULL,'3a7be48a-6339-2cf3-baf0-1b4321ed5b93','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',3),('2cdc538e-efa1-e49e-2e80-2122dc1c69c6','root','object',NULL,'根节点',NULL,NULL,'a4244630-f12b-40ec-93a8-9336efe2f320',1),('2d197363-3d60-d683-2bfa-08e3b9dd1f5d','root','object',NULL,'根节点',NULL,NULL,'9af5e62f-fbaa-42b1-8dfa-2b3661012f59',1),('2d405e6e-5ecb-29fb-3773-3906b03e0c95','root','object',NULL,'根节点',NULL,NULL,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',1),('34ef9476-1595-2854-dc4b-2871274e360e','number','string',_binary '\0','流程单号',NULL,'2d197363-3d60-d683-2bfa-08e3b9dd1f5d','9af5e62f-fbaa-42b1-8dfa-2b3661012f59',1),('357e2da3-1d2d-93d2-d4c0-e07e0a1a963a','DetailStatus','string',_binary '\0','DetailStatus',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',3),('377bdc0e-b8ff-d605-a894-965a780d09ff','JobName','string',_binary '\0','职务',NULL,'22e8d063-4aae-2b87-421a-b8015c318aff','28580b9b-1f33-4117-984b-bc6bf32d6676',4),('37a1e9eb-1092-4fc2-84c0-6997025e90ea','BusinessData','object',_binary '\0','BusinessData',NULL,'3a7be48a-6339-2cf3-baf0-1b4321ed5b93','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',4),('386120e3-2f4b-b34e-644f-3aa8dcb90b10','StatusName','string',_binary '\0','StatusName',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',6),('3a7be48a-6339-2cf3-baf0-1b4321ed5b93','root','object',NULL,'根节点',NULL,NULL,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',1),('3b569f67-6c10-f330-c774-45f3eaefc3db','root','object',NULL,'根节点',NULL,NULL,'86b15014-49d7-4beb-9edd-3dacabb2c600',1),('3c8c4ef2-2d4f-15d8-de33-9efe7ff58069','root','object',NULL,'根节点',NULL,NULL,'cb7316bd-c7a1-4a3a-9c65-466482dd7925',1),('3fb1cea9-a9ef-3cc6-abe1-806651f50e26','root','object',NULL,'根节点',NULL,NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',1),('58dc2f20-a56d-b919-369a-944437b2862a','userid','string',_binary '\0','userid',NULL,'f82de346-2e53-bcf3-ea5c-b830fb444dd4','d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9',1),('611e59b8-66db-b28a-0d82-be16a55eb535','root','object',NULL,'根节点',NULL,NULL,'e64177a8-23e7-4bc6-926c-eb307d9bed1a',1),('63225fbc-3cf0-9320-c02c-7381d97750b8','page-size','number',_binary '\0','page-size',NULL,'22e8d063-4aae-2b87-421a-b8015c318aff','28580b9b-1f33-4117-984b-bc6bf32d6676',2),('78686cd6-a8ad-4cd8-9cd3-a87ee16b2a8b','ProcessId','string',_binary '\0','ProcessId',NULL,'3a7be48a-6339-2cf3-baf0-1b4321ed5b93','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',1),('78c0f9d3-6aa2-224c-470b-537a7f35042e','Number','string',_binary '\0','Number',NULL,'3fb1cea9-a9ef-3cc6-abe1-806651f50e26','b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',1),('80ce4e6c-3d4a-9eae-f341-6730f7411292','Status','string',_binary '\0','Status',NULL,'611e59b8-66db-b28a-0d82-be16a55eb535','e64177a8-23e7-4bc6-926c-eb307d9bed1a',4),('83a22e30-16df-4dfb-3f3a-40759e0e5d1e','BusinessData','array',_binary '\0','BusinessData',NULL,'611e59b8-66db-b28a-0d82-be16a55eb535','e64177a8-23e7-4bc6-926c-eb307d9bed1a',3),('84449483-3306-fdd7-cdeb-174e1066d1ba','ProcessId','string',_binary '\0','流程Id',NULL,'2d197363-3d60-d683-2bfa-08e3b9dd1f5d','9af5e62f-fbaa-42b1-8dfa-2b3661012f59',3),('8dc0ae28-5d99-07b1-7794-e564c36ba118','InstanceData','object',_binary '\0','流程实例数据',NULL,'fd5768bd-f3b6-8d4b-eb61-19716b0a38d5','3d1bb54c-3e44-4f2f-9410-eadd50bb003c',2),('91227e25-6bae-6f5a-6951-ec51ed8c6d88','ProcessId','string',_binary '\0','ProcessId',NULL,'3b569f67-6c10-f330-c774-45f3eaefc3db','86b15014-49d7-4beb-9edd-3dacabb2c600',2),('95689c7f-0d9c-28b9-d205-a5fe53e1b72f','EndType','number',_binary '\0','EndType',NULL,'611e59b8-66db-b28a-0d82-be16a55eb535','e64177a8-23e7-4bc6-926c-eb307d9bed1a',5),('95f81f2b-29cb-8b04-0c45-0e595697dc5b','Status','string',_binary '\0','Status',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',5),('96945f55-bb7c-2ff9-fa94-f88aaf976646','Number','string',_binary '\0','流程实例号',NULL,'fd5768bd-f3b6-8d4b-eb61-19716b0a38d5','3d1bb54c-3e44-4f2f-9410-eadd50bb003c',1),('9c80c1ae-135f-e240-1f3d-2bb6138b3a05','PostName','string',_binary '\0','岗位全称',NULL,'22e8d063-4aae-2b87-421a-b8015c318aff','28580b9b-1f33-4117-984b-bc6bf32d6676',3),('9d79d8af-7d31-1451-c4ab-784e1cc903d9','page-index','number',_binary '\0','page-index',NULL,'22e8d063-4aae-2b87-421a-b8015c318aff','28580b9b-1f33-4117-984b-bc6bf32d6676',1),('a24ae877-0bc0-1967-ec52-4492d2cea7f3','Number','string',_binary '\0','流程实例号',NULL,'2cdc538e-efa1-e49e-2e80-2122dc1c69c6','a4244630-f12b-40ec-93a8-9336efe2f320',1),('a4d80eed-da69-46b8-85ac-dce6f1fe4a3b','CreateDate','string',_binary '\0','创建日期',NULL,'22e8d063-4aae-2b87-421a-b8015c318aff','28580b9b-1f33-4117-984b-bc6bf32d6676',5),('acf9ec0d-8908-ad0f-5a19-26deeec91aed','Activities','array',_binary '\0','Activities',NULL,'3fb1cea9-a9ef-3cc6-abe1-806651f50e26','b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',3),('ae91f7a6-170c-f258-410f-89f7e18d2dcc','ProcessId','string',_binary '\0','流程Id',NULL,'2cdc538e-efa1-e49e-2e80-2122dc1c69c6','a4244630-f12b-40ec-93a8-9336efe2f320',2),('b144b8ef-c9e7-3bc2-1e98-5efe555c781b','Status','string',_binary '\0','Status',NULL,'3a7be48a-6339-2cf3-baf0-1b4321ed5b93','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',2),('b6ad3b2a-4ee9-e7db-99f8-328fde0965a4','root','object',NULL,'根节点',NULL,NULL,'8adbff29-3a78-4dc8-bd20-d45ca0364c83',1),('ba8a59e5-fa1e-8ca3-2285-4059c4d5a21b','BOID','string',_binary '\0','BOID',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',4),('c0458cdd-c754-5b75-b20f-8d0bf0296f96','Num','string',_binary '\0','字号',NULL,'8dc0ae28-5d99-07b1-7794-e564c36ba118','3d1bb54c-3e44-4f2f-9410-eadd50bb003c',1),('c45260d7-2c6b-4c53-6085-4fe7de789f49','NodeId','string',_binary '\0','NodeId',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',1),('c79c5448-07ea-4bed-c5f1-c3b89f4e131e','ActivityId','string',_binary '\0','节点Id',NULL,'2d197363-3d60-d683-2bfa-08e3b9dd1f5d','9af5e62f-fbaa-42b1-8dfa-2b3661012f59',2),('cd89f34b-498f-8ebd-60c6-cb591177c3b5','Number','string',_binary '\0','Number',NULL,'3b569f67-6c10-f330-c774-45f3eaefc3db','86b15014-49d7-4beb-9edd-3dacabb2c600',1),('d260bce5-7efa-e501-9e6c-1b8577b8e074','Activities','array',_binary '\0','Activities',NULL,'3c8c4ef2-2d4f-15d8-de33-9efe7ff58069','cb7316bd-c7a1-4a3a-9c65-466482dd7925',2),('dac05cd7-78e2-67fa-3e12-463adfc85ce1','BTID','string',_binary '\0','BTID',NULL,'611e59b8-66db-b28a-0d82-be16a55eb535','e64177a8-23e7-4bc6-926c-eb307d9bed1a',2),('e2fc4a31-4cfc-3f52-42da-47cc45ff934d','BSID','string',_binary '\0','BSID',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',2),('e7bbbea1-1936-82db-789d-99a87b49c71e','Name','string',_binary '\0','Name',NULL,'d260bce5-7efa-e501-9e6c-1b8577b8e074','cb7316bd-c7a1-4a3a-9c65-466482dd7925',2),('f04de2dc-40a8-80f6-a7b0-e1c743026a04','Number','string',_binary '\0','流程实例号',NULL,'b6ad3b2a-4ee9-e7db-99f8-328fde0965a4','8adbff29-3a78-4dc8-bd20-d45ca0364c83',1),('f46409b8-a0c1-f41c-9f67-608defe45d49','Number','string',_binary '\0','Number',NULL,'2d405e6e-5ecb-29fb-3773-3906b03e0c95','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',1),('f82de346-2e53-bcf3-ea5c-b830fb444dd4','root','object',NULL,'根节点',NULL,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9',1),('fd1768fd-4930-a877-6829-cf772cea18cd','ProcessId','string',_binary '\0','ProcessId',NULL,'3fb1cea9-a9ef-3cc6-abe1-806651f50e26','b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',2),('fd5768bd-f3b6-8d4b-eb61-19716b0a38d5','root','object',NULL,'根节点',NULL,NULL,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c',1);
/*!40000 ALTER TABLE `apirequestbodyschema` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apiresultmapping`
--

DROP TABLE IF EXISTS `apiresultmapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apiresultmapping` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结构名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `UpperMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级结构ID',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `DataSource` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据来源',
  `DataSourceValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据来源值',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息ID',
  PRIMARY KEY (`MappingId`) USING BTREE,
  KEY `IDX_ApiResultMapping_ApiBaseId` (`ApiBaseId`) USING BTREE,
  KEY `IDX_ApiResultMapping_UpperMappingId` (`UpperMappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API结果映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apiresultmapping`
--

LOCK TABLES `apiresultmapping` WRITE;
/*!40000 ALTER TABLE `apiresultmapping` DISABLE KEYS */;
INSERT INTO `apiresultmapping` VALUES ('2dc06ba7-54b7-9d43-ce94-7533f2a7395b','root','object',NULL,'根节点',NULL,0,NULL,NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('307c04f8-9e10-9144-5ceb-9a14181c780a','root','object',NULL,'根节点',NULL,0,NULL,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('362f1829-9f03-459a-902e-e798c0b523e9','root','object',NULL,'根节点',NULL,0,NULL,NULL,'8adbff29-3a78-4dc8-bd20-d45ca0364c83'),('41182da2-bfc4-3db1-792e-d8c207ac9642','root','object',NULL,'根节点',NULL,0,NULL,NULL,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c'),('4ea71236-3560-e23b-9484-9e0f63f8368b','root','object',NULL,'根节点',NULL,0,NULL,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('74a251a3-9205-21bd-911b-2df10124417d','root','object',NULL,'根节点',NULL,0,NULL,NULL,'86b15014-49d7-4beb-9edd-3dacabb2c600'),('74ff7d12-4376-5216-9d01-ebc67bc335ab','root','object',NULL,'根节点',NULL,0,NULL,NULL,'a4244630-f12b-40ec-93a8-9336efe2f320'),('7795c533-f166-03d6-90c6-d679ac38bde1','root','object',NULL,'根节点',NULL,0,NULL,NULL,'cb7316bd-c7a1-4a3a-9c65-466482dd7925'),('85abb75f-2c67-60ee-4400-aaf5533d1e1f','root','object',NULL,'根节点',NULL,0,NULL,NULL,'e64177a8-23e7-4bc6-926c-eb307d9bed1a'),('99e6c538-25ae-fb8a-4a5d-1e87a0643024','root','object',NULL,'根节点',NULL,0,NULL,NULL,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db'),('cada4536-8659-9bb7-7e0e-1d70ed6bcc65','root','object',NULL,'根节点',NULL,0,NULL,NULL,'9af5e62f-fbaa-42b1-8dfa-2b3661012f59'),('cb5ee95c-2dbe-c003-d7e5-6afb939552e3','root','object',NULL,'根节点',NULL,0,NULL,NULL,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9');
/*!40000 ALTER TABLE `apiresultmapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apiresultsample`
--

DROP TABLE IF EXISTS `apiresultsample`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apiresultsample` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `ResultSampleType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结果样例类型',
  `DataValue` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '样例数据值',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息ID',
  `ApiSuccessDefine` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '成功定义',
  `DefineSchemaKeys` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '定义结构key',
  `DefineJudge` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '定义断言',
  `DefineValue` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '定义值',
  `ApiMsgDefine` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '返回消息体定义',
  `DefineMsgValue` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息内容',
  PRIMARY KEY (`InnerId`) USING BTREE,
  KEY `IDX_ApiResultSample_ApiBaseId` (`ApiBaseId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API结果样例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apiresultsample`
--

LOCK TABLES `apiresultsample` WRITE;
/*!40000 ALTER TABLE `apiresultsample` DISABLE KEYS */;
INSERT INTO `apiresultsample` VALUES ('027f9674-a9d0-44b5-b1eb-95b955e84bad','JSON','{\n    \"code\": 0\n}','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9',NULL,NULL,NULL,NULL,NULL,NULL),('1227024d-75e2-4906-8a8b-2b3af91a1fc6','json','{\n    \"code\": 0\n}','8adbff29-3a78-4dc8-bd20-d45ca0364c83',NULL,NULL,NULL,NULL,NULL,NULL),('4397cc46-1f94-4280-9103-6720622c3249','json','{\n    \"code\": 0\n}','cb7316bd-c7a1-4a3a-9c65-466482dd7925',NULL,NULL,NULL,NULL,NULL,NULL),('4d20d251-3a9f-47aa-bff8-2bf0c3fb26dd','json','{\n    \"code\": 0\n}','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db',NULL,NULL,NULL,NULL,NULL,NULL),('6ad7f83b-6ca2-41ce-a76e-fb915fec3e68','json','{\n    \"code\": 0\n}','3d1bb54c-3e44-4f2f-9410-eadd50bb003c',NULL,NULL,NULL,NULL,NULL,NULL),('72cb8c04-ed3c-43dd-8d4e-7c286490d99e','json','{\n    \"code\": 0\n}','86b15014-49d7-4beb-9edd-3dacabb2c600',NULL,NULL,NULL,NULL,NULL,NULL),('aae9393f-3696-4468-a8cb-55510672a6ec','json','{\n    \"userId\": \"string\",\n    \"workDate\": \"string\",\n    \"annualLeave\": 0,\n    \"used\": 0,\n    \"unUsed\": 0,\n    \"carryOverAnnualLeave\": 0,\n    \"frozenAnnualLeave\": 0,\n    \"userLoginId\": \"string\",\n    \"userName\": \"string\",\n    \"totallLeave\": 0\n}','d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9',NULL,NULL,NULL,NULL,NULL,NULL),('d12f4929-72d2-484d-afec-6e7a65e2f0f2','JSON','{\n    \"code\": \"string\",\n    \"message\": \"string\"\n}','b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1',NULL,NULL,NULL,NULL,NULL,NULL),('dd76bb79-b166-4983-823a-d4be79c1145c','JSON','{\n    \"total\": 0,\n    \"items\": [\n        {\n            \"ID\": \"string\",\n            \"CreateDate\": \"string\",\n            \"CreateUserId\": \"string\",\n            \"CreateUserOrgPathId\": \"string\",\n            \"ModifyDate\": \"string\",\n            \"ModifyUserId\": \"string\",\n            \"IsDelete\": false,\n            \"ParentId\": \"string\",\n            \"PostName\": \"string\",\n            \"PostShortName\": \"string\",\n            \"PostNo\": \"string\",\n            \"JobName\": \"string\"\n        }\n    ]\n}','28580b9b-1f33-4117-984b-bc6bf32d6676','default',NULL,NULL,NULL,NULL,NULL),('dfde3d40-a6a0-4406-9db4-09434d3119c8','JSON','{}','9af5e62f-fbaa-42b1-8dfa-2b3661012f59',NULL,NULL,NULL,NULL,NULL,NULL),('e0b75b51-a574-4090-8382-d5c5cda7c65d','json','{\n    \"code\": 0\n}','a4244630-f12b-40ec-93a8-9336efe2f320',NULL,NULL,NULL,NULL,NULL,NULL),('e26dbc5c-1341-4bec-a8e6-f918a2b0624f','JSON','{}','e64177a8-23e7-4bc6-926c-eb307d9bed1a',NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `apiresultsample` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apiresultschema`
--

DROP TABLE IF EXISTS `apiresultschema`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apiresultschema` (
  `SchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `SchemaName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结构名称',
  `DataType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必须',
  `Description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `DefaultValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认值',
  `UpperSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '上级结构ID',
  `OrderIndex` int DEFAULT NULL COMMENT '排序',
  `MappingSchemaId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射结构Id',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息ID',
  PRIMARY KEY (`SchemaId`) USING BTREE,
  KEY `IDX_ApiResultSchema_ApiBaseId` (`ApiBaseId`) USING BTREE,
  KEY `IDX_ApiResultSchema_UpperSchemaId` (`UpperSchemaId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API返回结构';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apiresultschema`
--

LOCK TABLES `apiresultschema` WRITE;
/*!40000 ALTER TABLE `apiresultschema` DISABLE KEYS */;
INSERT INTO `apiresultschema` VALUES ('01ce9132-d78c-25e8-d726-0e9f9fd9d181','CreateUserId','string',NULL,'创建用户Id',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',5,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('06f9e100-83ea-7494-6429-e71d2b1bbcf0','ID','string',NULL,'主键',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',3,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('09149b52-bce0-03b3-be41-5f5d7bea8023','root','object',NULL,'根节点',NULL,NULL,0,NULL,'e64177a8-23e7-4bc6-926c-eb307d9bed1a'),('0cdf5805-2f2c-6d17-2db6-8d1c765253bb','code','number',NULL,'code',NULL,'47783e39-4260-b45b-9c46-386439219ae9',1,NULL,'86b15014-49d7-4beb-9edd-3dacabb2c600'),('0f0c7a0c-f5b1-33cf-56c6-b0b67a6836b0','userName','string',NULL,'userName',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',9,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('14676972-7cca-ae91-8662-bd71549969a0','totallLeave','number',NULL,'totallLeave',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',10,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('156da74f-34f7-196c-6d43-243237294c06','code','number',NULL,'code',NULL,'66440d67-20c8-6871-bcf9-eed5fd2ef4e4',1,NULL,'8adbff29-3a78-4dc8-bd20-d45ca0364c83'),('17270705-3c30-2b4b-8daf-c24cf90e4fe5','items','array',NULL,'items',NULL,'4942bf4f-9a94-c3c8-e57b-0ec76aac4fb2',2,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('18329b43-3137-8adc-4d2f-8133430aab91','code','number',NULL,'code',NULL,'8421e0de-b9b6-f256-dc1f-a54accb7de9e',1,NULL,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db'),('2c7f0c5d-46a2-e1a7-fac6-04d55c3f8780','code','number',NULL,'code',NULL,'7365a629-acf0-1f80-08e5-ff8c362ffe1f',1,NULL,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c'),('2cad3bca-3c63-abe0-cd1a-b5de40f5f126','unUsed','number',NULL,'unUsed',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',5,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('37f7c787-cfd7-6fe6-f867-12c2edf180df','annualLeave','number',NULL,'annualLeave',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',3,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('3cda5803-ad23-aad4-8e3f-a93272729aa6','CreateUserOrgPathId','string',NULL,'创建用户组织路径Id',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',6,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('3d31f5fc-271b-c0aa-1d8f-c9005225d518','PostNo','string',NULL,'岗位编号',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',13,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('47783e39-4260-b45b-9c46-386439219ae9','root','object',NULL,'根节点',NULL,NULL,0,NULL,'86b15014-49d7-4beb-9edd-3dacabb2c600'),('4942bf4f-9a94-c3c8-e57b-0ec76aac4fb2','root','object',NULL,'根节点',NULL,NULL,0,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('4b36fab4-615d-ea37-1555-0249c8032629','carryOverAnnualLeave','number',NULL,'carryOverAnnualLeave',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',6,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('4cfad5d7-eea0-6d04-59b7-bf4fb019bb73','total','number',NULL,'total',NULL,'4942bf4f-9a94-c3c8-e57b-0ec76aac4fb2',1,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('50adefd2-50d8-e2ed-d22d-62d5047ef02f','PostName','string',NULL,'岗位全称',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',11,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('5d570f17-9a27-8af8-efc4-15c26b5753db','root','object',NULL,'根节点',NULL,NULL,0,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('5e77c4df-d0f1-6c1c-fbe2-af5314fbf42a','root','object',NULL,'根节点',NULL,NULL,0,NULL,'9af5e62f-fbaa-42b1-8dfa-2b3661012f59'),('66440d67-20c8-6871-bcf9-eed5fd2ef4e4','root','object',NULL,'根节点',NULL,NULL,0,NULL,'8adbff29-3a78-4dc8-bd20-d45ca0364c83'),('677aae9a-7fb9-883c-db73-e0472b891481','code','string',NULL,'code',NULL,'7515b180-94f3-dda9-fd59-7852b75a566c',1,NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('6cdd6be6-59f4-4db8-c6eb-a156f7289238','root','object',NULL,'根节点',NULL,NULL,0,NULL,'a4244630-f12b-40ec-93a8-9336efe2f320'),('6e7c1854-f600-a3b5-f5fd-a0bc26892062','ModifyDate','string',NULL,'修改日期',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',7,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('70af0920-6c8f-a4b9-ec73-e6830d8cf082','message','string',NULL,'message',NULL,'7515b180-94f3-dda9-fd59-7852b75a566c',2,NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('7365a629-acf0-1f80-08e5-ff8c362ffe1f','root','object',NULL,'根节点',NULL,NULL,0,NULL,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c'),('7515b180-94f3-dda9-fd59-7852b75a566c','root','object',NULL,'根节点',NULL,NULL,0,NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('7a878cc7-1823-42bd-adc7-384dc8c51e46','CreateDate','string',NULL,'创建日期',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',4,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('7b7627d0-cf01-51e8-6272-8997724ebdfe','userLoginId','string',NULL,'userLoginId',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',8,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('7fddb96c-efd1-c14e-950e-52a8d61180b8','used','number',NULL,'used',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',4,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('8421e0de-b9b6-f256-dc1f-a54accb7de9e','root','object',NULL,'根节点',NULL,NULL,0,NULL,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db'),('916bc3b6-d52e-84cf-bad5-59c43fc1f708','ModifyUserId','string',NULL,'修改用户Id',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',8,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('a6d55c39-8317-1489-00a6-675787a6baf3','IsDelete','boolean',NULL,'是否删除',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',9,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('a7629947-1f30-c5c6-ec6e-e93adfd9323a','code','number',NULL,'code',NULL,'e456d65b-a989-552a-0c76-1f56ed6fe7c9',1,NULL,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9'),('aa3cfe26-5a7d-316c-40b7-6ffaba732f22','PostShortName','string',NULL,'简称',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',12,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('ab12ff6c-1145-e4da-c977-ec2d9d33da1b','JobName','string',NULL,'职务',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',14,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('c348a26f-cc34-4067-845b-357ae92ace54','userId','string',NULL,'userId',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',1,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('c84f7b4c-2964-729f-4c56-bb0b123c80f1','workDate','string',NULL,'workDate',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',2,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('cd43e895-e301-b8ab-b04c-27b8e5f1ef86','root','object',NULL,'根节点',NULL,NULL,0,NULL,'cb7316bd-c7a1-4a3a-9c65-466482dd7925'),('d3bd9f7d-9784-4b7a-884a-5d05ef4f0c18','code','number',NULL,'code',NULL,'cd43e895-e301-b8ab-b04c-27b8e5f1ef86',1,NULL,'cb7316bd-c7a1-4a3a-9c65-466482dd7925'),('e456d65b-a989-552a-0c76-1f56ed6fe7c9','root','object',NULL,'根节点',NULL,NULL,0,NULL,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9'),('eec7cc58-3f99-0c3a-a580-c2a721577308','ParentId','string',NULL,'上级ID',NULL,'17270705-3c30-2b4b-8daf-c24cf90e4fe5',10,NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('f56c9d95-704e-1ba4-b9b7-0446e9996990','frozenAnnualLeave','number',NULL,'frozenAnnualLeave',NULL,'5d570f17-9a27-8af8-efc4-15c26b5753db',7,NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('f84c4403-4a19-308f-11e2-0b1cbb04567a','code','number',NULL,'code',NULL,'6cdd6be6-59f4-4db8-c6eb-a156f7289238',1,NULL,'a4244630-f12b-40ec-93a8-9336efe2f320');
/*!40000 ALTER TABLE `apiresultschema` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apis`
--

DROP TABLE IF EXISTS `apis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apis` (
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `Status` int DEFAULT NULL COMMENT '状态',
  `PublishStatus` int DEFAULT NULL COMMENT '发布状态',
  `CurrentVersionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '当前版本Id',
  `CurrentVersionNo` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '当前版本号',
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人Id',
  `CreateDate` datetime DEFAULT NULL COMMENT '创建时间',
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人Id',
  `ModifyDate` datetime(3) DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`ApiId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apis`
--

LOCK TABLES `apis` WRITE;
/*!40000 ALTER TABLE `apis` DISABLE KEYS */;
INSERT INTO `apis` VALUES ('19586f45-d0ed-4c97-b7fb-ee63c3dcd893',1,1,'5c5b9da3-eb70-43f3-bf2b-672dc43ff94c','20231222140017563','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-12-22 14:00:18','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:00.281'),('4b224771-8bea-4acc-a18e-4a207da0125e',1,1,'e11d2a32-cccc-43d6-9ee0-75457337214f','20240712163951976','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-07-12 16:39:52','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:20.815'),('4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1',1,1,'178c2287-cdb9-4aa5-b6d2-59ae39d6c2dc','20250318153419637','姚磊','1c7153cb-48bc-4134-b724-f4debfabe9ab','2025-03-18 15:34:20',NULL,NULL,NULL),('701c80a7-473a-4dd4-8449-3fe71e476714',1,1,'da798bf4-0a75-411f-b1da-a4050aa4d5d6','20240426233156463','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-26 23:31:56','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:08.741'),('7448fd78-311b-422e-a80d-48508b0a6c43',1,1,'01e7cb50-5f68-47eb-b5c2-f87ba335f244','20240108154027036','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2024-01-08 15:40:27','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:02.001'),('8208b1ec-9498-4b89-b568-58891e4ea19e',1,1,'1f3f31b3-1fb5-4f17-acff-44212dd519c9','20231204004738008','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-12-04 00:47:38','姚磊','1c7153cb-48bc-4134-b724-f4debfabe9ab','2025-04-19 23:03:10.261'),('a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb',1,1,'66fc0f77-1f17-427b-a9cb-25c117e00867','20231026031549942','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-26 03:15:50','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:30:55.575'),('aed8cc16-8e06-4b9a-87ac-4afed36cbfdc',1,1,'d1cbd53a-e2d4-4279-8492-5e7e77c54a27','20240430100541193','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-30 10:05:41','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:13.888'),('b42d7073-f79c-459b-831d-715bc65fd83e',1,1,'456a4ff0-ff36-4e1e-99c6-2bc3836643e7','20240426101017803','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-04-26 10:10:18','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:06.921'),('cbd670df-1478-42d2-a7e4-f4b90dd88c35',1,1,'ea8217f5-33b9-449c-ab04-5fb17351c649','20240624165752678','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-06-24 16:57:53','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-09-24 17:01:11.630'),('cd49b8d1-8535-4579-a4bc-76768a29e959',1,1,'8b2de592-94f3-4b13-b8a8-11ac40e40770','20240327175830619','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-03-27 17:58:31','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:31:03.878'),('f2465026-b9ca-40b9-86d1-c4396337f46f',1,1,'f78650e5-2c1d-4e0a-9fd6-8e89056c7cb2','20231122042848873','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-11-22 04:28:49','吴华','898bd563-9a55-43eb-af8e-697b13e2ec7c','2024-08-09 23:30:56.993');
/*!40000 ALTER TABLE `apis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apisservicemapping`
--

DROP TABLE IF EXISTS `apisservicemapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apisservicemapping` (
  `ServiceMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ServiceType` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务类型',
  `IsSecurityCert` bit(1) DEFAULT NULL COMMENT '是否加密',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息Id',
  PRIMARY KEY (`ServiceMappingId`) USING BTREE,
  KEY `IDX_ApisServiceMapping_ApiBaseId` (`ApiBaseId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API服务映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apisservicemapping`
--

LOCK TABLES `apisservicemapping` WRITE;
/*!40000 ALTER TABLE `apisservicemapping` DISABLE KEYS */;
INSERT INTO `apisservicemapping` VALUES ('01f4272d-b61a-4e20-80c5-16d2da734cbf','Https',NULL,'cb7316bd-c7a1-4a3a-9c65-466482dd7925'),('20f12e8b-9df7-4b0e-83be-941a887075da','Https',NULL,'28580b9b-1f33-4117-984b-bc6bf32d6676'),('40d62c7c-495c-4390-b3e3-45e84967765d','Https',NULL,'3d1bb54c-3e44-4f2f-9410-eadd50bb003c'),('62328463-e8c8-4866-b32b-2557ec0b3393','Https',NULL,'a4244630-f12b-40ec-93a8-9336efe2f320'),('6a0f5152-5487-4dff-b44b-87c751103294','Https',NULL,'a83ba6a6-e5f1-474f-a2a9-695e5849a4e9'),('7512ec1a-43bf-48fc-95b1-7c890eac05ce','Https',NULL,'b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('822bba29-1005-4ba0-ba82-52688dc5c21f','Https',NULL,'e64177a8-23e7-4bc6-926c-eb307d9bed1a'),('89c1e849-f1d1-48c6-8ccf-c8124bc6028a','Https',NULL,'86b15014-49d7-4beb-9edd-3dacabb2c600'),('8fa5e03b-0d18-4996-97ac-f702d62a56fc','Https',NULL,'d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('b002fed1-bb62-4523-a3b5-14fedeb59dde','Https',NULL,'9af5e62f-fbaa-42b1-8dfa-2b3661012f59'),('bad3e394-99af-49ed-af2d-ad38883a6a71','Https',NULL,'8adbff29-3a78-4dc8-bd20-d45ca0364c83'),('d73ec33d-8631-4be8-a024-da292bca96eb','Https',NULL,'a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db');
/*!40000 ALTER TABLE `apisservicemapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apisservicemappingdatabase`
--

DROP TABLE IF EXISTS `apisservicemappingdatabase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apisservicemappingdatabase` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务Id',
  `DbType` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据类型',
  `QuerySql` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '查询sql',
  `ServiceMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务映射主键Id',
  `Paging` bit(1) DEFAULT NULL COMMENT '是否分页',
  `Editable` bit(1) DEFAULT NULL COMMENT '是否可编辑',
  `OrderBy` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`MappingId`) USING BTREE,
  KEY `IDX_ApisServiceMappingDatabase_ServiceMappingId` (`ServiceMappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据库映射设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apisservicemappingdatabase`
--

LOCK TABLES `apisservicemappingdatabase` WRITE;
/*!40000 ALTER TABLE `apisservicemappingdatabase` DISABLE KEYS */;
/*!40000 ALTER TABLE `apisservicemappingdatabase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apisservicemappinghttp`
--

DROP TABLE IF EXISTS `apisservicemappinghttp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apisservicemappinghttp` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务Id',
  `RequestPath` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求路径',
  `RequestMethod` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求方法',
  `RequestModule` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求模式',
  `ServiceMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务映射主键Id',
  `ServiceApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务API关系ID',
  PRIMARY KEY (`MappingId`) USING BTREE,
  KEY `IDX_ApisServiceMappingHttp_ServiceMappingId` (`ServiceMappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API服务HTTP类型映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apisservicemappinghttp`
--

LOCK TABLES `apisservicemappinghttp` WRITE;
/*!40000 ALTER TABLE `apisservicemappinghttp` DISABLE KEYS */;
INSERT INTO `apisservicemappinghttp` VALUES ('0cab734c-8dff-4f78-8dca-a69d760db0f1','00000000-0000-0000-0000-000000000000','/todo-centre/v1/SFW/CallBackBS','Post','Mapping','6a0f5152-5487-4dff-b44b-87c751103294',NULL),('2bfcee8e-e078-4c65-93cd-156941f20da5','00000000-0000-0000-0000-000000000000','/process/v1/document-no/cancel-number','Post','Mapping','d73ec33d-8631-4be8-a024-da292bca96eb',NULL),('38028be6-528b-41dc-8cfc-fbe62d702a9c','00000000-0000-0000-0000-000000000000','/process/v1/webOffice/wrapheader/update-bookmark','Post','Mapping','40d62c7c-495c-4390-b3e3-45e84967765d',NULL),('4a871869-0760-41d0-a813-43f9ce674caf','00000000-0000-0000-0000-000000000000','/todo-centre/v1/SFW/PushLeave','Post','Mapping','89c1e849-f1d1-48c6-8ccf-c8124bc6028a',NULL),('63ae8bbb-f372-457e-baa4-2e0fe5f29205','00000000-0000-0000-0000-000000000000','/modeling/v1/common/business-object-data/integration-center','Post','Mapping','20f12e8b-9df7-4b0e-83be-941a887075da',NULL),('6d29c6d7-1e97-4a7a-84a9-a73df5ece569','00000000-0000-0000-0000-000000000000','/process/v1/hr/user-annual-leave/{user-id}','Get','Mapping','8fa5e03b-0d18-4996-97ac-f702d62a56fc',NULL),('8c8c158a-088a-4d85-aa44-1e2e3039b4fc','00000000-0000-0000-0000-000000000000','/process/v1/webOffice/wrapheader','Post','Mapping','01f4272d-b61a-4e20-80c5-16d2da734cbf',NULL),('a30c6f7e-5dde-412e-9979-e880c0b1ee71','00000000-0000-0000-0000-000000000000','/biz-logic/v1/instances/end','Post','Mapping','822bba29-1005-4ba0-ba82-52688dc5c21f',NULL),('bc5477da-72d8-4c4b-b502-aca2028909c0','00000000-0000-0000-0000-000000000000','/process/v1/instanceSendRecvRecord/addListByInstance','Post','Transparent','b002fed1-bb62-4523-a3b5-14fedeb59dde',NULL),('d7609428-1e60-481f-9a6d-a015d27c90e4','00000000-0000-0000-0000-000000000000','/process/v1/document-no/generate-number','Post','Mapping','62328463-e8c8-4866-b32b-2557ec0b3393',NULL),('defe81f3-4f4e-4904-b3b9-45800cde13f5','00000000-0000-0000-0000-000000000000','/todo-centre/v1/SFW/PushNews','Post','Mapping','7512ec1a-43bf-48fc-95b1-7c890eac05ce',NULL),('eacd81c7-151d-43c3-ba0f-cdb7a8ce513f','00000000-0000-0000-0000-000000000000','/process/v2/processes/structuredstorage','Post','Mapping','bad3e394-99af-49ed-af2d-ad38883a6a71',NULL);
/*!40000 ALTER TABLE `apisservicemappinghttp` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apisservicemappingmock`
--

DROP TABLE IF EXISTS `apisservicemappingmock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apisservicemappingmock` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `MockValue` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '值',
  `ServiceMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务映射主键Id',
  PRIMARY KEY (`MappingId`) USING BTREE,
  KEY `IDX_ApisServiceMappingMock_ServiceMappingId` (`ServiceMappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API服务Mock类型映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apisservicemappingmock`
--

LOCK TABLES `apisservicemappingmock` WRITE;
/*!40000 ALTER TABLE `apisservicemappingmock` DISABLE KEYS */;
/*!40000 ALTER TABLE `apisservicemappingmock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apisservicemappingwebservice`
--

DROP TABLE IF EXISTS `apisservicemappingwebservice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apisservicemappingwebservice` (
  `MappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务Id',
  `ServiceMethod` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务方法',
  `ServiceMappingId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务映射主键Id',
  `ServiceApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务API关系ID',
  `RequestMethod` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求方法',
  `RequestModule` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求模式',
  PRIMARY KEY (`MappingId`) USING BTREE,
  KEY `IDX_ApisServiceMappingWebService_ServiceMappingId` (`ServiceMappingId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API服务Webservice类型映射';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apisservicemappingwebservice`
--

LOCK TABLES `apisservicemappingwebservice` WRITE;
/*!40000 ALTER TABLE `apisservicemappingwebservice` DISABLE KEYS */;
/*!40000 ALTER TABLE `apisservicemappingwebservice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `apiupstreambodysample`
--

DROP TABLE IF EXISTS `apiupstreambodysample`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `apiupstreambodysample` (
  `InnerId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `RequestSampleType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '请求样例类型',
  `DataValue` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '样例值',
  `ApiBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'API基本信息主键ID',
  PRIMARY KEY (`InnerId`) USING BTREE,
  KEY `IDX_ApiUpstreamBodySample_ApiBaseId` (`ApiBaseId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='API上游请求体样例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `apiupstreambodysample`
--

LOCK TABLES `apiupstreambodysample` WRITE;
/*!40000 ALTER TABLE `apiupstreambodysample` DISABLE KEYS */;
INSERT INTO `apiupstreambodysample` VALUES ('2c19d8af-8b0e-4f19-97e3-453d5ebf3808','JSON','{\n    \"Number\": \"string\",\n    \"InstanceData\": {\n        \"Num\": \"string\"\n    }\n}','3d1bb54c-3e44-4f2f-9410-eadd50bb003c'),('33d95a96-93d6-4713-82b6-aabff4088b8e','JSON','{\n    \"Number\": \"string\"\n}','8adbff29-3a78-4dc8-bd20-d45ca0364c83'),('392592c1-bc09-4d5e-a557-d8f77b6387d2','JSON','{\n    \"Number\": \"string\",\n    \"Activities\": [\n        {\n            \"NodeId\": \"string\",\n            \"Name\": \"string\",\n            \"DetailStatus\": \"string\",\n            \"DetailStatusName\": \"string\",\n            \"Status\": \"string\",\n            \"StatusName\": \"string\"\n        }\n    ]\n}','cb7316bd-c7a1-4a3a-9c65-466482dd7925'),('4e52d910-daa7-4b36-97d2-620bb0925f42','JSON','{\n    \"Number\": \"string\",\n    \"ProcessId\": \"string\",\n    \"Activities\": []\n}','b86f0d57-507e-4f8d-9eb8-2b711a9ae1f1'),('5daaeb92-6590-41e1-a53a-3835879846ac','JSON','{\n    \"Number\": \"string\",\n    \"ProcessId\": \"string\"\n}','86b15014-49d7-4beb-9edd-3dacabb2c600'),('70539c12-ec6a-476c-9ef5-e7be76bc2058','JSON','{\n    \"page-index\": 0,\n    \"page-size\": 0,\n    \"PostName\": \"string\",\n    \"JobName\": \"string\",\n    \"CreateDate\": \"string\"\n}','28580b9b-1f33-4117-984b-bc6bf32d6676'),('70dd9501-88f4-4cf0-9e7a-35b42b10edce','JSON','{\n    \"Number\": \"string\",\n    \"BSID\": \"string\",\n    \"BTID\": \"string\",\n    \"BOID\": \"string\",\n    \"BusinessData\": \"string\",\n    \"InstanceExtension\": \"string\"\n}','a83ba6a6-e5f1-474f-a2a9-695e5849a4e9'),('7d792765-746a-486d-9deb-8c347b5d966c','JSON','{\n    \"Number\": \"string\",\n    \"BTID\": \"string\",\n    \"BusinessData\": [],\n    \"Status\": \"string\",\n    \"EndType\": 0\n}','e64177a8-23e7-4bc6-926c-eb307d9bed1a'),('8b81c7e5-94e9-4c26-9929-0939110fc4bb','JSON','{\n    \"userid\": \"string\"\n}','d361ec7d-8dce-4fd0-b6a2-37f5af7fe5e9'),('9c9b1503-5469-45fa-a067-296088e997eb','JSON','{\n    \"number\": \"string\",\n    \"ActivityId\": \"string\",\n    \"ProcessId\": \"string\"\n}','9af5e62f-fbaa-42b1-8dfa-2b3661012f59'),('bb0647a8-e8b6-4c17-a18c-583dc7d91e91','JSON','{\n    \"Number\": \"string\",\n    \"ProcessId\": \"string\"\n}','a4244630-f12b-40ec-93a8-9336efe2f320'),('f68aa7b0-cef5-4a4e-9b9f-203791a85533','JSON','{\n    \"ProcessId\": \"string\",\n    \"Status\": \"string\",\n    \"StartTime\": \"string\",\n    \"BusinessData\": {}\n}','a87ca611-3cad-4dc8-a2ed-3cfcfdf4f5db');
/*!40000 ALTER TABLE `apiupstreambodysample` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `applicationapis`
--

DROP TABLE IF EXISTS `applicationapis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `applicationapis` (
  `ApplicationAppId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'APIId',
  `ApiGroupId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '分组Id',
  `ExchangeReceiveId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交换ID',
  `ApplicationId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '应用ID',
  PRIMARY KEY (`ApplicationAppId`) USING BTREE,
  KEY `IDX_ApplicationApis_ApiId` (`ApiId`) USING BTREE,
  KEY `IDX_ApplicationApis_ApplicationId` (`ApplicationId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='应用API关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `applicationapis`
--

LOCK TABLES `applicationapis` WRITE;
/*!40000 ALTER TABLE `applicationapis` DISABLE KEYS */;
INSERT INTO `applicationapis` VALUES ('0008bccf-2fc0-48a7-b73c-3a83cdebf3ac','cbd670df-1478-42d2-a7e4-f4b90dd88c35',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('2a478f38-af97-4a3a-9c87-9edeca4a0945','cd49b8d1-8535-4579-a4bc-76768a29e959',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('2d075b0c-d988-4d15-98b0-bdd1bf447000','19586f45-d0ed-4c97-b7fb-ee63c3dcd893',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('4528f0ca-6d53-49e2-8a80-96493fc47705','a711daf2-6fdd-4bd0-b6eb-a7cb59e72dcb',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('74895644-7595-43ca-a9d4-d41b00aa1bab','4b224771-8bea-4acc-a18e-4a207da0125e',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('7ea999ae-203a-4afa-9483-c7c61b77e340','8208b1ec-9498-4b89-b568-58891e4ea19e',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('80995163-d492-49b1-8ba0-53ad7bba155c','4ca9f22b-ec6f-4ea1-b1ea-e8227a58cce1',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('80fe84fd-8c68-4d2f-9082-43c1ed4c55dc','701c80a7-473a-4dd4-8449-3fe71e476714',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('af299359-4d79-4081-b776-ecdd2289e409','7448fd78-311b-422e-a80d-48508b0a6c43',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('ccc32022-8496-476f-a4f5-39a425222f9b','aed8cc16-8e06-4b9a-87ac-4afed36cbfdc',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('e3081470-b0ed-4ed7-8097-afbef202dde7','f2465026-b9ca-40b9-86d1-c4396337f46f',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db'),('f9cb8955-843a-4709-9b71-7cdf1aab5dda','b42d7073-f79c-459b-831d-715bc65fd83e',NULL,NULL,'1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db');
/*!40000 ALTER TABLE `applicationapis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `applications`
--

DROP TABLE IF EXISTS `applications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `applications` (
  `ApplicationId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `AppName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '应用名称',
  `AppCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '应用code',
  `Password` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '授权密码',
  `Description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人Id',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人Id',
  `ModifyDate` datetime(3) DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`ApplicationId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='应用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `applications`
--

LOCK TABLES `applications` WRITE;
/*!40000 ALTER TABLE `applications` DISABLE KEYS */;
INSERT INTO `applications` VALUES ('1dbd2b35-d05e-44f6-a8e8-1f7e1e21c9db','bpm','bpm','H0YRieuuOZ4Ec68lUvyrgQ==',NULL,'管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-26 03:16:34.288','管理员','e8575564-17e8-4d9a-8ab3-e055c366dc62','2023-10-27 04:50:51.708');
/*!40000 ALTER TABLE `applications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `databases`
--

DROP TABLE IF EXISTS `databases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `databases` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `Name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库名称',
  `Description` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库描述',
  `Account` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库登录账号',
  `Password` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库登录密码',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `UpdateDate` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `CreateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `UpdateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
  `Localhost` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库地址',
  `Type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库类型 SqlServer，MySql，Oracle',
  `Remarks` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '备注',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据库管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `databases`
--

LOCK TABLES `databases` WRITE;
/*!40000 ALTER TABLE `databases` DISABLE KEYS */;
/*!40000 ALTER TABLE `databases` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `datatablecolumns`
--

DROP TABLE IF EXISTS `datatablecolumns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `datatablecolumns` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `Name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '字段名称',
  `Description` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '字段描述',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `UpdateDate` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `CreateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `UpdateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
  `Remarks` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '备注',
  `Length` int DEFAULT NULL COMMENT '字段长度',
  `Type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '字段类型',
  `IsPrimaryKey` tinyint DEFAULT NULL COMMENT '字段是否主键',
  `IsNullable` tinyint DEFAULT NULL COMMENT '字段是否允许空值',
  `DataTableId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据表Id',
  `Decimal` int DEFAULT NULL COMMENT '小数点',
  `OldColumnName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '原字段名称',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IDX_DataTableColumns_DataTableId` (`DataTableId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据表列';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `datatablecolumns`
--

LOCK TABLES `datatablecolumns` WRITE;
/*!40000 ALTER TABLE `datatablecolumns` DISABLE KEYS */;
/*!40000 ALTER TABLE `datatablecolumns` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `datatablerelateds`
--

DROP TABLE IF EXISTS `datatablerelateds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `datatablerelateds` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `DataBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库Id',
  `MainDataTableId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主表Id',
  `MainDataTableColumnId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主表列Id',
  `RelatedDataTableId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '关联表表Id',
  `RelatedDataTableColumnId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '关联表列Id',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `UpdateDate` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `CreateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `UpdateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
  `RelatedType` int NOT NULL COMMENT '关联类型 1:一对一 2:一对多',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IDX_DataTableRelateds_DataBaseId` (`DataBaseId`) USING BTREE,
  KEY `IDX_DataTableRelateds_MainDataTableColumnId` (`MainDataTableColumnId`) USING BTREE,
  KEY `IDX_DataTableRelateds_MainDataTableId` (`MainDataTableId`) USING BTREE,
  KEY `IDX_DataTableRelateds_RelatedDataTableColumnId` (`RelatedDataTableColumnId`) USING BTREE,
  KEY `IDX_DataTableRelateds_RelatedDataTableId` (`RelatedDataTableId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据表和列关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `datatablerelateds`
--

LOCK TABLES `datatablerelateds` WRITE;
/*!40000 ALTER TABLE `datatablerelateds` DISABLE KEYS */;
/*!40000 ALTER TABLE `datatablerelateds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `datatables`
--

DROP TABLE IF EXISTS `datatables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `datatables` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键',
  `Name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据表名称',
  `Description` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据表描述',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `UpdateDate` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `CreateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `UpdateUser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
  `Remarks` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '备注',
  `DataBaseId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '数据库管理Id',
  `Status` int NOT NULL COMMENT '状态 0：未同步 1:已同步',
  `VersionNo` int NOT NULL COMMENT '版本号',
  PRIMARY KEY (`Id`) USING BTREE,
  KEY `IDX_DataTables_DataBaseId` (`DataBaseId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据库表信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `datatables`
--

LOCK TABLES `datatables` WRITE;
/*!40000 ALTER TABLE `datatables` DISABLE KEYS */;
/*!40000 ALTER TABLE `datatables` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel`
--

DROP TABLE IF EXISTS `dt_exchangemodel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel` (
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `ModelCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModelName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `CreateUserId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CreateDate` datetime(3) DEFAULT NULL,
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModifyUserId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModifyDate` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`ExchangeModelId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel`
--

LOCK TABLES `dt_exchangemodel` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel_distribution`
--

DROP TABLE IF EXISTS `dt_exchangemodel_distribution`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel_distribution` (
  `DistributionId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Dispenser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DistributionSource` int DEFAULT NULL,
  `DistributionApp` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DistributionAppCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DistributionAPI` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ApiId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Remark` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PreProcess` bit(1) DEFAULT NULL,
  `ProcessMethod` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PreAPI` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DotNetCode` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `ExchangeModelId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `IsPaging` bit(1) DEFAULT NULL,
  `PageSize` int DEFAULT NULL,
  `NoticeApi` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `NoticeApiId` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`DistributionId`) USING BTREE,
  KEY `RefDT_ExchangeModel12` (`ExchangeModelId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel_distribution`
--

LOCK TABLES `dt_exchangemodel_distribution` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel_distribution` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel_distribution` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel_distributionfilter`
--

DROP TABLE IF EXISTS `dt_exchangemodel_distributionfilter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel_distributionfilter` (
  `DistributionFilterId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `PropertyName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PropertyType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '属性类型，包含String,Number,Date',
  `PropertyDescription` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `FilterValue` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Judge` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Sequence` int DEFAULT NULL,
  `DistributionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`DistributionFilterId`) USING BTREE,
  KEY `IDX_DT_ExchangeModel_DistributionFilter_DistributionId` (`DistributionId`) USING BTREE,
  KEY `IDX_DT_ExchangeModel_DistributionFilter_ExchangeModelId` (`ExchangeModelId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel_distributionfilter`
--

LOCK TABLES `dt_exchangemodel_distributionfilter` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel_distributionfilter` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel_distributionfilter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel_receive`
--

DROP TABLE IF EXISTS `dt_exchangemodel_receive`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel_receive` (
  `ReceiveId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Receiver` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ReceivingMode` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ApiId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Api` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ReceiveApi` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Pagination` bit(1) DEFAULT NULL,
  `PageSize` int DEFAULT NULL,
  `RequestPolicy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PolicyExpression` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PreProcess` bit(1) DEFAULT NULL,
  `ProcessMethod` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PreAPI` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DotNetCode` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `Remark` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ReceiveId`) USING BTREE,
  KEY `IDX_DT_ExchangeModel_Receive_ExchangeModelId` (`ExchangeModelId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel_receive`
--

LOCK TABLES `dt_exchangemodel_receive` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel_receive` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel_receive` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel_receivefilter`
--

DROP TABLE IF EXISTS `dt_exchangemodel_receivefilter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel_receivefilter` (
  `ReceiveFilterId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `PropertyName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `PropertyType` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '属性类型，包含String,Number,Date',
  `PropertyDescription` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `FilterValue` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Judge` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Sequence` int DEFAULT NULL,
  `ReceiveId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ReceiveFilterId`) USING BTREE,
  KEY `IDX_DT_ExchangeModel_ReceiveFilter_ExchangeModelId` (`ExchangeModelId`) USING BTREE,
  KEY `IDX_DT_ExchangeModel_ReceiveFilter_ReceiveId` (`ReceiveId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel_receivefilter`
--

LOCK TABLES `dt_exchangemodel_receivefilter` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel_receivefilter` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel_receivefilter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodel_receiveparam`
--

DROP TABLE IF EXISTS `dt_exchangemodel_receiveparam`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodel_receiveparam` (
  `ReceiveParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `ParamLocation` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ParamName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ParamValue` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ParamDescription` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Sequence` int DEFAULT NULL,
  `ReceiveId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `AttributeType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DataType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ReceiveParamId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='数据交换模型_数据接收配置_入参';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodel_receiveparam`
--

LOCK TABLES `dt_exchangemodel_receiveparam` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodel_receiveparam` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodel_receiveparam` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dt_exchangemodelstatus`
--

DROP TABLE IF EXISTS `dt_exchangemodelstatus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dt_exchangemodelstatus` (
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `CurrentTaskNumber` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CurrentSubTaskNumber` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CurrentTaskStage` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CurrentTaskStatus` int DEFAULT NULL,
  `ErrMsg` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  PRIMARY KEY (`ExchangeModelId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dt_exchangemodelstatus`
--

LOCK TABLES `dt_exchangemodelstatus` WRITE;
/*!40000 ALTER TABLE `dt_exchangemodelstatus` DISABLE KEYS */;
/*!40000 ALTER TABLE `dt_exchangemodelstatus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `log_dataexchange`
--

DROP TABLE IF EXISTS `log_dataexchange`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `log_dataexchange` (
  `LogId` bigint NOT NULL AUTO_INCREMENT,
  `TaskNumber` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `SubTaskNumber` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DistributeSubTaskNumber` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `TaskStage` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `TaskStatus` int DEFAULT NULL,
  `ExecutionTime` datetime(3) DEFAULT NULL,
  `ModelCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModeName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ExchangeModelId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Receiver` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ReceiveId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Dispenser` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DistributionId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ErrMsg` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `DataMsg` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `RetryCount` int DEFAULT NULL,
  PRIMARY KEY (`LogId`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `log_dataexchange`
--

LOCK TABLES `log_dataexchange` WRITE;
/*!40000 ALTER TABLE `log_dataexchange` DISABLE KEYS */;
/*!40000 ALTER TABLE `log_dataexchange` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `serviceapis`
--

DROP TABLE IF EXISTS `serviceapis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `serviceapis` (
  `ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `ApiCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ApiName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ApiUrl` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Status` int DEFAULT NULL,
  `Remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CreatorID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CreatorName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CreateDate` datetime(3) DEFAULT NULL,
  `ModifierID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModifierName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModifyDate` datetime(3) DEFAULT NULL,
  `RequestMethod` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DataFormat` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `serviceapis`
--

LOCK TABLES `serviceapis` WRITE;
/*!40000 ALTER TABLE `serviceapis` DISABLE KEYS */;
/*!40000 ALTER TABLE `serviceapis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `serviceauthparam`
--

DROP TABLE IF EXISTS `serviceauthparam`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `serviceauthparam` (
  `ParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '参数Id',
  `ParamName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数名称',
  `ParamLocation` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数位置',
  `DataType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '参数类型',
  `Required` bit(1) DEFAULT NULL COMMENT '是否必填',
  `Description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
  `MappingType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射类型',
  `MappingValue` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '映射值',
  `Sequencing` int DEFAULT NULL COMMENT '排序位置',
  `UpperParamId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父级参数Id',
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务Id',
  PRIMARY KEY (`ParamId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='服务认证参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `serviceauthparam`
--

LOCK TABLES `serviceauthparam` WRITE;
/*!40000 ALTER TABLE `serviceauthparam` DISABLE KEYS */;
INSERT INTO `serviceauthparam` VALUES ('bc35d94d-5961-48a0-1fe8-ae96769fc419','appKey','Header','string',NULL,NULL,'ApiToken','Key',1,NULL,'3d36f394-9ec3-4d4b-97e2-fd7f5f8f6aaf'),('e42136a7-2891-5ef6-983f-5e421585693b','sign','Header','string',NULL,NULL,'ApiToken','Sign',2,NULL,'3d36f394-9ec3-4d4b-97e2-fd7f5f8f6aaf');
/*!40000 ALTER TABLE `serviceauthparam` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `ServiceType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `HealthInterval` int DEFAULT NULL,
  `HealthIntervalUnit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `HealthTimeout` int DEFAULT NULL,
  `HealthTimeoutUnit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `HeartbeatStatus` int DEFAULT NULL,
  `AuthenticationMode` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `AuthenticationValue` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `Status` int DEFAULT NULL,
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `CreateDate` datetime(3) DEFAULT NULL,
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ModifyDate` datetime(3) DEFAULT NULL,
  `StrategyId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `TokenApi` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Ids4ClientId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Ids4ClientSecret` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Signkey` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `SignSecret` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ServiceId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `servicesdatabase`
--

DROP TABLE IF EXISTS `servicesdatabase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `servicesdatabase` (
  `ConnConfId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `DbHost` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DbPort` int DEFAULT NULL,
  `UserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Password` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `DbName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ConnConfId`) USING BTREE,
  KEY `IDX_ServicesDatabase_ServiceId` (`ServiceId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `servicesdatabase`
--

LOCK TABLES `servicesdatabase` WRITE;
/*!40000 ALTER TABLE `servicesdatabase` DISABLE KEYS */;
INSERT INTO `servicesdatabase` VALUES ('01423b61-cca1-4216-8a53-67eb5ad2691d','**************',NULL,'root','abc@123','lowcode','11d45edd-9c95-4d39-8342-74c2d4105783'),('047a452a-389c-464f-b576-324630eaac5c','**************',NULL,'root','abc@123','lowcode','42ce5cf1-791e-4230-8253-5ab149cec2da'),('072e4a71-8185-4e45-9822-180a74e7376f','*************',NULL,'sa','sa123','SchaefflerPMT_APAC','f5952b81-9ce4-4b0f-bf03-763c3e1718d5'),('39b84164-93d2-49c2-af9d-91a28b7c931a','23',NULL,'23','23','23','a92dd5e1-41a3-43ea-8f5a-fe8ccf91f25e'),('6eb0d76a-fb37-4bd7-b232-bfea57fa4460','**************',NULL,'root','abc@123','lowcode','b69ab00a-f5ac-4edc-a55c-e4a638ade403'),('8857d2f4-ec18-417b-b746-0419316c8a7f','172.20.138.48',NULL,'root','abc@123','Boost','7773034f-2fba-45cc-a487-bb64a92ad989'),('d4bb5c2a-cd91-4404-89a0-0bf28b275a12','*************',NULL,'sa','sa123','SchaefflerPMT_Dev','9e5c69f3-31f7-4d07-bff1-526566404323');
/*!40000 ALTER TABLE `servicesdatabase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `serviceshttps`
--

DROP TABLE IF EXISTS `serviceshttps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `serviceshttps` (
  `ConnConfId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `HostUrl` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ConnConfId`) USING BTREE,
  KEY `IDX_ServicesHttps_ServiceId` (`ServiceId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `serviceshttps`
--

LOCK TABLES `serviceshttps` WRITE;
/*!40000 ALTER TABLE `serviceshttps` DISABLE KEYS */;
/*!40000 ALTER TABLE `serviceshttps` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `serviceswebservice`
--

DROP TABLE IF EXISTS `serviceswebservice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `serviceswebservice` (
  `ConnConfId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `ServiceId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`ConnConfId`) USING BTREE,
  KEY `IDX_ServicesWebService_ServiceId` (`ServiceId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `serviceswebservice`
--

LOCK TABLES `serviceswebservice` WRITE;
/*!40000 ALTER TABLE `serviceswebservice` DISABLE KEYS */;
/*!40000 ALTER TABLE `serviceswebservice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `test_distribution`
--

DROP TABLE IF EXISTS `test_distribution`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `test_distribution` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Data` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `CreateDate` datetime DEFAULT NULL,
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `test_distribution`
--

LOCK TABLES `test_distribution` WRITE;
/*!40000 ALTER TABLE `test_distribution` DISABLE KEYS */;
/*!40000 ALTER TABLE `test_distribution` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `UserId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `UserAccount` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `UserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `Password` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci,
  `Creator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `CreatorId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人Id',
  `CreateDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `Modifier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人',
  `ModifierId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '修改人Id',
  `ModifyDate` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`UserId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('3B8F79AB-42C5-4AC3-931F-4596EC257938','admin','admin','Ba1sWYSECmi5Cg5X2MZKJg==',NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `v_applicationapireference`
--

DROP TABLE IF EXISTS `v_applicationapireference`;
/*!50001 DROP VIEW IF EXISTS `v_applicationapireference`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_applicationapireference` AS SELECT 
 1 AS `Id`,
 1 AS `ApiId`,
 1 AS `ApplicationId`,
 1 AS `ServiceId`,
 1 AS `ServiceCode`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_backendapireference`
--

DROP TABLE IF EXISTS `v_backendapireference`;
/*!50001 DROP VIEW IF EXISTS `v_backendapireference`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_backendapireference` AS SELECT 
 1 AS `ApiId`,
 1 AS `ApiStatus`,
 1 AS `ServiceType`,
 1 AS `ServiceId`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `workflow_designs`
--

DROP TABLE IF EXISTS `workflow_designs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_designs` (
  `Id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '流程ID',
  `FlowName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程名',
  `FlowJsonForDesign` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '流程图json（画图的json）',
  `FlowJosnForEngine` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '流程图json（引擎运行的版本）',
  `CreatDate` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `CreateUser` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `UpdateDate` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `UpdateUser` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`Id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_designs`
--

LOCK TABLES `workflow_designs` WRITE;
/*!40000 ALTER TABLE `workflow_designs` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_designs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `v_applicationapireference`
--

/*!50001 DROP VIEW IF EXISTS `v_applicationapireference`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `v_applicationapireference` AS select uuid() AS `Id`,`a`.`ApiId` AS `ApiId`,`aa`.`ApplicationId` AS `ApplicationId`,`asmh`.`ServiceId` AS `ServiceId`,`s`.`ServiceCode` AS `ServiceCode` from (((((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappinghttp` `asmh` on((`asm`.`ServiceMappingId` = `asmh`.`ServiceMappingId`))) join `services` `s` on((`s`.`ServiceId` = `asmh`.`ServiceId`))) join `applicationapis` `aa` on((`aa`.`ApiId` = `a`.`ApiId`))) union select uuid() AS `Id`,`a`.`ApiId` AS `ApiId`,`aa`.`ApplicationId` AS `ApplicationId`,`asmd`.`ServiceId` AS `ServiceId`,`s`.`ServiceCode` AS `ServiceCode` from (((((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappingdatabase` `asmd` on((`asm`.`ServiceMappingId` = `asmd`.`ServiceMappingId`))) join `services` `s` on((`s`.`ServiceId` = `asmd`.`ServiceId`))) join `applicationapis` `aa` on((`aa`.`ApiId` = `a`.`ApiId`))) union select uuid() AS `Id`,`a`.`ApiId` AS `ApiId`,`aa`.`ApplicationId` AS `ApplicationId`,`asmws`.`ServiceId` AS `ServiceId`,`s`.`ServiceCode` AS `ServiceCode` from (((((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappingwebservice` `asmws` on((`asm`.`ServiceMappingId` = `asmws`.`ServiceMappingId`))) join `services` `s` on((`s`.`ServiceId` = `asmws`.`ServiceId`))) join `applicationapis` `aa` on((`aa`.`ApiId` = `a`.`ApiId`))) union select uuid() AS `Id`,`a`.`ApiId` AS `ApiId`,`aa`.`ApplicationId` AS `ApplicationId`,NULL AS `NULL`,'Mock' AS `Mock` from ((((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappingmock` `asmws` on((`asm`.`ServiceMappingId` = `asmws`.`ServiceMappingId`))) join `applicationapis` `aa` on((`aa`.`ApiId` = `a`.`ApiId`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_backendapireference`
--

/*!50001 DROP VIEW IF EXISTS `v_backendapireference`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `v_backendapireference` AS select `a`.`ApiId` AS `ApiId`,`a`.`Status` AS `ApiStatus`,`asm`.`ServiceType` AS `ServiceType`,`asmh`.`ServiceId` AS `ServiceId` from (((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappinghttp` `asmh` on((`asm`.`ServiceMappingId` = `asmh`.`ServiceMappingId`))) union select `a`.`ApiId` AS `ApiId`,`a`.`Status` AS `ApiStatus`,`asm`.`ServiceType` AS `ServiceType`,`asmd`.`ServiceId` AS `ServiceId` from (((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappingdatabase` `asmd` on((`asm`.`ServiceMappingId` = `asmd`.`ServiceMappingId`))) union select `a`.`ApiId` AS `ApiId`,`a`.`Status` AS `ApiStatus`,`asm`.`ServiceType` AS `ServiceType`,`asmws`.`ServiceId` AS `ServiceId` from (((`apis` `a` join `apibase` `ab` on((`a`.`ApiId` = `ab`.`ApiId`))) join `apisservicemapping` `asm` on((`ab`.`ApiBaseId` = `asm`.`ApiBaseId`))) join `apisservicemappingwebservice` `asmws` on((`asm`.`ServiceMappingId` = `asmws`.`ServiceMappingId`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-05 17:08:23
