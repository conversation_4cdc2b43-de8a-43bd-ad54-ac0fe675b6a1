#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL批量导入工具
支持MySQL、PostgreSQL、SQL Server等数据库
支持批量导入SQL文件，带有错误处理和日志记录
"""

import os
import sys
import glob
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import configparser

# 数据库连接库
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

try:
    import pyodbc
    SQLSERVER_AVAILABLE = True
except ImportError:
    SQLSERVER_AVAILABLE = False

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False


class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self, db_type: str, **kwargs):
        self.db_type = db_type.lower()
        self.host = kwargs.get('host', 'localhost')
        self.port = kwargs.get('port')
        self.database = kwargs.get('database')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.charset = kwargs.get('charset', 'utf8mb4')
        self.connection_string = kwargs.get('connection_string')
        
        # 设置默认端口
        if not self.port:
            if self.db_type == 'mysql':
                self.port = 3306
            elif self.db_type == 'postgresql':
                self.port = 5432
            elif self.db_type == 'sqlserver':
                self.port = 1433


class SQLBatchImporter:
    """SQL批量导入器"""
    
    def __init__(self, config: DatabaseConfig, log_level: str = 'INFO'):
        self.config = config
        self.connection = None
        self.cursor = None
        
        # 设置日志
        self.setup_logging(log_level)
        
    def setup_logging(self, log_level: str):
        """设置日志配置"""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            handlers=[
                logging.FileHandler(f'sql_import_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """连接数据库"""
        try:
            if self.config.db_type == 'mysql':
                if not MYSQL_AVAILABLE:
                    raise ImportError("pymysql库未安装，请运行: pip install pymysql")
                
                self.connection = pymysql.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    charset=self.config.charset,
                    autocommit=False
                )
                
            elif self.config.db_type == 'postgresql':
                if not POSTGRESQL_AVAILABLE:
                    raise ImportError("psycopg2库未安装，请运行: pip install psycopg2-binary")
                
                self.connection = psycopg2.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database
                )
                self.connection.autocommit = False
                
            elif self.config.db_type == 'sqlserver':
                if not SQLSERVER_AVAILABLE:
                    raise ImportError("pyodbc库未安装，请运行: pip install pyodbc")
                
                if self.config.connection_string:
                    self.connection = pyodbc.connect(self.config.connection_string)
                else:
                    conn_str = (
                        f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                        f"SERVER={self.config.host},{self.config.port};"
                        f"DATABASE={self.config.database};"
                        f"UID={self.config.username};"
                        f"PWD={self.config.password}"
                    )
                    self.connection = pyodbc.connect(conn_str)
                self.connection.autocommit = False
                
            elif self.config.db_type == 'sqlite':
                if not SQLITE_AVAILABLE:
                    raise ImportError("sqlite3库未安装")
                
                self.connection = sqlite3.connect(self.config.database)
                self.connection.execute("PRAGMA foreign_keys = ON")
                
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.db_type}")
            
            self.cursor = self.connection.cursor()
            self.logger.info(f"成功连接到{self.config.db_type}数据库")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            self.logger.info("数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭数据库连接时出错: {str(e)}")
    
    def read_sql_file(self, file_path: str) -> Optional[str]:
        """读取SQL文件内容"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        if content:
                            self.logger.debug(f"使用{encoding}编码成功读取文件: {file_path}")
                            return content
                except UnicodeDecodeError:
                    continue
            
            self.logger.error(f"无法读取文件 {file_path}，尝试了所有编码都失败")
            return None
            
        except Exception as e:
            self.logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
            return None
    
    def split_sql_statements(self, sql_content: str) -> List[str]:
        """分割SQL语句"""
        # 简单的SQL语句分割，按分号分割
        statements = []
        current_statement = ""
        in_string = False
        string_char = None
        
        i = 0
        while i < len(sql_content):
            char = sql_content[i]
            
            if not in_string:
                if char in ("'", '"'):
                    in_string = True
                    string_char = char
                elif char == ';':
                    if current_statement.strip():
                        statements.append(current_statement.strip())
                    current_statement = ""
                    i += 1
                    continue
            else:
                if char == string_char:
                    # 检查是否是转义的引号
                    if i + 1 < len(sql_content) and sql_content[i + 1] == string_char:
                        current_statement += char + sql_content[i + 1]
                        i += 2
                        continue
                    else:
                        in_string = False
                        string_char = None
            
            current_statement += char
            i += 1
        
        # 添加最后一个语句
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        return statements

    def execute_sql_statement(self, statement: str) -> Tuple[bool, Optional[str]]:
        """执行单个SQL语句"""
        try:
            self.cursor.execute(statement)
            return True, None
        except Exception as e:
            error_msg = f"SQL执行失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def import_sql_file(self, file_path: str, continue_on_error: bool = True) -> Dict:
        """导入单个SQL文件"""
        result = {
            'file': file_path,
            'success': False,
            'total_statements': 0,
            'executed_statements': 0,
            'failed_statements': 0,
            'errors': []
        }

        self.logger.info(f"开始导入文件: {file_path}")

        # 读取文件内容
        sql_content = self.read_sql_file(file_path)
        if not sql_content:
            result['errors'].append("无法读取文件内容")
            return result

        # 分割SQL语句
        statements = self.split_sql_statements(sql_content)
        result['total_statements'] = len(statements)

        if not statements:
            result['errors'].append("文件中没有找到有效的SQL语句")
            return result

        # 开始事务
        try:
            for i, statement in enumerate(statements, 1):
                if not statement.strip():
                    continue

                self.logger.debug(f"执行语句 {i}/{len(statements)}")
                success, error = self.execute_sql_statement(statement)

                if success:
                    result['executed_statements'] += 1
                else:
                    result['failed_statements'] += 1
                    result['errors'].append(f"语句 {i}: {error}")

                    if not continue_on_error:
                        self.logger.error(f"遇到错误，停止执行文件: {file_path}")
                        self.connection.rollback()
                        return result

            # 提交事务
            self.connection.commit()
            result['success'] = True
            self.logger.info(f"文件导入完成: {file_path} "
                           f"(成功: {result['executed_statements']}, "
                           f"失败: {result['failed_statements']})")

        except Exception as e:
            self.connection.rollback()
            error_msg = f"事务执行失败: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg)

        return result

    def import_multiple_files(self, file_patterns: List[str],
                            continue_on_error: bool = True,
                            file_order: Optional[List[str]] = None) -> Dict:
        """批量导入多个SQL文件"""
        all_results = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'file_results': [],
            'summary': {}
        }

        # 收集所有匹配的文件
        all_files = []
        for pattern in file_patterns:
            matched_files = glob.glob(pattern)
            all_files.extend(matched_files)

        # 去重并排序
        all_files = list(set(all_files))

        # 如果指定了文件顺序，按顺序排列
        if file_order:
            ordered_files = []
            remaining_files = all_files.copy()

            for ordered_file in file_order:
                for file_path in remaining_files:
                    if os.path.basename(file_path) == ordered_file or file_path == ordered_file:
                        ordered_files.append(file_path)
                        remaining_files.remove(file_path)
                        break

            # 添加剩余文件
            ordered_files.extend(sorted(remaining_files))
            all_files = ordered_files
        else:
            all_files.sort()

        all_results['total_files'] = len(all_files)

        if not all_files:
            self.logger.warning("没有找到匹配的SQL文件")
            return all_results

        self.logger.info(f"找到 {len(all_files)} 个SQL文件，开始批量导入")

        # 逐个导入文件
        for file_path in all_files:
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                continue

            result = self.import_sql_file(file_path, continue_on_error)
            all_results['file_results'].append(result)

            if result['success']:
                all_results['successful_files'] += 1
            else:
                all_results['failed_files'] += 1

                if not continue_on_error:
                    self.logger.error("遇到错误，停止批量导入")
                    break

        # 生成汇总信息
        total_statements = sum(r['total_statements'] for r in all_results['file_results'])
        executed_statements = sum(r['executed_statements'] for r in all_results['file_results'])
        failed_statements = sum(r['failed_statements'] for r in all_results['file_results'])

        all_results['summary'] = {
            'total_statements': total_statements,
            'executed_statements': executed_statements,
            'failed_statements': failed_statements,
            'success_rate': f"{(executed_statements/total_statements*100):.2f}%" if total_statements > 0 else "0%"
        }

        self.logger.info(f"批量导入完成 - 文件: {all_results['successful_files']}/{all_results['total_files']} "
                        f"语句: {executed_statements}/{total_statements}")

        return all_results


def load_config_from_file(config_file: str) -> DatabaseConfig:
    """从配置文件加载数据库配置"""
    config = configparser.ConfigParser()
    config.read(config_file, encoding='utf-8')

    if 'database' not in config:
        raise ValueError("配置文件中缺少 [database] 部分")

    db_section = config['database']

    return DatabaseConfig(
        db_type=db_section.get('type', 'mysql'),
        host=db_section.get('host', 'localhost'),
        port=db_section.getint('port', None),
        database=db_section.get('database'),
        username=db_section.get('username'),
        password=db_section.get('password'),
        charset=db_section.get('charset', 'utf8mb4'),
        connection_string=db_section.get('connection_string')
    )


def create_sample_config():
    """创建示例配置文件"""
    config_content = """[database]
# 数据库类型: mysql, postgresql, sqlserver, sqlite
type = mysql

# 数据库连接信息
host = localhost
port = 3306
database = your_database_name
username = your_username
password = your_password
charset = utf8mb4

# 对于SQL Server，也可以使用连接字符串
# connection_string = DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost,1433;DATABASE=your_db;UID=user;PWD=pass

# 对于SQLite，只需要指定数据库文件路径
# database = /path/to/your/database.db
"""

    with open('db_config.ini', 'w', encoding='utf-8') as f:
        f.write(config_content)

    print("已创建示例配置文件: db_config.ini")
    print("请编辑配置文件中的数据库连接信息")


def print_results(results: Dict):
    """打印导入结果"""
    print("\n" + "="*60)
    print("SQL批量导入结果汇总")
    print("="*60)

    print(f"总文件数: {results['total_files']}")
    print(f"成功文件数: {results['successful_files']}")
    print(f"失败文件数: {results['failed_files']}")

    if 'summary' in results:
        summary = results['summary']
        print(f"总SQL语句数: {summary['total_statements']}")
        print(f"成功执行: {summary['executed_statements']}")
        print(f"执行失败: {summary['failed_statements']}")
        print(f"成功率: {summary['success_rate']}")

    print("\n详细结果:")
    print("-"*60)

    for result in results['file_results']:
        status = "✓" if result['success'] else "✗"
        print(f"{status} {result['file']} "
              f"({result['executed_statements']}/{result['total_statements']})")

        if result['errors']:
            for error in result['errors'][:3]:  # 只显示前3个错误
                print(f"    错误: {error}")
            if len(result['errors']) > 3:
                print(f"    ... 还有 {len(result['errors']) - 3} 个错误")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SQL批量导入工具')
    parser.add_argument('files', nargs='*', help='要导入的SQL文件路径或通配符模式')
    parser.add_argument('-c', '--config', default='db_config.ini',
                       help='数据库配置文件路径 (默认: db_config.ini)')
    parser.add_argument('--create-config', action='store_true',
                       help='创建示例配置文件')
    parser.add_argument('--continue-on-error', action='store_true', default=True,
                       help='遇到错误时继续执行 (默认: True)')
    parser.add_argument('--stop-on-error', action='store_true',
                       help='遇到错误时停止执行')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--order', help='指定文件执行顺序，用逗号分隔文件名')

    # 数据库连接参数
    parser.add_argument('--db-type', choices=['mysql', 'postgresql', 'sqlserver', 'sqlite'],
                       help='数据库类型')
    parser.add_argument('--host', help='数据库主机')
    parser.add_argument('--port', type=int, help='数据库端口')
    parser.add_argument('--database', help='数据库名称')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--password', help='密码')

    args = parser.parse_args()

    # 创建配置文件
    if args.create_config:
        create_sample_config()
        return

    # 如果没有指定文件，使用当前目录下的所有.sql文件
    if not args.files:
        args.files = ['*.sql']

    try:
        # 加载数据库配置
        if any([args.db_type, args.host, args.database, args.username]):
            # 使用命令行参数
            config = DatabaseConfig(
                db_type=args.db_type or 'mysql',
                host=args.host or 'localhost',
                port=args.port,
                database=args.database,
                username=args.username,
                password=args.password
            )
        else:
            # 从配置文件加载
            if not os.path.exists(args.config):
                print(f"配置文件不存在: {args.config}")
                print("请使用 --create-config 创建示例配置文件，或使用命令行参数指定数据库连接信息")
                return
            config = load_config_from_file(args.config)

        # 处理文件执行顺序
        file_order = None
        if args.order:
            file_order = [f.strip() for f in args.order.split(',')]

        # 创建导入器并执行
        importer = SQLBatchImporter(config, args.log_level)

        if not importer.connect():
            print("数据库连接失败，请检查配置")
            return

        try:
            continue_on_error = args.continue_on_error and not args.stop_on_error
            results = importer.import_multiple_files(
                args.files,
                continue_on_error=continue_on_error,
                file_order=file_order
            )

            print_results(results)

        finally:
            importer.disconnect()

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
