/*
 Navicat Premium Dump SQL

 Source Server         : BPM数据库
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : *************:3306
 Source Schema         : logcenter

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 02/04/2025 13:51:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activityuserfilllogs
-- ----------------------------
DROP TABLE IF EXISTS `activityuserfilllogs`;
CREATE TABLE `activityuserfilllogs`  (
  `ActivityUserFillLogId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'Id主键',
  `InstanceNumber` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '实例编号',
  `NodeId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '节点ID',
  `ActivityId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '活动Id',
  `ActivityName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `ResolverId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '节点策略ID',
  `ResolverName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '节点策略名称',
  `UserId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户id',
  `UserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '用户名称',
  `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`ActivityUserFillLogId`) USING BTREE,
  INDEX `idx_activityuserfilllogs_InstanceNumber`(`InstanceNumber` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '流程节点抓人日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eventlogs
-- ----------------------------
DROP TABLE IF EXISTS `eventlogs`;
CREATE TABLE `eventlogs`  (
  `ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键ID',
  `EventName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '事件名',
  `EventBegin` datetime(3) NULL DEFAULT NULL COMMENT '开始执行时间',
  `EventEnd` datetime(3) NULL DEFAULT NULL COMMENT '结束执行时间',
  `InstanceNum` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '流程号',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_eventlogs_eventlogs`(`InstanceNum` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '引擎事件执行日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for logs
-- ----------------------------
DROP TABLE IF EXISTS `logs`;
CREATE TABLE `logs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `MachineName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `Logged` datetime(3) NOT NULL,
  `Level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Message` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Logger` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `Callsite` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  `Exception` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL,
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `idx_logs_Logged`(`Logged` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2017107 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '系统日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for operationlog
-- ----------------------------
DROP TABLE IF EXISTS `operationlog`;
CREATE TABLE `operationlog`  (
  `LogId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `BigModuleName` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '大模块名称',
  `SmallModuleName` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '小模块名称',
  `KeyValue1` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '查询关键字1',
  `KeyValue2` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '查询关键字2',
  `DataContent` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '数据内容',
  `CreateUserId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建者Id',
  `CreateUserName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建者名称',
  `CreateTime` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `CreateUserLoginId` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建者登录账号',
  PRIMARY KEY (`LogId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for publishjoblogs
-- ----------------------------
DROP TABLE IF EXISTS `publishjoblogs`;
CREATE TABLE `publishjoblogs`  (
  `PublishJobLogId` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '主键Id',
  `JobCode` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'Job代码',
  `JobName` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT 'Job名称',
  `JobData` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT 'Job数据',
  `ExMsg` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '异常信息',
  `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`PublishJobLogId`) USING BTREE,
  INDEX `idx_publishjoblogs_JobCode`(`JobCode` ASC) USING BTREE,
  INDEX `idx_publishjoblogs_JobName`(`JobName` ASC) USING BTREE,
  INDEX `idx_publishjoblogs_CreateTime`(`CreateTime` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
