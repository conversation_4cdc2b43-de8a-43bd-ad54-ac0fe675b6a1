# SQL数据库批量创建和导入工具

这个工具可以根据SQL文件名自动创建对应的数据库，并将SQL内容导入到相应的数据库中。

## 功能特性

- 🗄️ **自动数据库创建**: 根据SQL文件名自动创建对应的数据库
- 📁 **批量处理**: 支持通配符模式批量处理多个SQL文件
- 🔄 **智能命名**: 自动将文件名转换为有效的数据库名
- 📝 **详细日志**: 完整的执行日志和错误报告
- ⚙️ **灵活配置**: 支持配置文件和命令行参数
- 🛡️ **错误处理**: 可选择遇到错误时继续或停止执行
- 📊 **执行报告**: 详细的执行结果统计

## 文件名到数据库名的映射规则

工具会自动将SQL文件名转换为有效的数据库名：

- 移除 `.sql` 扩展名
- 将特殊字符（如 `-`、空格等）替换为下划线 `_`
- 确保数据库名以字母或下划线开头
- 限制长度不超过60个字符

### 示例映射

```
boost.sql                    -> boost
bpm-engine.sql              -> bpm_engine
bpm-enginecache.sql         -> bpm_enginecache
identitycenter_dev.sql      -> identitycenter_dev
interface-center.sql        -> interface_center
```

## 快速开始

### 1. 查看文件映射

首先查看您的SQL文件将会创建哪些数据库：

```bash
python sql_database_creator.py --list-only
```

### 2. 配置数据库连接

确保您的 `db_config.ini` 文件包含正确的数据库连接信息：

```ini
[database]
type = mysql
host = 127.0.0.1
port = 48410
database = 
username = root
password = 5#fa=zk03s`68Je|
charset = utf8mb4
```

### 3. 执行批量创建和导入

```bash
# 处理当前目录下所有.sql文件
python sql_database_creator.py

# 处理指定文件
python sql_database_creator.py boost.sql bpm-engine.sql

# 使用通配符
python sql_database_creator.py bpm-*.sql
```

## 使用方法

### 基本用法

```bash
# 处理所有SQL文件
python sql_database_creator.py

# 处理指定文件
python sql_database_creator.py boost.sql identitycenter_dev.sql

# 使用通配符处理特定模式的文件
python sql_database_creator.py bpm-*.sql
```

### 高级选项

```bash
# 如果数据库已存在则删除后重新创建
python sql_database_creator.py --drop-existing *.sql

# 遇到错误时停止执行
python sql_database_creator.py --stop-on-error *.sql

# 设置详细日志
python sql_database_creator.py --log-level DEBUG *.sql

# 只查看文件和数据库名映射，不执行操作
python sql_database_creator.py --list-only *.sql

# 使用命令行参数指定数据库连接
python sql_database_creator.py --db-type mysql --host localhost --username root --password yourpass *.sql
```

### 命令行参数

- `files`: 要处理的SQL文件路径或通配符模式 (默认: *.sql)
- `-c, --config`: 数据库配置文件路径 (默认: db_config.ini)
- `--drop-existing`: 如果数据库已存在则删除后重新创建
- `--continue-on-error`: 遇到错误时继续执行 (默认)
- `--stop-on-error`: 遇到错误时停止执行
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `--list-only`: 只列出文件和数据库名映射，不执行创建和导入
- `--db-type`: 数据库类型 (mysql, postgresql)
- `--host`: 数据库主机
- `--port`: 数据库端口
- `--username`: 用户名
- `--password`: 密码

## 输出示例

### 文件映射列表

```bash
$ python sql_database_creator.py --list-only

SQL文件和对应数据库名映射:
============================================================
boost.sql                     -> boost
bpm-engine.sql                -> bpm_engine
bpm-enginecache.sql           -> bpm_enginecache
bpm-form.sql                  -> bpm_form
identitycenter_dev.sql        -> identitycenter_dev
interfacecenter_dev.sql       -> interfacecenter_dev

总计: 15 个文件
```

### 执行结果

```bash
$ python sql_database_creator.py

开始批量处理SQL文件...
数据库类型: mysql
服务器地址: 127.0.0.1:48410
删除已存在数据库: 否
遇到错误时继续: 是
--------------------------------------------------
2024-01-15 10:30:15 - INFO - 成功连接到mysql服务器
2024-01-15 10:30:15 - INFO - 找到 15 个SQL文件，开始批量处理
2024-01-15 10:30:15 - INFO - 处理文件: boost.sql -> 数据库: boost
2024-01-15 10:30:15 - INFO - 成功创建数据库: boost
2024-01-15 10:30:16 - INFO - ✓ 成功处理: boost.sql -> boost
...

======================================================================
SQL数据库批量创建和导入结果汇总
======================================================================
总文件数: 15
成功处理文件数: 14
失败文件数: 1
成功创建数据库数: 14
创建失败数据库数: 1
总SQL语句数: 1250
成功执行: 1240
执行失败: 10
成功率: 99.20%

成功创建的数据库 (14):
--------------------------------------------------
  ✓ boost
  ✓ bpm_engine
  ✓ bpm_enginecache
  ✓ bpm_form
  ...

详细处理结果:
----------------------------------------------------------------------
✓ boost.sql -> boost (25/25)
✓ bpm-engine.sql -> bpm_engine (150/150)
✗ bpm-form.sql -> bpm_form (80/85)
    错误: 语句 82: Table 'form_data' already exists
...
```

## 注意事项

1. **权限要求**: 确保数据库用户有创建数据库的权限
2. **备份数据**: 在执行前请备份重要数据，特别是使用 `--drop-existing` 选项时
3. **文件编码**: 工具会自动尝试多种编码格式读取SQL文件
4. **数据库命名**: 文件名会被转换为有效的数据库名，请检查映射结果
5. **事务处理**: 每个文件的导入作为一个独立事务

## 故障排除

### 常见错误

1. **权限不足**: 确保数据库用户有 CREATE DATABASE 权限
2. **连接失败**: 检查数据库服务是否运行，连接信息是否正确
3. **数据库已存在**: 使用 `--drop-existing` 选项或手动删除已存在的数据库
4. **文件读取失败**: 检查文件是否存在，编码是否正确

### 调试技巧

```bash
# 启用详细日志
python sql_database_creator.py --log-level DEBUG *.sql

# 先查看映射关系
python sql_database_creator.py --list-only *.sql

# 测试单个文件
python sql_database_creator.py single_file.sql

# 遇到错误立即停止
python sql_database_creator.py --stop-on-error *.sql
```

## 与原导入工具的区别

| 功能 | 原导入工具 | 数据库创建工具 |
|------|------------|----------------|
| 数据库创建 | 需要手动创建 | 自动创建 |
| 文件处理 | 导入到指定数据库 | 每个文件创建对应数据库 |
| 适用场景 | 单数据库多文件 | 多数据库多文件 |
| 命名规则 | 无 | 文件名转数据库名 |
