#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL文件分割器
将大的SQL文件分割成小文件，然后分别处理
这样可以避免连接超时和内存问题
"""

import os
import sys
import re


def split_large_sql_file(input_file, output_dir=None, max_statements=100):
    """
    将大的SQL文件分割成多个小文件
    
    Args:
        input_file: 输入的SQL文件路径
        output_dir: 输出目录，如果为None则使用文件名作为目录
        max_statements: 每个文件最大语句数
    """
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return False
    
    # 创建输出目录
    if output_dir is None:
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_dir = f"{base_name}_split"
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
    print(f"开始分割文件: {input_file} ({file_size_mb:.1f}MB)")
    
    try:
        # 尝试不同编码读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                print(f"尝试使用 {encoding} 编码...")
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                print(f"✓ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("✗ 无法读取文件，所有编码都失败")
            return False
        
        print(f"文件内容大小: {len(content) / (1024*1024):.1f}MB")
        
        # 分割SQL语句
        print("开始分割SQL语句...")
        statements = split_sql_statements(content)
        print(f"找到 {len(statements)} 个SQL语句")
        
        if not statements:
            print("没有找到有效的SQL语句")
            return False
        
        # 分组写入文件
        file_count = 0
        current_statements = []
        
        for i, statement in enumerate(statements):
            current_statements.append(statement)
            
            # 达到最大语句数或是最后一个语句
            if len(current_statements) >= max_statements or i == len(statements) - 1:
                file_count += 1
                output_file = os.path.join(output_dir, f"part_{file_count:03d}.sql")
                
                with open(output_file, 'w', encoding=used_encoding) as f:
                    for stmt in current_statements:
                        f.write(stmt + ';\n\n')
                
                print(f"创建文件: {output_file} ({len(current_statements)} 个语句)")
                current_statements = []
        
        print(f"\n✓ 分割完成!")
        print(f"总共创建了 {file_count} 个文件")
        print(f"输出目录: {output_dir}")
        
        # 创建处理脚本
        create_processing_script(output_dir, input_file)
        
        return True
        
    except Exception as e:
        print(f"分割文件时出错: {e}")
        return False


def split_sql_statements(content):
    """分割SQL语句"""
    statements = []
    current_statement = ""
    in_string = False
    string_char = None
    
    i = 0
    while i < len(content):
        char = content[i]
        
        if not in_string:
            if char in ("'", '"'):
                in_string = True
                string_char = char
            elif char == ';':
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
                i += 1
                continue
        else:
            if char == string_char:
                # 检查是否是转义的引号
                if i + 1 < len(content) and content[i + 1] == string_char:
                    current_statement += char + content[i + 1]
                    i += 2
                    continue
                else:
                    in_string = False
                    string_char = None
        
        current_statement += char
        i += 1
    
    # 添加最后一个语句
    if current_statement.strip():
        statements.append(current_statement.strip())
    
    return statements


def create_processing_script(output_dir, original_file):
    """创建处理脚本"""
    base_name = os.path.splitext(os.path.basename(original_file))[0]
    script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的处理脚本
用于批量导入分割后的SQL文件到数据库: {base_name}
"""

import os
import sys
import glob
sys.path.append('..')
from sql_database_creator import SQLDatabaseCreator, DatabaseConfig, load_config_from_file

def main():
    print("开始处理分割后的SQL文件...")
    
    # 加载配置
    try:
        config = load_config_from_file('../db_config.ini')
    except Exception as e:
        print(f"加载配置失败: {{e}}")
        return
    
    creator = SQLDatabaseCreator(config, 'INFO')
    
    try:
        # 连接到服务器
        if not creator.connect_to_server():
            print("无法连接到数据库服务器")
            return
        
        # 创建数据库
        database_name = "{base_name}"
        print(f"创建数据库: {{database_name}}")
        if not creator.create_database(database_name, drop_if_exists=True):
            print(f"创建数据库失败: {{database_name}}")
            return
        
        # 连接到数据库
        if not creator.connect_to_database(database_name):
            print(f"连接数据库失败: {{database_name}}")
            return
        
        # 获取所有分割文件
        sql_files = sorted(glob.glob("part_*.sql"))
        print(f"找到 {{len(sql_files)}} 个分割文件")
        
        total_success = 0
        total_failed = 0
        
        for i, sql_file in enumerate(sql_files, 1):
            print(f"\\n处理文件 {{i}}/{{len(sql_files)}}: {{sql_file}}")
            
            try:
                # 读取文件
                with open(sql_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 分割语句
                statements = [s.strip() for s in content.split(';') if s.strip()]
                
                # 执行语句
                success_count = 0
                for j, statement in enumerate(statements, 1):
                    try:
                        creator.cursor.execute(statement)
                        success_count += 1
                        if j % 10 == 0:
                            print(f"  执行进度: {{j}}/{{len(statements)}}")
                    except Exception as e:
                        print(f"  语句执行失败: {{str(e)[:50]}}...")
                
                # 提交事务
                creator.connection.commit()
                total_success += success_count
                print(f"  ✓ 文件处理完成: {{success_count}}/{{len(statements)}} 个语句成功")
                
            except Exception as e:
                print(f"  ✗ 文件处理失败: {{e}}")
                total_failed += 1
        
        print(f"\\n处理完成!")
        print(f"总成功语句数: {{total_success}}")
        print(f"失败文件数: {{total_failed}}")
        
    finally:
        creator.disconnect()

if __name__ == '__main__':
    main()
'''
    
    script_path = os.path.join(output_dir, 'process_split_files.py')
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"创建处理脚本: {script_path}")


def main():
    """主函数"""
    print("SQL文件分割器")
    print("=" * 50)
    
    # 显示大文件
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql')]
    large_files = []
    
    print("\\n大文件列表 (>10MB):")
    for file in sql_files:
        size_mb = os.path.getsize(file) / (1024 * 1024)
        if size_mb > 10:
            large_files.append((file, size_mb))
    
    large_files.sort(key=lambda x: x[1], reverse=True)
    
    for i, (file, size_mb) in enumerate(large_files, 1):
        print(f"{i:2d}. {file} ({size_mb:.1f}MB)")
    
    if not large_files:
        print("没有找到大文件")
        return
    
    # 选择文件
    try:
        choice = int(input(f"\\n请选择要分割的文件 (1-{len(large_files)}): ")) - 1
        if 0 <= choice < len(large_files):
            selected_file, size_mb = large_files[choice]
        else:
            print("无效选择")
            return
    except ValueError:
        print("请输入数字")
        return
    
    print(f"\\n选择的文件: {selected_file} ({size_mb:.1f}MB)")
    
    # 选择分割大小
    if size_mb > 1000:
        suggested_statements = 50
    elif size_mb > 100:
        suggested_statements = 100
    else:
        suggested_statements = 200
    
    print(f"\\n建议每个文件包含语句数: {suggested_statements}")
    statements_input = input("请输入每个文件的语句数 (直接回车使用建议值): ").strip()
    max_statements = int(statements_input) if statements_input else suggested_statements
    
    # 开始分割
    print(f"\\n开始分割文件，每个文件最多 {max_statements} 个语句...")
    success = split_large_sql_file(selected_file, max_statements=max_statements)
    
    if success:
        base_name = os.path.splitext(selected_file)[0]
        output_dir = f"{base_name}_split"
        print(f"\\n🎉 分割成功!")
        print(f"\\n下一步:")
        print(f"1. 进入目录: cd {output_dir}")
        print(f"2. 运行处理脚本: python process_split_files.py")
    else:
        print("\\n❌ 分割失败!")


if __name__ == '__main__':
    main()
