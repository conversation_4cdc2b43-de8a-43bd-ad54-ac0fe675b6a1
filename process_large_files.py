#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大文件SQL处理脚本
专门用于处理大的SQL文件，提供更好的进度显示和错误处理
"""

import os
import sys
import time
from sql_database_creator import SQLDatabaseCreator, DatabaseConfig, load_config_from_file


def analyze_sql_files():
    """分析SQL文件大小"""
    print("分析SQL文件大小...")
    print("=" * 60)
    
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql')]
    sql_files.sort()
    
    file_info = []
    for sql_file in sql_files:
        try:
            # 获取文件大小
            size_bytes = os.path.getsize(sql_file)
            size_mb = size_bytes / (1024 * 1024)
            
            # 获取行数
            with open(sql_file, 'r', encoding='utf-8', errors='ignore') as f:
                line_count = sum(1 for _ in f)
            
            file_info.append({
                'file': sql_file,
                'size_mb': size_mb,
                'lines': line_count
            })
            
        except Exception as e:
            print(f"分析文件 {sql_file} 时出错: {e}")
    
    # 按大小排序
    file_info.sort(key=lambda x: x['size_mb'], reverse=True)
    
    print(f"{'文件名':<30} {'大小(MB)':<10} {'行数':<10} {'类型'}")
    print("-" * 60)
    
    small_files = []
    medium_files = []
    large_files = []
    
    for info in file_info:
        size_str = f"{info['size_mb']:.1f}"
        lines_str = f"{info['lines']}"
        
        if info['size_mb'] < 1:
            file_type = "小文件"
            small_files.append(info['file'])
        elif info['size_mb'] < 10:
            file_type = "中等文件"
            medium_files.append(info['file'])
        else:
            file_type = "大文件"
            large_files.append(info['file'])
        
        print(f"{info['file']:<30} {size_str:<10} {lines_str:<10} {file_type}")
    
    print(f"\n统计:")
    print(f"小文件 (<1MB): {len(small_files)} 个")
    print(f"中等文件 (1-10MB): {len(medium_files)} 个")
    print(f"大文件 (>10MB): {len(large_files)} 个")
    
    return small_files, medium_files, large_files


def process_files_by_size():
    """按文件大小分批处理"""
    small_files, medium_files, large_files = analyze_sql_files()
    
    # 加载配置
    try:
        config = load_config_from_file('db_config.ini')
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    creator = SQLDatabaseCreator(config, 'INFO')
    
    try:
        print(f"\n开始处理...")
        
        # 1. 先处理小文件
        if small_files:
            print(f"\n第1步: 处理小文件 ({len(small_files)} 个)")
            print("-" * 40)
            results = creator.process_sql_files(
                small_files,
                drop_existing=True,
                continue_on_error=True,
                batch_size=50
            )
            print(f"小文件处理完成: {results['successful_files']}/{results['total_files']}")
        
        # 2. 处理中等文件
        if medium_files:
            print(f"\n第2步: 处理中等文件 ({len(medium_files)} 个)")
            print("-" * 40)
            results = creator.process_sql_files(
                medium_files,
                drop_existing=True,
                continue_on_error=True,
                batch_size=20
            )
            print(f"中等文件处理完成: {results['successful_files']}/{results['total_files']}")
        
        # 3. 处理大文件
        if large_files:
            print(f"\n第3步: 处理大文件 ({len(large_files)} 个)")
            print("-" * 40)
            print("注意: 大文件处理可能需要较长时间，请耐心等待...")
            
            for large_file in large_files:
                print(f"\n正在处理大文件: {large_file}")
                start_time = time.time()
                
                results = creator.process_sql_files(
                    [large_file],
                    drop_existing=True,
                    continue_on_error=True,
                    batch_size=10  # 更小的批次
                )
                
                elapsed_time = time.time() - start_time
                print(f"文件 {large_file} 处理完成，耗时: {elapsed_time:.1f}秒")
                
                if results['successful_files'] == 0:
                    print(f"警告: 文件 {large_file} 处理失败")
                    choice = input("是否继续处理下一个大文件? (y/n): ")
                    if choice.lower() != 'y':
                        break
        
        print(f"\n所有文件处理完成!")
        
    finally:
        creator.disconnect()


def process_specific_files():
    """处理指定的文件"""
    print("当前目录的SQL文件:")
    sql_files = [f for f in os.listdir('.') if f.endswith('.sql')]
    sql_files.sort()
    
    for i, file in enumerate(sql_files, 1):
        size_mb = os.path.getsize(file) / (1024 * 1024)
        print(f"{i:2d}. {file} ({size_mb:.1f}MB)")
    
    print("\n请选择要处理的文件 (输入数字，用逗号分隔，如: 1,3,5):")
    try:
        choice = input("选择: ").strip()
        if not choice:
            return
        
        indices = [int(x.strip()) - 1 for x in choice.split(',')]
        selected_files = [sql_files[i] for i in indices if 0 <= i < len(sql_files)]
        
        if not selected_files:
            print("没有选择有效的文件")
            return
        
        print(f"\n将处理以下文件:")
        for file in selected_files:
            print(f"  - {file}")
        
        confirm = input("\n确认处理? (y/n): ")
        if confirm.lower() != 'y':
            return
        
        # 加载配置并处理
        config = load_config_from_file('db_config.ini')
        creator = SQLDatabaseCreator(config, 'INFO')
        
        try:
            results = creator.process_sql_files(
                selected_files,
                drop_existing=True,
                continue_on_error=True,
                batch_size=20
            )
            
            print(f"\n处理完成: {results['successful_files']}/{results['total_files']}")
            
        finally:
            creator.disconnect()
            
    except Exception as e:
        print(f"处理出错: {e}")


def main():
    """主函数"""
    print("SQL大文件处理工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 分析文件大小")
        print("2. 按大小分批处理所有文件")
        print("3. 选择特定文件处理")
        print("4. 退出")
        
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                analyze_sql_files()
            elif choice == '2':
                process_files_by_size()
            elif choice == '3':
                process_specific_files()
            elif choice == '4':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
        
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n操作出错: {e}")


if __name__ == '__main__':
    main()
